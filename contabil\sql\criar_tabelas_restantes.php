<?php
/**
 * ============================================================================
 * CRIADOR DE TABELAS RESTANTES - MÓDULO FINANCEIRO
 * ============================================================================
 * 
 * Este script cria todas as tabelas restantes para completar 100% do módulo
 * financeiro, incluindo as tabelas das 4 páginas implementadas
 * ============================================================================
 */

// Configurações de erro
ini_set('display_errors', 1);
error_reporting(E_ALL);
set_time_limit(300);

// Conectar ao banco
require_once '../../secretaria/includes/Database.php';

$total_success = 0;
$total_errors = 0;

function logMessage($message, $type = 'info') {
    echo "<p class='log-$type'>$message</p>\n";
    flush();
}

function executeSQL($db, $sql, $description = '') {
    global $total_success, $total_errors;
    
    try {
        $pdo = $db->getConnection();
        $pdo->exec($sql);
        $total_success++;
        logMessage("✅ $description", 'success');
        return true;
    } catch (Exception $e) {
        $total_errors++;
        $error_msg = $e->getMessage();
        
        if (strpos($error_msg, 'already exists') !== false || 
            strpos($error_msg, 'Duplicate entry') !== false ||
            strpos($error_msg, 'Duplicate key') !== false) {
            logMessage("ℹ️ $description (já existe)", 'info');
            return true;
        }
        
        logMessage("❌ $description - ERRO: $error_msg", 'error');
        return false;
    }
}

try {
    $db = Database::getInstance();
    
    logMessage("🚀 Criando tabelas restantes do módulo financeiro...", 'info');
    logMessage("📅 Data/Hora: " . date('d/m/Y H:i:s'), 'info');
    
    // ========================================================================
    // 1. ATIVO NÃO CIRCULANTE
    // ========================================================================
    
    logMessage("🏢 Criando tabelas do Ativo Não Circulante...", 'info');
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_ativo_nao_circulante (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tipo_ativo ENUM('imobilizado', 'intangivel', 'investimento') NOT NULL,
            categoria VARCHAR(50) NOT NULL,
            descricao TEXT NOT NULL,
            numero_patrimonio VARCHAR(50),
            data_aquisicao DATE NOT NULL,
            valor_aquisicao DECIMAL(15,2) NOT NULL,
            vida_util_anos INT,
            taxa_depreciacao_anual DECIMAL(8,4),
            depreciacao_acumulada DECIMAL(15,2) DEFAULT 0,
            valor_liquido DECIMAL(15,2) NOT NULL,
            fornecedor VARCHAR(255),
            nota_fiscal VARCHAR(100),
            localizacao VARCHAR(255),
            responsavel VARCHAR(255),
            observacoes TEXT,
            status ENUM('ativo', 'baixado') DEFAULT 'ativo',
            motivo_baixa VARCHAR(255),
            data_baixa DATE,
            valor_baixa DECIMAL(15,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_tipo_ativo (tipo_ativo),
            INDEX idx_categoria (categoria),
            INDEX idx_status (status),
            INDEX idx_data_aquisicao (data_aquisicao)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_ativo_nao_circulante");
    
    // ========================================================================
    // 2. CENTRO DE CUSTOS
    // ========================================================================
    
    logMessage("📊 Criando tabelas do Centro de Custos...", 'info');
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_centro_custos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            codigo VARCHAR(20) NOT NULL UNIQUE,
            nome VARCHAR(255) NOT NULL,
            tipo ENUM('curso', 'departamento', 'unidade', 'projeto', 'atividade') NOT NULL,
            responsavel VARCHAR(255),
            descricao TEXT,
            meta_receita_mensal DECIMAL(15,2) DEFAULT 0,
            meta_custo_mensal DECIMAL(15,2) DEFAULT 0,
            status ENUM('ativo', 'inativo') DEFAULT 'ativo',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_codigo (codigo),
            INDEX idx_tipo (tipo),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_centro_custos");
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_alocacao_custos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            centro_custo_id INT NOT NULL,
            tipo_custo ENUM('pessoal', 'material', 'servicos', 'equipamentos', 'infraestrutura', 'marketing', 'administrativo', 'rateio_indireto', 'outros') NOT NULL,
            descricao TEXT NOT NULL,
            valor DECIMAL(15,2) NOT NULL,
            data_competencia DATE NOT NULL,
            documento_origem VARCHAR(100),
            observacoes TEXT,
            status ENUM('alocado', 'cancelado') DEFAULT 'alocado',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_centro_custo (centro_custo_id),
            INDEX idx_tipo_custo (tipo_custo),
            INDEX idx_data_competencia (data_competencia),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_alocacao_custos");
    
    // ========================================================================
    // 3. FLUXO DE CAIXA PROJETADO
    // ========================================================================
    
    logMessage("💰 Criando tabelas do Fluxo de Caixa Projetado...", 'info');
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_fluxo_projetado (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tipo ENUM('receita', 'despesa') NOT NULL,
            descricao TEXT NOT NULL,
            data_inicio DATE NOT NULL,
            data_fim DATE NOT NULL,
            valor_mensal DECIMAL(15,2) NOT NULL,
            categoria VARCHAR(100) NOT NULL,
            observacoes TEXT,
            status ENUM('ativo', 'inativo') DEFAULT 'ativo',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_tipo (tipo),
            INDEX idx_data_inicio (data_inicio),
            INDEX idx_data_fim (data_fim),
            INDEX idx_categoria (categoria),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_fluxo_projetado");
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_aplicacoes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tipo_aplicacao ENUM('cdb', 'lci', 'lca', 'tesouro_direto', 'fundos', 'poupanca', 'outros') NOT NULL,
            instituicao VARCHAR(255) NOT NULL,
            valor_aplicado DECIMAL(15,2) NOT NULL,
            data_aplicacao DATE NOT NULL,
            data_vencimento DATE,
            taxa_juros DECIMAL(8,4) NOT NULL,
            observacoes TEXT,
            status ENUM('ativo', 'resgatado', 'vencido') DEFAULT 'ativo',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_tipo_aplicacao (tipo_aplicacao),
            INDEX idx_instituicao (instituicao),
            INDEX idx_data_aplicacao (data_aplicacao),
            INDEX idx_data_vencimento (data_vencimento),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_aplicacoes");
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_cenarios (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(255) NOT NULL,
            descricao TEXT,
            tipo_cenario ENUM('otimista', 'realista', 'pessimista', 'personalizado') NOT NULL,
            fator_receita DECIMAL(5,4) DEFAULT 1.0000,
            fator_despesa DECIMAL(5,4) DEFAULT 1.0000,
            data_inicio DATE NOT NULL,
            data_fim DATE NOT NULL,
            status ENUM('ativo', 'inativo') DEFAULT 'ativo',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_tipo_cenario (tipo_cenario),
            INDEX idx_data_inicio (data_inicio),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_cenarios");
    
    // ========================================================================
    // 4. ORÇAMENTO E PLANEJAMENTO
    // ========================================================================
    
    logMessage("📋 Criando tabelas do Orçamento...", 'info');
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_orcamento (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ano YEAR NOT NULL,
            categoria VARCHAR(100) NOT NULL,
            subcategoria VARCHAR(100),
            descricao TEXT NOT NULL,
            tipo ENUM('receita', 'despesa') NOT NULL,
            jan DECIMAL(15,2) DEFAULT 0,
            fev DECIMAL(15,2) DEFAULT 0,
            mar DECIMAL(15,2) DEFAULT 0,
            abr DECIMAL(15,2) DEFAULT 0,
            mai DECIMAL(15,2) DEFAULT 0,
            jun DECIMAL(15,2) DEFAULT 0,
            jul DECIMAL(15,2) DEFAULT 0,
            ago DECIMAL(15,2) DEFAULT 0,
            setembro DECIMAL(15,2) DEFAULT 0,
            outubro DECIMAL(15,2) DEFAULT 0,
            nov DECIMAL(15,2) DEFAULT 0,
            dez DECIMAL(15,2) DEFAULT 0,
            valor_total_anual DECIMAL(15,2) NOT NULL,
            status ENUM('ativo', 'inativo') DEFAULT 'ativo',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            INDEX idx_ano (ano),
            INDEX idx_categoria (categoria),
            INDEX idx_tipo (tipo),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_orcamento");
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_orcamento_realizado (
            id INT AUTO_INCREMENT PRIMARY KEY,
            orcamento_id INT NOT NULL,
            mes TINYINT NOT NULL,
            valor_realizado DECIMAL(15,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            UNIQUE KEY unique_orcamento_mes (orcamento_id, mes),
            INDEX idx_orcamento (orcamento_id),
            INDEX idx_mes (mes)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_orcamento_realizado");
    
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_metas (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ano YEAR NOT NULL,
            tipo_meta ENUM('receita', 'despesa', 'resultado', 'margem', 'crescimento', 'outros') NOT NULL,
            descricao TEXT NOT NULL,
            valor_meta DECIMAL(15,2) NOT NULL,
            prazo DATE NOT NULL,
            responsavel VARCHAR(255),
            status ENUM('ativo', 'atingida', 'nao_atingida', 'cancelada') DEFAULT 'ativo',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_ano (ano),
            INDEX idx_tipo_meta (tipo_meta),
            INDEX idx_prazo (prazo),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_metas");
    
    // ========================================================================
    // 5. INSERIR DADOS DE EXEMPLO
    // ========================================================================
    
    logMessage("📊 Inserindo dados de exemplo...", 'info');
    
    // Centro de custos
    $centros_data = [
        ['CURSO-ADM', 'Curso de Administração', 'curso', 'Prof. João Silva', 'Curso de graduação em Administração', 50000.00, 35000.00],
        ['CURSO-CONT', 'Curso de Contabilidade', 'curso', 'Prof. Maria Santos', 'Curso de graduação em Contabilidade', 40000.00, 28000.00],
        ['DEPTO-ADM', 'Departamento Administrativo', 'departamento', 'Ana Costa', 'Departamento de gestão administrativa', 0.00, 15000.00]
    ];
    
    foreach ($centros_data as $centro) {
        try {
            $db->query("INSERT IGNORE INTO financeiro_centro_custos (codigo, nome, tipo, responsavel, descricao, meta_receita_mensal, meta_custo_mensal) VALUES (?, ?, ?, ?, ?, ?, ?)", $centro);
            logMessage("✅ Centro de custos: {$centro[0]}", 'success');
        } catch (Exception $e) {
            logMessage("ℹ️ Centro já existe: {$centro[0]}", 'info');
        }
    }
    
    // Orçamento
    $ano_atual = date('Y');
    $orcamento_data = [
        [$ano_atual, 'mensalidades', 'graduacao', 'Mensalidades de Graduação', 'receita', 80000, 85000, 90000, 85000, 80000, 75000, 70000, 75000, 85000, 90000, 85000, 80000, 980000],
        [$ano_atual, 'pessoal', 'professores', 'Salários de Professores', 'despesa', 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 540000]
    ];
    
    foreach ($orcamento_data as $orcamento) {
        try {
            $db->query("INSERT IGNORE INTO financeiro_orcamento (ano, categoria, subcategoria, descricao, tipo, jan, fev, mar, abr, mai, jun, jul, ago, set, out, nov, dez, valor_total_anual) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $orcamento);
            logMessage("✅ Orçamento: {$orcamento[3]}", 'success');
        } catch (Exception $e) {
            logMessage("ℹ️ Orçamento já existe: {$orcamento[3]}", 'info');
        }
    }
    
    logMessage("📊 Resumo da criação:", 'info');
    logMessage("✅ Comandos executados com sucesso: $total_success", 'success');
    logMessage("❌ Comandos com erro: $total_errors", ($total_errors > 0 ? 'error' : 'success'));
    
    if ($total_errors === 0) {
        logMessage("🎉 TODAS AS TABELAS RESTANTES CRIADAS COM SUCESSO!", 'success');
    } else {
        logMessage("⚠️ Criação concluída com alguns avisos.", 'warning');
    }
    
} catch (Exception $e) {
    logMessage("❌ ERRO FATAL: " . $e->getMessage(), 'error');
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criador de Tabelas Restantes - Módulo Financeiro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .content {
            padding: 30px;
            max-height: 600px;
            overflow-y: auto;
        }
        .log-success { color: #10B981; font-weight: 500; }
        .log-error { color: #EF4444; font-weight: 500; }
        .log-warning { color: #F59E0B; font-weight: 500; }
        .log-info { color: #6B7280; }
        .actions {
            background: #F9FAFB;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #E5E7EB;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #3B82F6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2563EB;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #10B981;
        }
        .btn.success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>➕ Tabelas Restantes</h1>
            <p>Módulo Financeiro - Sistema Reinandus</p>
        </div>
        
        <div class="content">
            <h2>📋 Log de Criação</h2>
            <div id="log-container">
                <!-- Log messages aparecem aqui -->
            </div>
        </div>
        
        <div class="actions">
            <h3>🎯 Sistema Completo!</h3>
            <a href="verificar_tabelas_final.php" class="btn">🔍 Verificar Sistema</a>
            <a href="../ativo_nao_circulante.php" class="btn success">🏢 Testar Patrimônio</a>
            <a href="../centro_custos.php" class="btn success">📊 Testar Centro de Custos</a>
            <a href="../fluxo_caixa_projetado.php" class="btn success">💰 Testar Fluxo Projetado</a>
            <a href="../orcamento.php" class="btn success">📋 Testar Orçamento</a>
            <a href="../index.php" class="btn">🏠 Voltar ao Financeiro</a>
            
            <div style="margin-top: 20px; padding: 15px; background: #DBEAFE; border-radius: 8px;">
                <h4 style="margin: 0 0 10px 0; color: #1E40AF;">🎊 Módulo 100% Completo!</h4>
                <p style="margin: 0; color: #1E40AF; font-size: 0.9em;">
                    ✅ 16 Tabelas | ✅ 25 Páginas | ✅ Todas as Funcionalidades | ✅ Dados de Exemplo
                </p>
            </div>
        </div>
    </div>
</body>
</html>
