<?php
/**
 * Listagem de ativos não circulantes
 */

// Filtros
$filtro_tipo = $_GET['tipo'] ?? '';
$filtro_categoria = $_GET['categoria'] ?? '';
$filtro_status = $_GET['status'] ?? 'ativo';

$where_clauses = ['1=1'];
$params = [];

if ($filtro_tipo) {
    $where_clauses[] = "tipo_ativo = ?";
    $params[] = $filtro_tipo;
}
if ($filtro_categoria) {
    $where_clauses[] = "categoria = ?";
    $params[] = $filtro_categoria;
}
if ($filtro_status) {
    $where_clauses[] = "status = ?";
    $params[] = $filtro_status;
}

$where_sql = implode(' AND ', $where_clauses);

try {
    $ativos = $db->fetchAll("
        SELECT *
        FROM financeiro_ativo_nao_circulante 
        WHERE $where_sql
        ORDER BY data_aquisicao DESC, created_at DESC
        LIMIT 100
    ", $params) ?: [];
    
    // Resumo dos filtros
    $resumo_filtros = $db->fetchOne("
        SELECT 
            COUNT(*) as total,
            SUM(valor_aquisicao) as valor_aquisicao_total,
            SUM(depreciacao_acumulada) as depreciacao_total,
            SUM(valor_liquido) as valor_liquido_total
        FROM financeiro_ativo_nao_circulante
        WHERE $where_sql
    ", $params) ?: ['total' => 0, 'valor_aquisicao_total' => 0, 'depreciacao_total' => 0, 'valor_liquido_total' => 0];
    
} catch (Exception $e) {
    $ativos = [];
    $resumo_filtros = ['total' => 0, 'valor_aquisicao_total' => 0, 'depreciacao_total' => 0, 'valor_liquido_total' => 0];
}
?>

<!-- Resumo dos Filtros -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-list text-indigo-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Filtrado</p>
                <p class="text-2xl font-semibold text-gray-900"><?php echo $resumo_filtros['total']; ?></p>
                <p class="text-sm text-gray-500">ativos</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-blue-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Valor Aquisição</p>
                <p class="text-2xl font-semibold text-blue-600">
                    R$ <?php echo number_format($resumo_filtros['valor_aquisicao_total'], 2, ',', '.'); ?>
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-chart-line-down text-red-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Depreciação</p>
                <p class="text-2xl font-semibold text-red-600">
                    R$ <?php echo number_format($resumo_filtros['depreciacao_total'], 2, ',', '.'); ?>
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-chart-line text-green-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Valor Líquido</p>
                <p class="text-2xl font-semibold text-green-600">
                    R$ <?php echo number_format($resumo_filtros['valor_liquido_total'], 2, ',', '.'); ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-gray-500 mr-2"></i>
        Filtros
    </h3>
    <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <input type="hidden" name="acao" value="listar">
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tipo de Ativo</label>
            <select name="tipo" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">Todos os tipos</option>
                <option value="imobilizado" <?php echo ($filtro_tipo === 'imobilizado') ? 'selected' : ''; ?>>Imobilizado</option>
                <option value="intangivel" <?php echo ($filtro_tipo === 'intangivel') ? 'selected' : ''; ?>>Intangível</option>
                <option value="investimento" <?php echo ($filtro_tipo === 'investimento') ? 'selected' : ''; ?>>Investimento</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
            <select name="categoria" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">Todas as categorias</option>
                <option value="moveis_utensilios" <?php echo ($filtro_categoria === 'moveis_utensilios') ? 'selected' : ''; ?>>Móveis e Utensílios</option>
                <option value="equipamentos_informatica" <?php echo ($filtro_categoria === 'equipamentos_informatica') ? 'selected' : ''; ?>>Equipamentos de Informática</option>
                <option value="veiculos" <?php echo ($filtro_categoria === 'veiculos') ? 'selected' : ''; ?>>Veículos</option>
                <option value="maquinas_equipamentos" <?php echo ($filtro_categoria === 'maquinas_equipamentos') ? 'selected' : ''; ?>>Máquinas e Equipamentos</option>
                <option value="instalacoes" <?php echo ($filtro_categoria === 'instalacoes') ? 'selected' : ''; ?>>Instalações</option>
                <option value="edificacoes" <?php echo ($filtro_categoria === 'edificacoes') ? 'selected' : ''; ?>>Edificações</option>
                <option value="software" <?php echo ($filtro_categoria === 'software') ? 'selected' : ''; ?>>Software</option>
                <option value="marcas_patentes" <?php echo ($filtro_categoria === 'marcas_patentes') ? 'selected' : ''; ?>>Marcas e Patentes</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select name="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="ativo" <?php echo ($filtro_status === 'ativo') ? 'selected' : ''; ?>>Ativo</option>
                <option value="baixado" <?php echo ($filtro_status === 'baixado') ? 'selected' : ''; ?>>Baixado</option>
                <option value="" <?php echo ($filtro_status === '') ? 'selected' : ''; ?>>Todos</option>
            </select>
        </div>
        
        <div class="flex items-end">
            <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                <i class="fas fa-search mr-2"></i>
                Filtrar
            </button>
        </div>
    </form>
</div>

<!-- Lista de Ativos -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-indigo-500 mr-2"></i>
                Lista de Ativos
            </h3>
            <a href="?acao=novo" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Novo Ativo
            </a>
        </div>
    </div>
    
    <?php if (empty($ativos)): ?>
        <div class="p-8 text-center">
            <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
            <p class="text-gray-500 text-lg">Nenhum ativo encontrado</p>
            <p class="text-gray-400 text-sm">Cadastre o primeiro ativo clicando no botão "Novo Ativo"</p>
        </div>
    <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo/Categoria</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Aquisição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor Aquisição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Depreciação</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor Líquido</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($ativos as $ativo): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($ativo['descricao']); ?></div>
                                <?php if ($ativo['numero_patrimonio']): ?>
                                    <div class="text-sm text-gray-500">Patrimônio: <?php echo htmlspecialchars($ativo['numero_patrimonio']); ?></div>
                                <?php endif; ?>
                                <?php if ($ativo['localizacao']): ?>
                                    <div class="text-sm text-gray-500">Local: <?php echo htmlspecialchars($ativo['localizacao']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo ucfirst($ativo['tipo_ativo']); ?></div>
                                <div class="text-sm text-gray-500"><?php echo ucfirst(str_replace('_', ' ', $ativo['categoria'])); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y', strtotime($ativo['data_aquisicao'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                R$ <?php echo number_format($ativo['valor_aquisicao'], 2, ',', '.'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                R$ <?php echo number_format($ativo['depreciacao_acumulada'], 2, ',', '.'); ?>
                                <?php if ($ativo['tipo_ativo'] !== 'investimento'): ?>
                                    <div class="text-xs text-gray-500">
                                        <?php echo number_format($ativo['taxa_depreciacao_anual'], 2, ',', '.'); ?>% a.a.
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                R$ <?php echo number_format($ativo['valor_liquido'], 2, ',', '.'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $status_colors = [
                                    'ativo' => 'bg-green-100 text-green-800',
                                    'baixado' => 'bg-red-100 text-red-800'
                                ];
                                $status_color = $status_colors[$ativo['status']] ?? 'bg-gray-100 text-gray-800';
                                ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_color; ?>">
                                    <?php echo ucfirst($ativo['status']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <?php if ($ativo['status'] === 'ativo'): ?>
                                        <button onclick="abrirModalBaixa(<?php echo $ativo['id']; ?>, '<?php echo addslashes($ativo['descricao']); ?>')" 
                                                class="text-red-600 hover:text-red-900" title="Baixar Ativo">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button onclick="verDetalhes(<?php echo $ativo['id']; ?>)" 
                                                class="text-blue-600 hover:text-blue-900" title="Ver Detalhes">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php else: ?>
                                        <span class="text-gray-400">-</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Modal de Baixa -->
<div id="modalBaixa" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Baixar Ativo</h3>
                <form id="formBaixa" method="POST">
                    <input type="hidden" name="acao" value="baixar">
                    <input type="hidden" id="ativoId" name="id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ativo</label>
                        <input type="text" id="ativoDescricao" readonly class="w-full border border-gray-300 rounded-lg px-3 py-2 bg-gray-50">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Motivo da Baixa</label>
                        <select name="motivo_baixa" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <option value="">Selecione o motivo</option>
                            <option value="venda">Venda</option>
                            <option value="perda">Perda</option>
                            <option value="roubo">Roubo</option>
                            <option value="obsolescencia">Obsolescência</option>
                            <option value="dano">Dano/Quebra</option>
                            <option value="doacao">Doação</option>
                            <option value="outros">Outros</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Data da Baixa</label>
                        <input type="date" name="data_baixa" required value="<?php echo date('Y-m-d'); ?>" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Valor da Baixa (opcional)</label>
                        <input type="text" name="valor_baixa" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500" placeholder="0,00" onkeyup="formatarMoeda(this)">
                        <p class="text-xs text-gray-500 mt-1">Valor obtido na venda ou valor do seguro</p>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="fecharModalBaixa()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                            Cancelar
                        </button>
                        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                            Confirmar Baixa
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function formatarMoeda(input) {
    let value = input.value.replace(/\D/g, '');
    value = (value / 100).toFixed(2) + '';
    value = value.replace(".", ",");
    value = value.replace(/(\d)(\d{3})(\d{3}),/g, "$1.$2.$3,");
    value = value.replace(/(\d)(\d{3}),/g, "$1.$2,");
    input.value = value;
}

function abrirModalBaixa(id, descricao) {
    document.getElementById('ativoId').value = id;
    document.getElementById('ativoDescricao').value = descricao;
    document.getElementById('modalBaixa').classList.remove('hidden');
}

function fecharModalBaixa() {
    document.getElementById('modalBaixa').classList.add('hidden');
}

function verDetalhes(id) {
    alert('Funcionalidade de detalhes em desenvolvimento. ID: ' + id);
}

// Fechar modal ao clicar fora
document.getElementById('modalBaixa').addEventListener('click', function(e) {
    if (e.target === this) {
        fecharModalBaixa();
    }
});
</script>
