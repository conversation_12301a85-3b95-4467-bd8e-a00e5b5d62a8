<?php
/**
 * Cabeçalho CSS Padrão para Módulo Financeiro
 * Inclui sistema de fallback CSS para garantir funcionamento em produção
 */

// Função para gerar o cabeçalho CSS
function gerarCabecalhoCSS($titulo_pagina = "Módulo Financeiro") {
    ob_start();
?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo htmlspecialchars($titulo_pagina); ?></title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS Específico do Módulo Financeiro (Primeira opção - mais confiável) -->
    <link rel="stylesheet" href="css/financeiro.css" id="financeiro-css">
    
    <!-- CSS Principal - Fallback para compatibilidade local/produção -->
    <link rel="stylesheet" href="../secretaria/css/styles.css" id="secretaria-styles" disabled>
    <link rel="stylesheet" href="../secretaria/css/sidebar.css" id="secretaria-sidebar" disabled>
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css" id="secretaria-layout" disabled>
    
    <!-- CSS da raiz - Segundo fallback -->
    <link rel="stylesheet" href="../css/styles.css" id="root-styles" disabled>
    <link rel="stylesheet" href="../css/sidebar.css" id="root-sidebar" disabled>
    <link rel="stylesheet" href="../css/layout-fixes.css" id="root-layout" disabled>
    
    <script>
        // Sistema inteligente de fallback CSS
        window.addEventListener('load', function() {
            setTimeout(function() {
                const testDiv = document.createElement('div');
                testDiv.className = 'bg-blue-500 p-4';
                testDiv.style.display = 'none';
                document.body.appendChild(testDiv);
                
                const computedStyle = window.getComputedStyle(testDiv);
                const bgColor = computedStyle.backgroundColor;
                const padding = computedStyle.padding;
                
                // Verificar se o CSS do financeiro carregou corretamente
                const cssCarregado = bgColor.includes('59, 130, 246') && padding === '16px';
                
                if (!cssCarregado) {
                    console.warn('CSS do módulo financeiro falhou, tentando fallbacks...');
                    
                    // Tentar CSS da secretaria
                    document.getElementById('secretaria-styles').disabled = false;
                    document.getElementById('secretaria-sidebar').disabled = false;
                    document.getElementById('secretaria-layout').disabled = false;
                    
                    // Se ainda não funcionar, tentar CSS da raiz
                    setTimeout(function() {
                        const testDiv2 = document.createElement('div');
                        testDiv2.className = 'bg-blue-500';
                        testDiv2.style.display = 'none';
                        document.body.appendChild(testDiv2);
                        
                        const computedStyle2 = window.getComputedStyle(testDiv2);
                        const bgColor2 = computedStyle2.backgroundColor;
                        
                        if (!bgColor2.includes('59, 130, 246')) {
                            console.warn('CSS da secretaria também falhou, usando CSS da raiz...');
                            document.getElementById('root-styles').disabled = false;
                            document.getElementById('root-sidebar').disabled = false;
                            document.getElementById('root-layout').disabled = false;
                        }
                        
                        document.body.removeChild(testDiv2);
                    }, 500);
                } else {
                    console.log('CSS do módulo financeiro carregado com sucesso!');
                }
                
                document.body.removeChild(testDiv);
            }, 500);
        });
    </script>
    
    <!-- Estilos inline para garantir funcionamento mínimo -->
    <style>
        /* Fallback CSS básico caso todos os arquivos falhem */
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 0; 
            padding: 0; 
            background: #f8f9fa; 
        }
        .flex { display: flex; }
        .h-screen { height: 100vh; }
        .flex-1 { flex: 1; }
        .flex-col { flex-direction: column; }
        .overflow-hidden { overflow: hidden; }
        .overflow-y-auto { overflow-y: auto; }
        .bg-white { background-color: #ffffff; }
        .bg-gray-100 { background-color: #f3f4f6; }
        .text-gray-800 { color: #1f2937; }
        .p-4 { padding: 1rem; }
        .p-6 { padding: 1.5rem; }
        .m-4 { margin: 1rem; }
        .rounded { border-radius: 0.25rem; }
        .shadow { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
        .border { border: 1px solid #e5e7eb; }
        .main-content { margin-left: 250px; }
        
        /* Sidebar básico */
        .sidebar { 
            width: 250px; 
            background: #1f2937; 
            color: white; 
            position: fixed; 
            height: 100vh; 
            overflow-y: auto; 
        }
        
        /* Responsivo básico */
        @media (max-width: 768px) {
            .main-content { margin-left: 0; }
            .sidebar { transform: translateX(-100%); }
        }
    </style>
<?php
    return ob_get_clean();
}

// Função para incluir o cabeçalho CSS em uma página
function incluirCSSFinanceiro($titulo_pagina = "Módulo Financeiro") {
    echo gerarCabecalhoCSS($titulo_pagina);
}
?>
