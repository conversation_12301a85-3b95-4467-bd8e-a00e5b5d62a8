<?php
/**
 * Formulário para criar novo centro de custos
 */
?>

<div class="max-w-4xl mx-auto">
    <!-- Header do Formulário -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-plus text-orange-500 mr-2"></i>
                    Novo Centro de Custos
                </h2>
                <a href="?acao=dashboard" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar ao Dashboard
                </a>
            </div>
        </div>

        <!-- Formulário -->
        <form method="POST" class="p-6">
            <input type="hidden" name="acao" value="criar_centro">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Código -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Código <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="codigo" required 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                           placeholder="Ex: CC001, CURSO-ADM, DEPTO-TI"
                           pattern="[A-Z0-9\-]+" title="Use apenas letras maiúsculas, números e hífen">
                </div>

                <!-- Nome -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Nome <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="nome" required 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                           placeholder="Nome do centro de custos">
                </div>

                <!-- Tipo -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Tipo <span class="text-red-500">*</span>
                    </label>
                    <select name="tipo" required onchange="atualizarExemplos()"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Selecione o tipo</option>
                        <option value="curso">Curso</option>
                        <option value="departamento">Departamento</option>
                        <option value="unidade">Unidade</option>
                        <option value="projeto">Projeto</option>
                        <option value="atividade">Atividade</option>
                    </select>
                </div>

                <!-- Responsável -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Responsável
                    </label>
                    <input type="text" name="responsavel" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                           placeholder="Nome do responsável pelo centro">
                </div>

                <!-- Meta de Receita Mensal -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Meta de Receita Mensal
                    </label>
                    <div class="relative">
                        <span class="absolute left-3 top-2 text-gray-500">R$</span>
                        <input type="text" name="meta_receita_mensal" 
                               class="w-full border border-gray-300 rounded-lg pl-10 pr-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                               placeholder="0,00" onkeyup="formatarMoeda(this)">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Usado para cálculo de rateio proporcional</p>
                </div>

                <!-- Meta de Custo Mensal -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Meta de Custo Mensal
                    </label>
                    <div class="relative">
                        <span class="absolute left-3 top-2 text-gray-500">R$</span>
                        <input type="text" name="meta_custo_mensal" 
                               class="w-full border border-gray-300 rounded-lg pl-10 pr-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                               placeholder="0,00" onkeyup="formatarMoeda(this)">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Meta de custo para controle orçamentário</p>
                </div>
            </div>

            <!-- Descrição -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Descrição
                </label>
                <textarea name="descricao" rows="3" 
                          class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                          placeholder="Descrição detalhada do centro de custos, suas atividades e objetivos"></textarea>
            </div>

            <!-- Exemplos por Tipo -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg" id="exemplos_tipo">
                <h4 class="text-md font-semibold text-blue-800 mb-2">
                    <i class="fas fa-lightbulb mr-2"></i>
                    Exemplos de Centros de Custos
                </h4>
                <div id="lista_exemplos" class="text-sm text-blue-700">
                    <p>Selecione um tipo para ver exemplos específicos.</p>
                </div>
            </div>

            <!-- Botões -->
            <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                <a href="?acao=dashboard" class="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancelar
                </a>
                <button type="submit" class="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Criar Centro de Custos
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Exemplos por tipo de centro de custos
const exemplos = {
    curso: {
        titulo: "Exemplos de Centros de Custos por Curso",
        lista: [
            "CURSO-ADM: Administração",
            "CURSO-CONT: Contabilidade", 
            "CURSO-DIR: Direito",
            "CURSO-ENG: Engenharia",
            "CURSO-MED: Medicina",
            "CURSO-ENF: Enfermagem",
            "CURSO-PSIC: Psicologia",
            "CURSO-ED-FIS: Educação Física"
        ],
        dica: "Use para controlar custos específicos de cada curso, como material didático, laboratórios e professores."
    },
    departamento: {
        titulo: "Exemplos de Centros de Custos por Departamento",
        lista: [
            "DEPTO-ACAD: Departamento Acadêmico",
            "DEPTO-ADM: Departamento Administrativo",
            "DEPTO-FIN: Departamento Financeiro",
            "DEPTO-RH: Recursos Humanos",
            "DEPTO-TI: Tecnologia da Informação",
            "DEPTO-MKT: Marketing",
            "DEPTO-BIBLIO: Biblioteca",
            "DEPTO-MANUT: Manutenção"
        ],
        dica: "Use para controlar custos administrativos e de apoio por departamento."
    },
    unidade: {
        titulo: "Exemplos de Centros de Custos por Unidade",
        lista: [
            "UNID-CAMPUS1: Campus Principal",
            "UNID-CAMPUS2: Campus Filial",
            "UNID-EAD: Educação a Distância",
            "UNID-POS: Pós-Graduação",
            "UNID-PESQ: Pesquisa e Extensão",
            "UNID-HOSP: Hospital Universitário",
            "UNID-LAB: Laboratórios",
            "UNID-ESPORTE: Centro Esportivo"
        ],
        dica: "Use para controlar custos por unidade física ou organizacional."
    },
    projeto: {
        titulo: "Exemplos de Centros de Custos por Projeto",
        lista: [
            "PROJ-VEST2025: Vestibular 2025",
            "PROJ-REFORM: Reforma do Prédio A",
            "PROJ-SIST: Implementação Sistema",
            "PROJ-CERT: Certificação ISO",
            "PROJ-PESQ01: Projeto de Pesquisa 01",
            "PROJ-EXT01: Projeto de Extensão 01",
            "PROJ-EVENTO: Semana Acadêmica",
            "PROJ-CAPT: Captação de Alunos"
        ],
        dica: "Use para controlar custos de projetos específicos com início e fim definidos."
    },
    atividade: {
        titulo: "Exemplos de Centros de Custos por Atividade",
        lista: [
            "ATIV-ENSINO: Atividades de Ensino",
            "ATIV-PESQ: Atividades de Pesquisa",
            "ATIV-EXT: Atividades de Extensão",
            "ATIV-ADMIN: Atividades Administrativas",
            "ATIV-MANUT: Manutenção Predial",
            "ATIV-LIMP: Limpeza e Conservação",
            "ATIV-SEG: Segurança Patrimonial",
            "ATIV-TRANSP: Transporte"
        ],
        dica: "Use para controlar custos por tipo de atividade realizada."
    }
};

function atualizarExemplos() {
    const tipo = document.querySelector('select[name="tipo"]').value;
    const listaExemplos = document.getElementById('lista_exemplos');
    
    if (tipo && exemplos[tipo]) {
        const exemplo = exemplos[tipo];
        let html = `
            <h5 class="font-semibold mb-2">${exemplo.titulo}</h5>
            <ul class="list-disc list-inside space-y-1 mb-3">
        `;
        
        exemplo.lista.forEach(item => {
            html += `<li>${item}</li>`;
        });
        
        html += `
            </ul>
            <p class="text-xs italic">${exemplo.dica}</p>
        `;
        
        listaExemplos.innerHTML = html;
    } else {
        listaExemplos.innerHTML = '<p>Selecione um tipo para ver exemplos específicos.</p>';
    }
}

function formatarMoeda(input) {
    let value = input.value.replace(/\D/g, '');
    value = (value / 100).toFixed(2) + '';
    value = value.replace(".", ",");
    value = value.replace(/(\d)(\d{3})(\d{3}),/g, "$1.$2.$3,");
    value = value.replace(/(\d)(\d{3}),/g, "$1.$2,");
    input.value = value;
}

// Formatar código automaticamente
document.querySelector('input[name="codigo"]').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9\-]/g, '');
});
</script>
