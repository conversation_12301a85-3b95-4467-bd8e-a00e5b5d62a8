<?php
/**
 * Dashboard do Centro de Custos
 */
?>

<!-- Dashboard Principal -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total de Centros -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-sitemap text-orange-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Centros de Custos</p>
                <p class="text-2xl font-semibold text-gray-900"><?php echo $resumo_geral['total_centros']; ?></p>
                <p class="text-sm text-gray-500">ativos</p>
            </div>
        </div>
    </div>

    <!-- Custos do Mês -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-red-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Custos do Mês</p>
                <p class="text-2xl font-semibold text-red-600">
                    R$ <?php echo number_format($resumo_geral['total_custos_mes'], 2, ',', '.'); ?>
                </p>
                <p class="text-sm text-gray-500"><?php echo date('m/Y'); ?></p>
            </div>
        </div>
    </div>

    <!-- Alocações do Mês -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-share-alt text-blue-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Alocações do Mês</p>
                <p class="text-2xl font-semibold text-blue-600"><?php echo $resumo_geral['total_alocacoes_mes']; ?></p>
                <p class="text-sm text-gray-500">lançamentos</p>
            </div>
        </div>
    </div>

    <!-- Custo Médio por Centro -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-chart-bar text-green-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Custo Médio</p>
                <p class="text-2xl font-semibold text-green-600">
                    R$ <?php echo $resumo_geral['total_centros'] > 0 ? number_format($resumo_geral['total_custos_mes'] / $resumo_geral['total_centros'], 2, ',', '.') : '0,00'; ?>
                </p>
                <p class="text-sm text-gray-500">por centro</p>
            </div>
        </div>
    </div>
</div>

<!-- Menu de Ações -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Gestão de Centros -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-building text-orange-500 mr-2"></i>
                Gestão de Centros
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Criar e gerenciar centros de custos</p>
        <div class="space-y-2">
            <a href="?acao=novo_centro" class="block w-full bg-orange-600 text-white text-center py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Novo Centro
            </a>
            <button onclick="alert('Funcionalidade em desenvolvimento')" class="block w-full bg-amber-600 text-white text-center py-2 px-4 rounded-lg hover:bg-amber-700 transition-colors">
                <i class="fas fa-list mr-2"></i>
                Listar Centros
            </button>
        </div>
    </div>

    <!-- Alocação de Custos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-share-alt text-blue-500 mr-2"></i>
                Alocação de Custos
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Alocar custos diretos aos centros</p>
        <div class="space-y-2">
            <a href="?acao=alocar" class="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Alocar Custo
            </a>
            <button onclick="alert('Funcionalidade em desenvolvimento')" class="block w-full bg-indigo-600 text-white text-center py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                <i class="fas fa-history mr-2"></i>
                Histórico
            </button>
        </div>
    </div>

    <!-- Rateio de Custos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-percentage text-green-500 mr-2"></i>
                Rateio de Custos
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Ratear custos indiretos automaticamente</p>
        <div class="space-y-2">
            <a href="?acao=ratear" class="block w-full bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-calculator mr-2"></i>
                Novo Rateio
            </a>
            <button onclick="alert('Funcionalidade em desenvolvimento')" class="block w-full bg-emerald-600 text-white text-center py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors">
                <i class="fas fa-cogs mr-2"></i>
                Configurar Rateio
            </button>
        </div>
    </div>

    <!-- Relatórios -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-chart-line text-purple-500 mr-2"></i>
                Relatórios
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Análises e relatórios de custos</p>
        <div class="space-y-2">
            <a href="?acao=relatorio" class="block w-full bg-purple-600 text-white text-center py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-chart-bar mr-2"></i>
                Análise de Custos
            </a>
            <button onclick="alert('Funcionalidade em desenvolvimento')" class="block w-full bg-violet-600 text-white text-center py-2 px-4 rounded-lg hover:bg-violet-700 transition-colors">
                <i class="fas fa-file-pdf mr-2"></i>
                Relatório PDF
            </button>
        </div>
    </div>
</div>

<!-- Centros de Custos -->
<?php if (!empty($centros_custos)): ?>
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Lista de Centros -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-orange-500 mr-2"></i>
                Centros de Custos Ativos
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php foreach ($centros_custos as $centro): ?>
                    <?php $custos_centro = $resumo_custos[$centro['id']] ?? ['total_custos' => 0, 'total_alocacoes' => 0]; ?>
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900">
                                <?php echo htmlspecialchars($centro['codigo'] . ' - ' . $centro['nome']); ?>
                            </h4>
                            <p class="text-sm text-gray-500">
                                <?php echo ucfirst($centro['tipo']); ?>
                                <?php if ($centro['responsavel']): ?>
                                    • <?php echo htmlspecialchars($centro['responsavel']); ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">
                                R$ <?php echo number_format($custos_centro['total_custos'], 2, ',', '.'); ?>
                            </p>
                            <p class="text-sm text-gray-500">
                                <?php echo $custos_centro['total_alocacoes']; ?> alocações
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Top 5 Maiores Custos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-trophy text-red-500 mr-2"></i>
                Top 5 - Maiores Custos (<?php echo date('m/Y'); ?>)
            </h3>
        </div>
        <div class="p-6">
            <?php if (!empty($top_custos)): ?>
                <div class="space-y-4">
                    <?php foreach ($top_custos as $index => $centro): ?>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-bold text-red-600"><?php echo $index + 1; ?></span>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">
                                        <?php echo htmlspecialchars($centro['codigo']); ?>
                                    </h4>
                                    <p class="text-sm text-gray-500">
                                        <?php echo htmlspecialchars($centro['nome']); ?>
                                    </p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-red-600">
                                    R$ <?php echo number_format($centro['total_custos'], 2, ',', '.'); ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-chart-bar text-gray-400 text-3xl mb-4"></i>
                    <p class="text-gray-500">Nenhum custo registrado este mês</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Análise Rápida -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-analytics text-blue-500 mr-2"></i>
            Análise Rápida - <?php echo date('m/Y'); ?>
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Distribuição de Custos -->
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                    <?php echo $resumo_geral['total_centros']; ?>
                </div>
                <p class="text-sm text-gray-600">Centros Ativos</p>
                <p class="text-xs text-gray-500 mt-1">
                    Média: R$ <?php echo $resumo_geral['total_centros'] > 0 ? number_format($resumo_geral['total_custos_mes'] / $resumo_geral['total_centros'], 2, ',', '.') : '0,00'; ?> por centro
                </p>
            </div>

            <!-- Eficiência -->
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">
                    <?php echo $resumo_geral['total_alocacoes_mes']; ?>
                </div>
                <p class="text-sm text-gray-600">Alocações Realizadas</p>
                <p class="text-xs text-gray-500 mt-1">
                    Média: <?php echo $resumo_geral['total_centros'] > 0 ? number_format($resumo_geral['total_alocacoes_mes'] / $resumo_geral['total_centros'], 1, ',', '.') : '0'; ?> por centro
                </p>
            </div>

            <!-- Controle -->
            <div class="text-center">
                <div class="text-3xl font-bold text-orange-600 mb-2">
                    <?php echo count($centros_custos); ?>
                </div>
                <p class="text-sm text-gray-600">Centros Configurados</p>
                <p class="text-xs text-gray-500 mt-1">
                    Status: <?php echo count($centros_custos) > 0 ? 'Operacional' : 'Configuração Pendente'; ?>
                </p>
            </div>
        </div>

        <?php if (count($centros_custos) === 0): ?>
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-yellow-800">Configuração Inicial Necessária</h4>
                        <p class="text-sm text-yellow-700 mt-1">
                            Para começar a usar o sistema de centro de custos, você precisa criar pelo menos um centro de custos.
                            <a href="?acao=novo_centro" class="font-medium underline">Criar primeiro centro de custos</a>
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
