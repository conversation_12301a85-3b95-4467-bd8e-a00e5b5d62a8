<?php
/**
 * Formulário para rateio de custos indiretos
 */

// Buscar centros de custos ativos para preview
try {
    $centros_preview = $db->fetchAll("
        SELECT * FROM financeiro_centro_custos WHERE status = 'ativo' ORDER BY codigo
    ") ?: [];
} catch (Exception $e) {
    $centros_preview = [];
}
?>

<div class="max-w-6xl mx-auto">
    <!-- Header do Formulário -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-percentage text-green-500 mr-2"></i>
                    Rateio de Custos Indiretos
                </h2>
                <a href="?acao=dashboard" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar ao Dashboard
                </a>
            </div>
        </div>

        <?php if (empty($centros_preview)): ?>
            <!-- Aviso se não há centros -->
            <div class="p-6">
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Nenhum Centro de Custos Disponível</h3>
                            <p class="text-sm text-yellow-700 mt-1">
                                Você precisa criar pelo menos dois centros de custos para realizar rateios.
                                <a href="?acao=novo_centro" class="font-medium underline">Criar centro de custos</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Formulário de Rateio -->
                <div class="p-6">
                    <form method="POST" id="formRateio">
                        <input type="hidden" name="acao" value="ratear_custos">

                        <div class="space-y-6">
                            <!-- Mês de Competência -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Mês de Competência <span class="text-red-500">*</span>
                                </label>
                                <input type="month" name="mes_competencia" required 
                                       value="<?php echo date('Y-m'); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            </div>

                            <!-- Valor Total -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Valor Total a Ratear <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-2 text-gray-500">R$</span>
                                    <input type="text" name="valor_total" required id="valor_total"
                                           class="w-full border border-gray-300 rounded-lg pl-10 pr-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                           placeholder="0,00" onkeyup="formatarMoeda(this); calcularPreview()">
                                </div>
                            </div>

                            <!-- Critério de Rateio -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Critério de Rateio <span class="text-red-500">*</span>
                                </label>
                                <select name="criterio_rateio" required id="criterio_rateio" onchange="calcularPreview()"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                    <option value="">Selecione o critério</option>
                                    <option value="igual">Rateio Igual (mesmo valor para todos)</option>
                                    <option value="receita">Proporcional à Meta de Receita</option>
                                    <option value="alunos">Proporcional ao Número de Alunos</option>
                                </select>
                            </div>

                            <!-- Descrição -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Descrição <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="descricao" required 
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                       placeholder="Ex: Rateio de energia elétrica, Custos administrativos">
                            </div>

                            <!-- Botões -->
                            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                                <a href="?acao=dashboard" class="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                    Cancelar
                                </a>
                                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-percentage mr-2"></i>
                                    Executar Rateio
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Preview do Rateio -->
                <div class="p-6 bg-gray-50">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-eye text-gray-500 mr-2"></i>
                        Preview do Rateio
                    </h3>
                    
                    <div id="preview_rateio" class="space-y-3">
                        <p class="text-gray-500 text-sm">Preencha o formulário para ver o preview do rateio.</p>
                    </div>
                </div>
            </div>

            <!-- Explicação dos Critérios -->
            <div class="p-6 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                    Critérios de Rateio
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Rateio Igual -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">Rateio Igual</h4>
                        <p class="text-sm text-blue-700 mb-2">
                            Divide o valor total igualmente entre todos os centros de custos ativos.
                        </p>
                        <p class="text-xs text-blue-600">
                            <strong>Exemplo:</strong> R$ 1.000 ÷ 4 centros = R$ 250 para cada
                        </p>
                    </div>

                    <!-- Proporcional à Receita -->
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">Proporcional à Receita</h4>
                        <p class="text-sm text-green-700 mb-2">
                            Distribui baseado na meta de receita mensal de cada centro.
                        </p>
                        <p class="text-xs text-green-600">
                            <strong>Exemplo:</strong> Centro com 40% da receita total recebe 40% do custo
                        </p>
                    </div>

                    <!-- Proporcional aos Alunos -->
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-800 mb-2">Proporcional aos Alunos</h4>
                        <p class="text-sm text-purple-700 mb-2">
                            Distribui baseado no número de alunos de cada centro.
                        </p>
                        <p class="text-xs text-purple-600">
                            <strong>Exemplo:</strong> Centro com 30% dos alunos recebe 30% do custo
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Dados dos centros para cálculo
const centros = <?php echo json_encode($centros_preview); ?>;

function formatarMoeda(input) {
    let value = input.value.replace(/\D/g, '');
    value = (value / 100).toFixed(2) + '';
    value = value.replace(".", ",");
    value = value.replace(/(\d)(\d{3})(\d{3}),/g, "$1.$2.$3,");
    value = value.replace(/(\d)(\d{3}),/g, "$1.$2,");
    input.value = value;
}

function calcularPreview() {
    const valorTotal = document.getElementById('valor_total').value;
    const criterio = document.getElementById('criterio_rateio').value;
    const previewDiv = document.getElementById('preview_rateio');
    
    if (!valorTotal || !criterio || centros.length === 0) {
        previewDiv.innerHTML = '<p class="text-gray-500 text-sm">Preencha o formulário para ver o preview do rateio.</p>';
        return;
    }
    
    // Converter valor para número
    const valor = parseFloat(valorTotal.replace(/\./g, '').replace(',', '.'));
    
    if (isNaN(valor) || valor <= 0) {
        previewDiv.innerHTML = '<p class="text-red-500 text-sm">Valor inválido.</p>';
        return;
    }
    
    // Calcular rateio
    let totalBase = 0;
    let bases = {};
    
    centros.forEach(centro => {
        switch (criterio) {
            case 'igual':
                bases[centro.id] = 1;
                totalBase += 1;
                break;
            case 'receita':
                const receita = parseFloat(centro.meta_receita_mensal) || 1;
                bases[centro.id] = receita;
                totalBase += receita;
                break;
            case 'alunos':
                // Simular número de alunos (em produção, buscar da base real)
                const alunos = Math.floor(Math.random() * 150) + 50;
                bases[centro.id] = alunos;
                totalBase += alunos;
                break;
        }
    });
    
    // Gerar preview
    let html = '<div class="space-y-3">';
    let totalRateado = 0;
    
    centros.forEach(centro => {
        const proporcao = bases[centro.id] / totalBase;
        const valorRateado = valor * proporcao;
        totalRateado += valorRateado;
        
        html += `
            <div class="bg-white p-3 rounded border">
                <div class="flex justify-between items-center">
                    <div>
                        <h5 class="font-medium text-gray-900">${centro.codigo}</h5>
                        <p class="text-sm text-gray-500">${centro.nome}</p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-900">R$ ${valorRateado.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</p>
                        <p class="text-xs text-gray-500">${(proporcao * 100).toFixed(2)}%</p>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
        <div class="bg-green-100 p-3 rounded border border-green-200">
            <div class="flex justify-between items-center">
                <span class="font-medium text-green-800">Total Rateado:</span>
                <span class="font-bold text-green-800">R$ ${totalRateado.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</span>
            </div>
        </div>
    `;
    
    html += '</div>';
    
    previewDiv.innerHTML = html;
}
</script>
