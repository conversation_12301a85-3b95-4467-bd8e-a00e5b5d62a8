<?php
/**
 * Teste do Webhook Asaas
 * Simula eventos do webhook para teste
 */

require_once __DIR__ . '/../config/database.php';

echo "<h1>Teste do Webhook Asaas</h1>";

// Verificar se há algum pagamento Asaas para teste
try {
    $stmt = $pdo->prepare("
        SELECT ma.*, a.nome 
        FROM mensalidades_alunos ma 
        JOIN alunos a ON ma.aluno_id = a.id 
        WHERE ma.asaas_payment_id IS NOT NULL 
        LIMIT 5
    ");
    $stmt->execute();
    $pagamentos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($pagamentos)) {
        echo "<p>❌ Nenhum pagamento Asaas encontrado. Gere um boleto primeiro.</p>";
        exit;
    }
    
    echo "<h2>Pagamentos Asaas Encontrados:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th><PERSON><PERSON></th><th>Valor</th><th>Vencimento</th><th>Status</th><th>Asaas <PERSON>ment ID</th></tr>";
    
    foreach ($pagamentos as $pag) {
        echo "<tr>";
        echo "<td>{$pag['id']}</td>";
        echo "<td>{$pag['nome']}</td>";
        echo "<td>R$ " . number_format($pag['valor'], 2, ',', '.') . "</td>";
        echo "<td>" . date('d/m/Y', strtotime($pag['data_vencimento'])) . "</td>";
        echo "<td>{$pag['status']}</td>";
        echo "<td>{$pag['asaas_payment_id']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Simular webhook de pagamento recebido
    if (isset($_GET['simular']) && isset($_GET['payment_id'])) {
        echo "<h2>Simulando Webhook de Pagamento Recebido</h2>";
        
        $payment_id = $_GET['payment_id'];
        
        // Dados simulados do webhook
        $webhook_data = [
            'event' => 'PAYMENT_RECEIVED',
            'payment' => [
                'id' => $payment_id,
                'status' => 'RECEIVED',
                'value' => 416.67,
                'paymentDate' => date('Y-m-d'),
                'customer' => 'cus_123456789'
            ]
        ];
        
        // Simular processamento do webhook
        try {
            // Salvar webhook simulado
            $stmt = $pdo->prepare("
                INSERT INTO asaas_webhooks (event_type, payment_id, customer_id, webhook_data) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $webhook_data['event'],
                $webhook_data['payment']['id'],
                $webhook_data['payment']['customer'],
                json_encode($webhook_data)
            ]);
            
            $webhook_id = $pdo->lastInsertId();
            
            // Processar pagamento
            $payment = $webhook_data['payment'];
            $valor_pago = $payment['value'];
            $data_pagamento = $payment['paymentDate'];
            
            // Atualizar mensalidade
            $stmt = $pdo->prepare("
                UPDATE mensalidades_alunos 
                SET status = 'pago', 
                    data_pagamento = ?, 
                    valor_pago = ?,
                    asaas_status = ?
                WHERE asaas_payment_id = ?
            ");
            $stmt->execute([$data_pagamento, $valor_pago, $payment['status'], $payment_id]);
            
            // Marcar webhook como processado
            $stmt = $pdo->prepare("UPDATE asaas_webhooks SET processed = 1, processed_at = NOW() WHERE id = ?");
            $stmt->execute([$webhook_id]);
            
            echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
            echo "✅ <strong>Webhook simulado com sucesso!</strong><br>";
            echo "- Payment ID: {$payment_id}<br>";
            echo "- Status atualizado para: PAGO<br>";
            echo "- Data do pagamento: " . date('d/m/Y', strtotime($data_pagamento)) . "<br>";
            echo "- Valor pago: R$ " . number_format($valor_pago, 2, ',', '.') . "<br>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
            echo "❌ <strong>Erro ao simular webhook:</strong> " . $e->getMessage();
            echo "</div>";
        }
    }
    
    // Formulário para simular webhook
    echo "<h2>Simular Webhook</h2>";
    echo "<form method='GET'>";
    echo "<input type='hidden' name='simular' value='1'>";
    echo "<label>Selecione um Payment ID para simular pagamento:</label><br>";
    echo "<select name='payment_id' required>";
    echo "<option value=''>Selecione...</option>";
    
    foreach ($pagamentos as $pag) {
        if ($pag['status'] === 'pendente') {
            echo "<option value='{$pag['asaas_payment_id']}'>";
            echo "{$pag['nome']} - {$pag['asaas_payment_id']} - R$ " . number_format($pag['valor'], 2, ',', '.');
            echo "</option>";
        }
    }
    
    echo "</select><br><br>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; cursor: pointer;'>";
    echo "Simular Pagamento Recebido";
    echo "</button>";
    echo "</form>";
    
    // Últimos webhooks
    echo "<h2>Últimos Webhooks Recebidos</h2>";
    $stmt = $pdo->prepare("
        SELECT * FROM asaas_webhooks 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $webhooks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($webhooks)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Evento</th><th>Payment ID</th><th>Processado</th><th>Data</th></tr>";
        
        foreach ($webhooks as $wh) {
            $processado = $wh['processed'] ? '✅ Sim' : '❌ Não';
            echo "<tr>";
            echo "<td>{$wh['id']}</td>";
            echo "<td>{$wh['event_type']}</td>";
            echo "<td>{$wh['payment_id']}</td>";
            echo "<td>{$processado}</td>";
            echo "<td>" . date('d/m/Y H:i:s', strtotime($wh['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Nenhum webhook recebido ainda.</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>URL do Webhook para Configurar no Asaas:</h2>";
echo "<code style='background: #f8f9fa; padding: 10px; display: block; margin: 10px 0;'>";
echo "https://" . $_SERVER['HTTP_HOST'] . "/api/asaas_webhook.php";
echo "</code>";

echo "<h2>Como Testar o Webhook Real:</h2>";
echo "<ol>";
echo "<li>Configure a URL acima no painel do Asaas</li>";
echo "<li>Gere um boleto via financeiro/boletos.php</li>";
echo "<li>Faça um pagamento teste (sandbox do Asaas)</li>";
echo "<li>Verifique se o webhook foi recebido aqui</li>";
echo "</ol>";
?>
