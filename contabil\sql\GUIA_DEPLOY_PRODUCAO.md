# 🚀 GUIA DE DEPLOY PRODUÇÃO - SISTEMA DE ROXINHOS

## 📋 **CHECKLIST PRÉ-DEPLOY**

### ✅ **1. BACKUP OBRIGATÓRIO**
```bash
# Fazer backup completo do banco de produção ANTES de qualquer alteração
mysqldump -u [usuario] -p [banco_producao] > backup_pre_roxinhos_$(date +%Y%m%d_%H%M%S).sql
```

### ✅ **2. VERIFICAÇÃO DO AMBIENTE**
- [ ] Acesso ao banco de produção confirmado
- [ ] Backup realizado e testado
- [ ] Horário de menor movimento escolhido
- [ ] Equipe técnica disponível para suporte

---

## 🔍 **PASSO 1: VERIFICAÇÃO PRÉ-DEPLOY**

Execute o script de verificação para entender o estado atual:

```sql
-- Executar no banco de produção
SOURCE financeiro/sql/pre_deploy_verificacao.sql;
```

**O que este script faz:**
- ✅ Verifica tabelas existentes
- ✅ Analisa estrutura atual da tabela `roxinhos`
- ✅ Conta registros existentes
- ✅ Verifica dependências (tabela `polos`)
- ✅ Lista views e triggers atuais
- ✅ Gera relatório do que precisa ser criado

---

## 🚀 **PASSO 2: DEPLOY PRINCIPAL**

Execute o script principal de deploy:

```sql
-- Executar no banco de produção
SOURCE financeiro/sql/deploy_producao_roxinhos.sql;
```

**O que este script faz:**
- ✅ Atualiza tabela `roxinhos` existente (adiciona colunas faltantes)
- ✅ Cria tabelas do sistema (`roxinho_polos`, `roxinho_comissoes`, etc.)
- ✅ Cria views para consultas otimizadas
- ✅ Insere dados de exemplo (se não existirem)
- ✅ Cria triggers para auditoria automática
- ✅ Executa verificação final

---

## 📁 **PASSO 3: UPLOAD DOS ARQUIVOS PHP**

Faça upload dos seguintes arquivos para o servidor de produção:

### **Arquivos Principais:**
```
financeiro/
├── roxinhos.php                    # Arquivo principal do sistema
├── includes/
│   └── sidebar.php                 # Atualizado com link dos roxinhos
└── views/roxinhos/
    ├── listar.php                  # Listagem de roxinhos
    ├── novo.php                    # Cadastro/edição
    ├── editar.php                  # Redirecionamento para novo.php
    ├── vinculos.php                # Gestão de vínculos
    ├── comissoes.php               # Gestão de comissões
    ├── pagamentos.php              # Histórico de pagamentos
    └── relatorios.php              # Dashboard e relatórios
```

### **Permissões Necessárias:**
```bash
# Definir permissões corretas
chmod 644 financeiro/roxinhos.php
chmod 644 financeiro/views/roxinhos/*.php
chmod 755 financeiro/views/roxinhos/
```

---

## 🧪 **PASSO 4: TESTES PÓS-DEPLOY**

### **4.1 Teste de Acesso**
- [ ] Acessar: `https://app.faciencia.edu.br/financeiro/roxinhos.php`
- [ ] Verificar se a página carrega sem erros
- [ ] Confirmar se o menu lateral mostra o link "Roxinhos"

### **4.2 Teste de Funcionalidades**
- [ ] **Listagem:** Verificar se os 3 roxinhos de exemplo aparecem
- [ ] **Vínculos:** Testar criação de vínculo roxinho ↔ polo
- [ ] **Comissões:** Testar criação de comissão baseada em contrato
- [ ] **Pagamentos:** Testar registro de pagamento
- [ ] **Relatórios:** Verificar dashboard e estatísticas

### **4.3 Teste de Integração**
- [ ] Verificar se não quebrou outras funcionalidades do sistema
- [ ] Testar navegação entre módulos
- [ ] Confirmar que o sistema financeiro continua funcionando

---

## 🚨 **PASSO 5: ROLLBACK (SE NECESSÁRIO)**

Em caso de problemas, execute o rollback:

```sql
-- APENAS EM EMERGÊNCIA!
-- Descomente a linha abaixo no script antes de executar:
-- SET @confirmar_rollback = 'SIM_TENHO_CERTEZA';

SOURCE financeiro/sql/rollback_roxinhos.sql;
```

**Depois do rollback:**
- Remover arquivos PHP do servidor
- Restaurar backup do banco
- Investigar problemas antes de tentar novamente

---

## 📊 **ESTRUTURA CRIADA NO BANCO**

### **Tabelas:**
1. **`roxinhos`** - Cadastro de indicadores (atualizada)
2. **`roxinho_polos`** - Vínculos roxinho ↔ polo
3. **`roxinho_comissoes`** - Comissões geradas
4. **`roxinho_pagamentos`** - Histórico de pagamentos
5. **`roxinho_historico`** - Log de auditoria

### **Views:**
1. **`vw_roxinho_comissoes_detalhadas`** - Comissões com dados completos
2. **`vw_roxinho_resumo`** - Resumo por roxinho

### **Triggers:**
1. **`tr_roxinho_historico_insert`** - Log de cadastros
2. **`tr_roxinho_historico_update`** - Log de edições
3. **`tr_roxinho_comissao_historico`** - Log de comissões
4. **`tr_roxinho_pagamento_historico`** - Log de pagamentos

---

## 🔧 **CONFIGURAÇÕES ADICIONAIS**

### **Permissões de Usuário:**
Verificar se os usuários têm permissão para acessar o módulo financeiro:
```sql
-- Verificar permissões existentes
SELECT * FROM usuarios WHERE nivel IN ('admin', 'financeiro');
```

### **Logs de Erro:**
Monitorar logs do servidor após o deploy:
```bash
# Monitorar logs PHP
tail -f /var/log/php/error.log

# Monitorar logs MySQL
tail -f /var/log/mysql/error.log
```

---

## 📞 **SUPORTE PÓS-DEPLOY**

### **Contatos de Emergência:**
- Desenvolvedor responsável
- Administrador do sistema
- Suporte técnico da hospedagem

### **Monitoramento:**
- [ ] Verificar performance do banco após 24h
- [ ] Monitorar uso de espaço em disco
- [ ] Acompanhar logs de erro por 48h
- [ ] Coletar feedback dos usuários

---

## ✅ **CHECKLIST FINAL**

- [ ] Backup realizado
- [ ] Script de verificação executado
- [ ] Deploy principal executado sem erros
- [ ] Arquivos PHP enviados para produção
- [ ] Testes básicos realizados
- [ ] Sistema funcionando normalmente
- [ ] Equipe notificada sobre nova funcionalidade
- [ ] Documentação atualizada

---

## 🎉 **DEPLOY CONCLUÍDO!**

O Sistema de Roxinhos está agora disponível em produção em:
**https://app.faciencia.edu.br/financeiro/roxinhos.php**

### **Funcionalidades Disponíveis:**
- ✅ Cadastro e gestão de roxinhos
- ✅ Vínculos com polos e taxas específicas
- ✅ Criação e controle de comissões
- ✅ Registro de pagamentos por parcelas
- ✅ Relatórios e dashboards gerenciais
- ✅ Auditoria completa de alterações

**O sistema está pronto para revolucionar o controle de comissões da FaCiência!** 🚀
