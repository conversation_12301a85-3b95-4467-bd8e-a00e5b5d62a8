<?php
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

require_once '../includes/Database.php';

$db = Database::getInstance();

// Filtros
$filtro_tipo = $_GET['tipo'] ?? '';
$filtro_aluno = $_GET['aluno'] ?? '';
$filtro_data_inicio = $_GET['data_inicio'] ?? date('Y-m-d', strtotime('-7 days'));
$filtro_data_fim = $_GET['data_fim'] ?? date('Y-m-d');
$filtro_ip = $_GET['ip'] ?? '';

// Construir query de atividades
$where_conditions = [];
$params = [];

if ($filtro_tipo) {
    $where_conditions[] = "aa.tipo = :tipo";
    $params['tipo'] = $filtro_tipo;
}

if ($filtro_aluno) {
    $where_conditions[] = "(a.nome LIKE :aluno OR a.email LIKE :aluno)";
    $params['aluno'] = "%$filtro_aluno%";
}

if ($filtro_data_inicio) {
    $where_conditions[] = "DATE(aa.created_at) >= :data_inicio";
    $params['data_inicio'] = $filtro_data_inicio;
}

if ($filtro_data_fim) {
    $where_conditions[] = "DATE(aa.created_at) <= :data_fim";
    $params['data_fim'] = $filtro_data_fim;
}

if ($filtro_ip) {
    $where_conditions[] = "aa.ip LIKE :ip";
    $params['ip'] = "%$filtro_ip%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Buscar atividades
$atividades = $db->fetchAll("
    SELECT aa.*, 
           COALESCE(a.nome, 'Sistema') as aluno_nome,
           a.email as aluno_email
    FROM alunos_atividades aa
    LEFT JOIN alunos a ON aa.aluno_id = a.id
    $where_clause
    ORDER BY aa.created_at DESC
    LIMIT 500
", $params);

// Estatísticas gerais
$stats = [];

// Atividades por tipo (últimos 7 dias)
$stats['por_tipo'] = $db->fetchAll("
    SELECT tipo, COUNT(*) as total
    FROM alunos_atividades 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY tipo
    ORDER BY total DESC
");

// Logins por dia (últimos 7 dias)
$stats['logins_por_dia'] = $db->fetchAll("
    SELECT DATE(created_at) as data, COUNT(*) as total
    FROM alunos_atividades 
    WHERE tipo = 'login_success' 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY DATE(created_at)
    ORDER BY data DESC
");

// IPs mais ativos
$stats['ips_ativos'] = $db->fetchAll("
    SELECT ip, COUNT(*) as total, COUNT(DISTINCT aluno_id) as alunos_unicos
    FROM alunos_atividades 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND ip != 'unknown'
    GROUP BY ip
    ORDER BY total DESC
    LIMIT 10
");

// Alunos mais ativos (últimos 7 dias)
$stats['alunos_ativos'] = $db->fetchAll("
    SELECT a.nome, a.email, COUNT(*) as total_atividades,
           MAX(aa.created_at) as ultimo_acesso
    FROM alunos_atividades aa
    INNER JOIN alunos a ON aa.aluno_id = a.id
    WHERE aa.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY aa.aluno_id
    ORDER BY total_atividades DESC
    LIMIT 10
");

// Tentativas de login falhadas (últimas 24h)
$stats['tentativas_falhadas'] = $db->fetchAll("
    SELECT ip, COUNT(*) as tentativas,
           GROUP_CONCAT(DISTINCT SUBSTRING(descricao, 1, 50) SEPARATOR '; ') as detalhes
    FROM alunos_atividades 
    WHERE tipo IN ('login_failed', 'login_blocked', 'login_blocked_ip')
    AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    GROUP BY ip
    ORDER BY tentativas DESC
    LIMIT 10
");

$page_title = 'Monitoramento do Portal';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Administrador</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'red': {
                            50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5',
                            400: '#f87171', 500: '#ef4444', 600: '#dc2626', 700: '#b91c1c',
                            800: '#991b1b', 900: '#7f1d1d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <div class="bg-red-600 text-white p-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-xl font-bold"><?php echo $page_title; ?></h1>
                <p class="text-red-100">Monitoramento de segurança e atividades do portal dos alunos</p>
            </div>
            <div class="flex space-x-4">
                <a href="index.php" class="bg-red-700 hover:bg-red-800 px-4 py-2 rounded-md">
                    <i class="fas fa-arrow-left mr-2"></i>Voltar
                </a>
                <a href="../portal_aluno/setup.php?key=faciencia_setup_2025" target="_blank" class="bg-red-700 hover:bg-red-800 px-4 py-2 rounded-md">
                    <i class="fas fa-cogs mr-2"></i>Setup
                </a>
            </div>
        </div>
    </div>

    <!-- Content -->
    <main class="p-6">
        <!-- Estatísticas Rápidas -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- Total de Atividades (24h) -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-blue-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Atividades (24h)</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php 
                            $total_24h = $db->fetch("SELECT COUNT(*) as total FROM alunos_atividades WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['total'];
                            echo number_format($total_24h);
                            ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Logins Únicos (24h) -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-green-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Logins Únicos (24h)</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php 
                            $logins_unicos = $db->fetch("SELECT COUNT(DISTINCT aluno_id) as total FROM alunos_atividades WHERE tipo = 'login_success' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['total'];
                            echo number_format($logins_unicos);
                            ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Tentativas Falhadas (24h) -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Falhas (24h)</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php 
                            $falhas_24h = $db->fetch("SELECT COUNT(*) as total FROM alunos_atividades WHERE tipo IN ('login_failed', 'login_blocked') AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['total'];
                            echo number_format($falhas_24h);
                            ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- IPs Únicos (24h) -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-globe text-purple-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">IPs Únicos (24h)</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php 
                            $ips_unicos = $db->fetch("SELECT COUNT(DISTINCT ip) as total FROM alunos_atividades WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) AND ip != 'unknown'")['total'];
                            echo number_format($ips_unicos);
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Atividades por Tipo -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Atividades por Tipo (7 dias)</h3>
                <canvas id="chartTipos" width="400" height="200"></canvas>
            </div>

            <!-- Logins por Dia -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Logins por Dia</h3>
                <canvas id="chartLogins" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Filtros -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Filtros de Atividades</h3>
            
            <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
                    <select name="tipo" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500">
                        <option value="">Todos</option>
                        <option value="login_success" <?php echo $filtro_tipo === 'login_success' ? 'selected' : ''; ?>>Login Sucesso</option>
                        <option value="login_failed" <?php echo $filtro_tipo === 'login_failed' ? 'selected' : ''; ?>>Login Falhou</option>
                        <option value="login_blocked" <?php echo $filtro_tipo === 'login_blocked' ? 'selected' : ''; ?>>Login Bloqueado</option>
                        <option value="logout" <?php echo $filtro_tipo === 'logout' ? 'selected' : ''; ?>>Logout</option>
                        <option value="password_changed" <?php echo $filtro_tipo === 'password_changed' ? 'selected' : ''; ?>>Senha Alterada</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Aluno</label>
                    <input type="text" name="aluno" value="<?php echo htmlspecialchars($filtro_aluno); ?>" 
                           placeholder="Nome ou email..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
                    <input type="date" name="data_inicio" value="<?php echo $filtro_data_inicio; ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
                    <input type="date" name="data_fim" value="<?php echo $filtro_data_fim; ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">IP</label>
                    <input type="text" name="ip" value="<?php echo htmlspecialchars($filtro_ip); ?>" 
                           placeholder="***********" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500">
                </div>
                
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md">
                        <i class="fas fa-search mr-2"></i>Filtrar
                    </button>
                    <a href="portal_monitoramento.php" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md">
                        <i class="fas fa-times mr-2"></i>Limpar
                    </a>
                </div>
            </form>
        </div>

        <!-- Lista de Atividades -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Log de Atividades (<?php echo count($atividades); ?>)</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data/Hora</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aluno</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($atividades as $atividade): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y H:i:s', strtotime($atividade['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($atividade['aluno_nome']); ?></div>
                                <?php if ($atividade['aluno_email']): ?>
                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($atividade['aluno_email']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php 
                                    switch($atividade['tipo']) {
                                        case 'login_success': echo 'bg-green-100 text-green-800'; break;
                                        case 'login_failed': echo 'bg-red-100 text-red-800'; break;
                                        case 'login_blocked': echo 'bg-red-100 text-red-800'; break;
                                        case 'logout': echo 'bg-blue-100 text-blue-800'; break;
                                        case 'password_changed': echo 'bg-purple-100 text-purple-800'; break;
                                        default: echo 'bg-gray-100 text-gray-800';
                                    }
                                    ?>">
                                    <?php echo htmlspecialchars($atividade['tipo']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <?php echo htmlspecialchars($atividade['descricao']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                                <?php echo htmlspecialchars($atividade['ip']); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <?php if (empty($atividades)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-chart-line text-gray-300 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma atividade encontrada</h3>
                    <p class="text-gray-500">Ajuste os filtros para ver mais resultados.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        // Gráfico de Atividades por Tipo
        const ctxTipos = document.getElementById('chartTipos').getContext('2d');
        new Chart(ctxTipos, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_column($stats['por_tipo'], 'tipo')); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_column($stats['por_tipo'], 'total')); ?>,
                    backgroundColor: [
                        '#10b981', '#ef4444', '#f59e0b', '#3b82f6', '#8b5cf6', '#6b7280'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Gráfico de Logins por Dia
        const ctxLogins = document.getElementById('chartLogins').getContext('2d');
        new Chart(ctxLogins, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_reverse(array_column($stats['logins_por_dia'], 'data'))); ?>,
                datasets: [{
                    label: 'Logins',
                    data: <?php echo json_encode(array_reverse(array_column($stats['logins_por_dia'], 'total'))); ?>,
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Auto-refresh a cada 30 segundos
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
