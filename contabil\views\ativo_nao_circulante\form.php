<?php
/**
 * Formulário para cadastro de ativos não circulantes
 */

$tipo_ativo_selecionado = $_GET['tipo'] ?? 'imobilizado';
?>

<div class="max-w-4xl mx-auto">
    <!-- Header do Formulário -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-plus text-indigo-500 mr-2"></i>
                    Novo Ativo Não Circulante
                </h2>
                <a href="?acao=dashboard" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar ao Dashboard
                </a>
            </div>
        </div>

        <!-- Formulário -->
        <form method="POST" class="p-6">
            <input type="hidden" name="acao" value="criar">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Tipo de Ativo -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Tipo de Ativo <span class="text-red-500">*</span>
                    </label>
                    <select name="tipo_ativo" required onchange="atualizarCategorias()" id="tipo_ativo"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="imobilizado" <?php echo $tipo_ativo_selecionado === 'imobilizado' ? 'selected' : ''; ?>>Imobilizado</option>
                        <option value="intangivel" <?php echo $tipo_ativo_selecionado === 'intangivel' ? 'selected' : ''; ?>>Intangível</option>
                        <option value="investimento" <?php echo $tipo_ativo_selecionado === 'investimento' ? 'selected' : ''; ?>>Investimento</option>
                    </select>
                </div>

                <!-- Categoria -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Categoria <span class="text-red-500">*</span>
                    </label>
                    <select name="categoria" required id="categoria"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <!-- Opções serão preenchidas via JavaScript -->
                    </select>
                </div>

                <!-- Descrição -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Descrição <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="descricao" required 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Descrição detalhada do ativo">
                </div>

                <!-- Número do Patrimônio -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Número do Patrimônio
                    </label>
                    <input type="text" name="numero_patrimonio" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Ex: PAT001, EQ2025001">
                </div>

                <!-- Data de Aquisição -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Data de Aquisição <span class="text-red-500">*</span>
                    </label>
                    <input type="date" name="data_aquisicao" required 
                           value="<?php echo date('Y-m-d'); ?>"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <!-- Valor de Aquisição -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Valor de Aquisição <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <span class="absolute left-3 top-2 text-gray-500">R$</span>
                        <input type="text" name="valor_aquisicao" required 
                               class="w-full border border-gray-300 rounded-lg pl-10 pr-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="0,00" onkeyup="formatarMoeda(this)">
                    </div>
                </div>

                <!-- Fornecedor -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Fornecedor
                    </label>
                    <input type="text" name="fornecedor" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Nome do fornecedor">
                </div>

                <!-- Nota Fiscal -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Nota Fiscal
                    </label>
                    <input type="text" name="nota_fiscal" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Número da nota fiscal">
                </div>

                <!-- Localização -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Localização
                    </label>
                    <input type="text" name="localizacao" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Sala, andar, prédio">
                </div>

                <!-- Responsável -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Responsável
                    </label>
                    <input type="text" name="responsavel" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Nome do responsável">
                </div>
            </div>

            <!-- Observações -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Observações
                </label>
                <textarea name="observacoes" rows="3" 
                          class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="Observações adicionais sobre o ativo"></textarea>
            </div>

            <!-- Informações de Depreciação -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 class="text-md font-semibold text-blue-800 mb-2">
                    <i class="fas fa-info-circle mr-2"></i>
                    Informações de Depreciação/Amortização
                </h4>
                <div id="info_depreciacao" class="text-sm text-blue-700">
                    <p>Selecione uma categoria para ver as informações de depreciação.</p>
                </div>
            </div>

            <!-- Botões -->
            <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                <a href="?acao=dashboard" class="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancelar
                </a>
                <button type="submit" class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Cadastrar Ativo
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Configurações de categorias por tipo
const categorias = {
    imobilizado: {
        'moveis_utensilios': 'Móveis e Utensílios',
        'equipamentos_informatica': 'Equipamentos de Informática',
        'veiculos': 'Veículos',
        'maquinas_equipamentos': 'Máquinas e Equipamentos',
        'instalacoes': 'Instalações',
        'edificacoes': 'Edificações'
    },
    intangivel: {
        'software': 'Software',
        'marcas_patentes': 'Marcas e Patentes'
    },
    investimento: {
        'participacoes': 'Participações Societárias',
        'aplicacoes_longo_prazo': 'Aplicações de Longo Prazo',
        'imoveis_investimento': 'Imóveis para Investimento'
    }
};

// Informações de depreciação
const infoDepreciacao = {
    'moveis_utensilios': { vida_util: 10, taxa: 10.00 },
    'equipamentos_informatica': { vida_util: 5, taxa: 20.00 },
    'veiculos': { vida_util: 5, taxa: 20.00 },
    'maquinas_equipamentos': { vida_util: 10, taxa: 10.00 },
    'instalacoes': { vida_util: 10, taxa: 10.00 },
    'edificacoes': { vida_util: 25, taxa: 4.00 },
    'software': { vida_util: 5, taxa: 20.00 },
    'marcas_patentes': { vida_util: 10, taxa: 10.00 }
};

function atualizarCategorias() {
    const tipoAtivo = document.getElementById('tipo_ativo').value;
    const categoriaSelect = document.getElementById('categoria');
    
    // Limpar opções
    categoriaSelect.innerHTML = '<option value="">Selecione uma categoria</option>';
    
    // Adicionar novas opções
    if (categorias[tipoAtivo]) {
        Object.entries(categorias[tipoAtivo]).forEach(([value, text]) => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            categoriaSelect.appendChild(option);
        });
    }
    
    // Atualizar informações de depreciação
    atualizarInfoDepreciacao();
}

function atualizarInfoDepreciacao() {
    const categoria = document.getElementById('categoria').value;
    const infoDiv = document.getElementById('info_depreciacao');
    const tipoAtivo = document.getElementById('tipo_ativo').value;
    
    if (categoria && infoDepreciacao[categoria] && tipoAtivo !== 'investimento') {
        const info = infoDepreciacao[categoria];
        const tipoCalculo = tipoAtivo === 'intangivel' ? 'amortização' : 'depreciação';
        
        infoDiv.innerHTML = `
            <p><strong>Vida útil:</strong> ${info.vida_util} anos</p>
            <p><strong>Taxa de ${tipoCalculo}:</strong> ${info.taxa}% ao ano</p>
            <p><strong>Método:</strong> Linear</p>
            <p class="mt-2 text-xs">A ${tipoCalculo} será calculada automaticamente a partir da data de aquisição.</p>
        `;
    } else if (tipoAtivo === 'investimento') {
        infoDiv.innerHTML = `
            <p><strong>Investimentos:</strong> Não sofrem depreciação ou amortização.</p>
            <p class="text-xs">O valor será mantido pelo custo histórico ou valor justo, conforme aplicável.</p>
        `;
    } else {
        infoDiv.innerHTML = '<p>Selecione uma categoria para ver as informações de depreciação.</p>';
    }
}

function formatarMoeda(input) {
    let value = input.value.replace(/\D/g, '');
    value = (value / 100).toFixed(2) + '';
    value = value.replace(".", ",");
    value = value.replace(/(\d)(\d{3})(\d{3}),/g, "$1.$2.$3,");
    value = value.replace(/(\d)(\d{3}),/g, "$1.$2,");
    input.value = value;
}

// Inicializar categorias
document.addEventListener('DOMContentLoaded', function() {
    atualizarCategorias();
    
    // Event listener para mudança de categoria
    document.getElementById('categoria').addEventListener('change', atualizarInfoDepreciacao);
});
</script>
