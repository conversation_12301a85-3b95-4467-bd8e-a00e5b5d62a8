CREATE TABLE IF NOT EXISTS patrimonio_snapshots (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data_snapshot DATETIME NOT NULL,
    total_ativos DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_passivos DECIMAL(15,2) NOT NULL DEFAULT 0,
    patrimonio_liquido DECIMAL(15,2) NOT NULL DEFAULT 0,
    detalhes_ativos JSON,
    detalhes_passivos JSON,
    observacoes TEXT,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_data_snapshot (data_snapshot),
    INDEX idx_patrimonio_liquido (patrimonio_liquido)
);

-- Inserir snapshot inicial se não existir
INSERT IGNORE INTO patrimonio_snapshots (data_snapshot, total_ativos, total_passivos, patrimonio_liquido, observacoes)
VALUES (NOW(), 0, 0, 0, 'Snapshot inicial do sistema');