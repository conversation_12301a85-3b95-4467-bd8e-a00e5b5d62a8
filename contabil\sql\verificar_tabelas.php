<?php
/**
 * Script para verificar se todas as tabelas do módulo financeiro estão criadas
 */

// Configurações de erro
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Conectar ao banco
require_once '../../secretaria/includes/Database.php';

try {
    $db = Database::getInstance();
    
    // Lista de todas as tabelas necessárias para o módulo financeiro
    $tabelas_necessarias = [
        // Tabelas básicas do financeiro
        'financeiro_contas_pagar' => 'Contas a Pagar',
        'financeiro_contas_receber' => 'Contas a Receber',
        'financeiro_movimentacao_bancaria' => 'Movimentação Bancária',
        'financeiro_impostos_recolher' => 'Impostos a Recolher',
        'financeiro_retencoes_fonte' => 'Retenções na Fonte',
        
        // Tabelas das páginas implementadas
        'financeiro_ativo_nao_circulante' => 'Ativo Não Circulante',
        'financeiro_centro_custos' => 'Centro de Custos',
        'financeiro_alocacao_custos' => 'Alocação de Custos',
        'financeiro_fluxo_projetado' => 'Fluxo de Caixa Projetado',
        'financeiro_aplicacoes' => 'Aplicações Financeiras',
        'financeiro_cenarios' => 'Cenários de Planejamento',
        'financeiro_orcamento' => 'Orçamento Anual',
        'financeiro_orcamento_realizado' => 'Orçamento Realizado',
        'financeiro_metas' => 'Metas Financeiras',
        
        // Tabelas de boletos
        'polos' => 'Polos da Instituição',
        'polos_boletos' => 'Boletos dos Polos'
    ];
    
    echo "<h2>🔍 Verificação das Tabelas do Módulo Financeiro</h2>\n";
    
    $tabelas_existentes = 0;
    $tabelas_faltantes = 0;
    $tabelas_com_erro = [];
    
    foreach ($tabelas_necessarias as $tabela => $descricao) {
        try {
            // Verificar se a tabela existe
            $result = $db->query("SHOW TABLES LIKE ?", [$tabela]);
            
            if ($result) {
                // Contar registros na tabela
                $count_result = $db->fetchOne("SELECT COUNT(*) as total FROM `$tabela`");
                $total_registros = $count_result['total'] ?? 0;
                
                echo "<p style='color: green;'>✅ <strong>$tabela</strong> - $descricao ($total_registros registros)</p>\n";
                $tabelas_existentes++;
            } else {
                echo "<p style='color: red;'>❌ <strong>$tabela</strong> - $descricao (NÃO EXISTE)</p>\n";
                $tabelas_faltantes++;
            }
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ <strong>$tabela</strong> - $descricao (ERRO: " . htmlspecialchars($e->getMessage()) . ")</p>\n";
            $tabelas_com_erro[] = $tabela;
        }
    }
    
    echo "<hr>\n";
    echo "<h3>📊 Resumo da Verificação:</h3>\n";
    echo "<p><strong>Total de tabelas necessárias:</strong> " . count($tabelas_necessarias) . "</p>\n";
    echo "<p><strong>Tabelas existentes:</strong> <span style='color: green;'>$tabelas_existentes</span></p>\n";
    echo "<p><strong>Tabelas faltantes:</strong> <span style='color: red;'>$tabelas_faltantes</span></p>\n";
    echo "<p><strong>Tabelas com erro:</strong> <span style='color: orange;'>" . count($tabelas_com_erro) . "</span></p>\n";
    
    $percentual_completo = round(($tabelas_existentes / count($tabelas_necessarias)) * 100, 1);
    echo "<p><strong>Completude do módulo:</strong> <span style='font-size: 1.2em; font-weight: bold; color: " . ($percentual_completo == 100 ? 'green' : 'orange') . ";'>$percentual_completo%</span></p>\n";
    
    if ($tabelas_faltantes > 0) {
        echo "<div style='background: #FEE2E2; border: 1px solid #EF4444; color: #991B1B; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4>🚨 Ação Necessária:</h4>\n";
        echo "<p>Algumas tabelas estão faltando. Execute os scripts SQL para criar as estruturas:</p>\n";
        echo "<ol>\n";
        echo "<li><a href='executar_criacao_boletos.php'>Criar tabelas de boletos</a></li>\n";
        echo "<li>Execute o script SQL: <code>criar_tabelas_faltantes.sql</code></li>\n";
        echo "</ol>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #D1FAE5; border: 1px solid #10B981; color: #065F46; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4>🎉 Parabéns!</h4>\n";
        echo "<p>Todas as tabelas do módulo financeiro estão criadas e funcionando corretamente!</p>\n";
        echo "<p>O sistema está pronto para uso em produção.</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Erro de conexão: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação de Tabelas - Módulo Financeiro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 10px;
        }
        .actions {
            background: #F3F4F6;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .actions a {
            background: #3B82F6;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-right: 10px;
            display: inline-block;
            margin-bottom: 10px;
        }
        .actions a:hover {
            background: #2563EB;
        }
        code {
            background: #F3F4F6;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Verificação do Módulo Financeiro</h1>
        
        <div class="actions">
            <h3>🛠️ Ações Disponíveis:</h3>
            <a href="../index.php">🏠 Voltar ao Financeiro</a>
            <a href="executar_criacao_boletos.php">🔧 Criar Tabelas de Boletos</a>
            <a href="verificar_tabelas.php">🔄 Atualizar Verificação</a>
        </div>
        
        <hr>
        <p><small>Sistema Reinandus - Módulo Financeiro - Verificação de Integridade</small></p>
    </div>
</body>
</html>
