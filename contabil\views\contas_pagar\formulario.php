<?php
/**
 * VIEW: Formulário de Contas a Pagar
 * Arquivo: views/contas_pagar/formulario.php
 */

$conta = $conta ?? null;
$modo_edicao = ($acao === 'editar' && $conta);
?>

<div class="max-w-4xl mx-auto">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="contas_pagar.php?acao=dashboard" 
                   class="text-gray-700 hover:text-red-600 inline-flex items-center">
                    <i class="fas fa-home mr-2"></i>
                    Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="contas_pagar.php?acao=listar" 
                       class="text-gray-700 hover:text-red-600">
                        Contas a Pagar
                    </a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">
                        <?php echo $modo_edicao ? 'Editar Conta' : 'Nova Conta'; ?>
                    </span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Formulário Principal -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas <?php echo $modo_edicao ? 'fa-edit' : 'fa-plus'; ?> text-red-600 mr-2"></i>
                <?php echo $modo_edicao ? 'Editar Conta a Pagar' : 'Nova Conta a Pagar'; ?>
            </h2>
            <p class="text-gray-600 mt-1">
                <?php echo $modo_edicao ? 'Atualize as informações da conta a pagar' : 'Preencha os dados da nova conta a pagar'; ?>
            </p>
        </div>

        <form method="POST" action="contas_pagar.php?acao=salvar<?php echo $modo_edicao ? '&id=' . $conta['id'] : ''; ?>" 
              class="p-6" id="formContaPagar">
            
            <!-- Informações Básicas -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Descrição -->
                <div class="lg:col-span-2">
                    <label for="descricao" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-file-alt mr-1"></i>
                        Descrição da Conta *
                    </label>
                    <input type="text" id="descricao" name="descricao" required
                           value="<?php echo $modo_edicao ? htmlspecialchars($conta['descricao']) : ''; ?>"
                           placeholder="Ex: Conta de energia elétrica, Material de escritório..."
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors">
                    <p class="text-sm text-gray-500 mt-1">Descreva claramente o que esta conta representa</p>
                </div>

                <!-- Valor -->
                <div>
                    <label for="valor" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign mr-1"></i>
                        Valor *
                    </label>
                    <div class="relative">
                        <span class="absolute left-3 top-3 text-gray-500">R$</span>
                        <input type="text" id="valor" name="valor" required
                               value="<?php echo $modo_edicao ? number_format($conta['valor'], 2, ',', '.') : ''; ?>"
                               placeholder="0,00"
                               class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors input-moeda">
                    </div>
                    <p class="text-sm text-gray-500 mt-1">Valor total da conta a pagar</p>
                </div>

                <!-- Data de Vencimento -->
                <div>
                    <label for="data_vencimento" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar mr-1"></i>
                        Data de Vencimento *
                    </label>
                    <input type="date" id="data_vencimento" name="data_vencimento" required
                           value="<?php echo $modo_edicao ? $conta['data_vencimento'] : ''; ?>"
                           min="<?php echo date('Y-m-d'); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors">
                    <p class="text-sm text-gray-500 mt-1">Data limite para pagamento</p>
                </div>

                <!-- Categoria -->
                <div>
                    <label for="categoria_id" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tag mr-1"></i>
                        Categoria
                    </label>
                    <select id="categoria_id" name="categoria_id"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors">
                        <option value="">Selecione uma categoria</option>
                        <?php foreach ($categorias as $categoria): ?>
                            <option value="<?php echo $categoria['id']; ?>"
                                    <?php echo ($modo_edicao && $conta['categoria_id'] == $categoria['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($categoria['nome']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="flex items-center justify-between mt-1">
                        <p class="text-sm text-gray-500">Organize suas contas por categoria</p>
                        <a href="categorias_financeiras.php" target="_blank" 
                           class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-plus mr-1"></i>
                            Nova categoria
                        </a>
                    </div>
                </div>

                <!-- Fornecedor -->
                <div>
                    <label for="fornecedor_nome" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-building mr-1"></i>
                        Fornecedor/Prestador
                    </label>
                    <input type="text" id="fornecedor_nome" name="fornecedor_nome"
                           value="<?php echo $modo_edicao ? htmlspecialchars($conta['fornecedor_nome']) : ''; ?>"
                           placeholder="Ex: Empresa de Energia, Fornecedor XYZ..."
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors">
                    <p class="text-sm text-gray-500 mt-1">Nome da empresa ou pessoa</p>
                </div>
            </div>

            <!-- Observações -->
            <div class="mb-8">
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-sticky-note mr-1"></i>
                    Observações
                </label>
                <textarea id="observacoes" name="observacoes" rows="4"
                          placeholder="Informações adicionais sobre esta conta..."
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors resize-none"><?php echo $modo_edicao ? htmlspecialchars($conta['observacoes']) : ''; ?></textarea>
                <p class="text-sm text-gray-500 mt-1">Detalhes extras, número da fatura, etc.</p>
            </div>

            <!-- Alertas e Validações -->
            <div id="alertas-formulario" class="hidden mb-6">
                <!-- Alertas serão inseridos aqui via JavaScript -->
            </div>

            <!-- Ações do Formulário -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex space-x-3">
                    <a href="contas_pagar.php?acao=listar" 
                       class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg transition-colors font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar
                    </a>
                    
                    <?php if ($modo_edicao): ?>
                        <a href="contas_pagar.php?acao=visualizar&id=<?php echo $conta['id']; ?>" 
                           class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-6 py-3 rounded-lg transition-colors font-medium">
                            <i class="fas fa-eye mr-2"></i>
                            Visualizar
                        </a>
                    <?php endif; ?>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="resetarFormulario()" 
                            class="bg-yellow-100 hover:bg-yellow-200 text-yellow-700 px-6 py-3 rounded-lg transition-colors font-medium">
                        <i class="fas fa-undo mr-2"></i>
                        Limpar
                    </button>
                    
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg transition-colors font-medium shadow-md hover:shadow-lg">
                        <i class="fas fa-save mr-2"></i>
                        <?php echo $modo_edicao ? 'Atualizar Conta' : 'Salvar Conta'; ?>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Dicas e Ajuda -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Dicas de Preenchimento -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">
                <i class="fas fa-lightbulb mr-2"></i>
                Dicas de Preenchimento
            </h3>
            <ul class="space-y-3 text-sm text-blue-800">
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    <span><strong>Descrição:</strong> Seja específico para facilitar a identificação posterior</span>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    <span><strong>Categorias:</strong> Use categorias para organizar e gerar relatórios</span>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    <span><strong>Fornecedor:</strong> Mantenha padronização nos nomes</span>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5"></i>
                    <span><strong>Observações:</strong> Inclua número da fatura ou outros detalhes importantes</span>
                </li>
            </ul>
        </div>

        <!-- Atalhos do Teclado -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-green-900 mb-4">
                <i class="fas fa-keyboard mr-2"></i>
                Atalhos do Teclado
            </h3>
            <ul class="space-y-3 text-sm text-green-800">
                <li class="flex items-center justify-between">
                    <span>Salvar formulário</span>
                    <span class="bg-green-200 px-2 py-1 rounded text-xs font-mono">Ctrl + S</span>
                </li>
                <li class="flex items-center justify-between">
                    <span>Limpar formulário</span>
                    <span class="bg-green-200 px-2 py-1 rounded text-xs font-mono">Ctrl + R</span>
                </li>
                <li class="flex items-center justify-between">
                    <span>Voltar à listagem</span>
                    <span class="bg-green-200 px-2 py-1 rounded text-xs font-mono">Esc</span>
                </li>
                <li class="flex items-center justify-between">
                    <span>Próximo campo</span>
                    <span class="bg-green-200 px-2 py-1 rounded text-xs font-mono">Tab</span>
                </li>
            </ul>
        </div>
    </div>

    <!-- Preview de Informações -->
    <?php if ($modo_edicao): ?>
        <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                Informações da Conta
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <span class="text-gray-600">Criada em:</span>
                    <span class="font-medium ml-2">
                        <?php echo date('d/m/Y H:i', strtotime($conta['created_at'])); ?>
                    </span>
                </div>
                <?php if ($conta['updated_at']): ?>
                    <div>
                        <span class="text-gray-600">Última atualização:</span>
                        <span class="font-medium ml-2">
                            <?php echo date('d/m/Y H:i', strtotime($conta['updated_at'])); ?>
                        </span>
                    </div>
                <?php endif; ?>
                <div>
                    <span class="text-gray-600">Status atual:</span>
                    <span class="font-medium ml-2 <?php echo $conta['status'] === 'pago' ? 'text-green-600' : 'text-yellow-600'; ?>">
                        <?php echo $conta['status'] === 'pago' ? 'Pago' : 'Pendente'; ?>
                    </span>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('formContaPagar');
    const descricaoInput = document.getElementById('descricao');
    const valorInput = document.getElementById('valor');
    const dataVencimentoInput = document.getElementById('data_vencimento');
    const alertasContainer = document.getElementById('alertas-formulario');

    // Validações em tempo real
    function validarFormulario() {
        const alertas = [];
        
        // Validar descrição
        if (descricaoInput.value.trim().length < 3) {
            alertas.push({
                tipo: 'warning',
                mensagem: 'A descrição deve ter pelo menos 3 caracteres.'
            });
        }

        // Validar valor
        const valor = parseFloat(valorInput.value.replace(/[^\d,]/g, '').replace(',', '.'));
        if (isNaN(valor) || valor <= 0) {
            alertas.push({
                tipo: 'error',
                mensagem: 'Informe um valor válido maior que zero.'
            });
        }

        // Validar data de vencimento
        const dataVencimento = new Date(dataVencimentoInput.value);
        const hoje = new Date();
        const diferenciaDias = Math.ceil((dataVencimento - hoje) / (1000 * 60 * 60 * 24));
        
        if (diferenciaDias < 0) {
            alertas.push({
                tipo: 'warning',
                mensagem: 'A data de vencimento está no passado. Verifique se está correto.'
            });
        } else if (diferenciaDias > 365) {
            alertas.push({
                tipo: 'info',
                mensagem: 'Data de vencimento muito distante. Verifique se está correto.'
            });
        }

        // Exibir alertas
        exibirAlertas(alertas);
        
        return alertas.filter(a => a.tipo === 'error').length === 0;
    }

    function exibirAlertas(alertas) {
        if (alertas.length === 0) {
            alertasContainer.classList.add('hidden');
            return;
        }

        alertasContainer.classList.remove('hidden');
        alertasContainer.innerHTML = alertas.map(alerta => {
            const cores = {
                error: 'bg-red-100 border-red-200 text-red-700',
                warning: 'bg-yellow-100 border-yellow-200 text-yellow-700',
                info: 'bg-blue-100 border-blue-200 text-blue-700'
            };
            
            const icones = {
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            return `
                <div class="border rounded-lg p-3 ${cores[alerta.tipo]}">
                    <div class="flex items-center">
                        <i class="${icones[alerta.tipo]} mr-2"></i>
                        <span>${alerta.mensagem}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    // Auto-save draft (salvar rascunho)
    function salvarRascunho() {
        if (typeof(Storage) !== "undefined") {
            const dados = {
                descricao: descricaoInput.value,
                valor: valorInput.value,
                data_vencimento: dataVencimentoInput.value,
                categoria_id: document.getElementById('categoria_id').value,
                fornecedor_nome: document.getElementById('fornecedor_nome').value,
                observacoes: document.getElementById('observacoes').value
            };
            
            localStorage.setItem('rascunho_conta_pagar', JSON.stringify(dados));
        }
    }

    // Carregar rascunho
    function carregarRascunho() {
        if (typeof(Storage) !== "undefined" && !<?php echo $modo_edicao ? 'true' : 'false'; ?>) {
            const rascunho = localStorage.getItem('rascunho_conta_pagar');
            if (rascunho) {
                const dados = JSON.parse(rascunho);
                
                if (confirm('Encontramos um rascunho salvo. Deseja carregá-lo?')) {
                    descricaoInput.value = dados.descricao || '';
                    valorInput.value = dados.valor || '';
                    dataVencimentoInput.value = dados.data_vencimento || '';
                    document.getElementById('categoria_id').value = dados.categoria_id || '';
                    document.getElementById('fornecedor_nome').value = dados.fornecedor_nome || '';
                    document.getElementById('observacoes').value = dados.observacoes || '';
                } else {
                    localStorage.removeItem('rascunho_conta_pagar');
                }
            }
        }
    }

    // Sugestões inteligentes
    function configurarSugestoes() {
        // Sugestão de categoria baseada na descrição
        descricaoInput.addEventListener('blur', function() {
            const descricao = this.value.toLowerCase();
            const categoriaSelect = document.getElementById('categoria_id');
            
            if (!categoriaSelect.value && descricao) {
                // Mapeamento simples de palavras-chave para categorias
                const sugestoes = {
                    'energia': 'Energia Elétrica',
                    'água': 'Água e Saneamento',
                    'telefone': 'Telecomunicações',
                    'internet': 'Telecomunicações',
                    'aluguel': 'Aluguel',
                    'material': 'Material de Escritório',
                    'combustível': 'Combustível',
                    'manutenção': 'Manutenção'
                };

                for (const [palavra, categoria] of Object.entries(sugestoes)) {
                    if (descricao.includes(palavra)) {
                        const opcoes = categoriaSelect.options;
                        for (let i = 0; i < opcoes.length; i++) {
                            if (opcoes[i].text.includes(categoria)) {
                                categoriaSelect.value = opcoes[i].value;
                                categoriaSelect.classList.add('bg-yellow-50');
                                setTimeout(() => categoriaSelect.classList.remove('bg-yellow-50'), 2000);
                                break;
                            }
                        }
                        break;
                    }
                }
            }
        });
    }

    // Event listeners
    descricaoInput.addEventListener('input', validarFormulario);
    valorInput.addEventListener('input', validarFormulario);
    dataVencimentoInput.addEventListener('change', validarFormulario);

    // Auto-save a cada 30 segundos
    setInterval(salvarRascunho, 30000);

    // Salvar rascunho quando sair da página
    window.addEventListener('beforeunload', salvarRascunho);

    // Atalhos do teclado
    document.addEventListener('keydown', function(e) {
        // Ctrl + S = Salvar
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (validarFormulario()) {
                form.submit();
            }
        }
        
        // Ctrl + R = Limpar
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            resetarFormulario();
        }
        
        // ESC = Voltar
        if (e.key === 'Escape') {
            window.location.href = 'contas_pagar.php?acao=listar';
        }
    });

    // Validação antes do submit
    form.addEventListener('submit', function(e) {
        if (!validarFormulario()) {
            e.preventDefault();
            alert('Por favor, corrija os erros antes de continuar.');
        } else {
            // Limpar rascunho ao salvar
            localStorage.removeItem('rascunho_conta_pagar');
        }
    });

    // Inicialização
    carregarRascunho();
    configurarSugestoes();
    validarFormulario();
});

// Funções globais
function resetarFormulario() {
    if (confirm('Tem certeza que deseja limpar todos os campos?')) {
        document.getElementById('formContaPagar').reset();
        localStorage.removeItem('rascunho_conta_pagar');
        document.getElementById('alertas-formulario').classList.add('hidden');
        document.getElementById('descricao').focus();
    }
}

// Duplicar conta (se em modo edição)
<?php if ($modo_edicao): ?>
function duplicarConta() {
    if (confirm('Deseja criar uma nova conta baseada nesta?')) {
        // Remove o ID da URL para criar uma nova conta
        const url = new URL(window.location);
        url.searchParams.set('acao', 'nova');
        url.searchParams.delete('id');
        window.location.href = url.toString();
    }
}
<?php endif; ?>

// Calculadora de parcelas
function abrirCalculadoraParcelas() {
    const valor = parseFloat(document.getElementById('valor').value.replace(/[^\d,]/g, '').replace(',', '.'));
    
    if (isNaN(valor) || valor <= 0) {
        alert('Informe um valor válido primeiro.');
        return;
    }
    
    const parcelas = prompt('Em quantas parcelas deseja dividir?', '1');
    if (parcelas && !isNaN(parcelas) && parcelas > 1) {
        const valorParcela = valor / parseInt(parcelas);
        
        if (confirm(`Cada parcela ficará R$ ${valorParcela.toFixed(2).replace('.', ',')}. Deseja criar ${parcelas} contas?`)) {
            // Implementar criação de múltiplas contas
            alert('Funcionalidade em desenvolvimento!');
        }
    }
}
</script>