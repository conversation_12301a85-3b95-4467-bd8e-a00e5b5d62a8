-- ============================================================================
-- CRIAÇÃO DAS TABELAS PARA O MÓDULO DE BOLETOS
-- ============================================================================
-- 
-- Este script cria as tabelas necessárias para o funcionamento do módulo
-- de boletos do sistema financeiro
-- ============================================================================

-- Tabela de polos (se não existir)
CREATE TABLE IF NOT EXISTS polos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    telefone VARCHAR(20),
    endereco TEXT,
    cidade VARCHAR(100),
    estado VARCHAR(2),
    cep VARCHAR(10),
    cnpj VARCHAR(18),
    responsavel VARCHAR(255),
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_nome (nome),
    INDEX idx_status (status),
    INDEX idx_cnpj (cnpj)
);

-- Tabela de boletos dos polos
CREATE TABLE IF NOT EXISTS polos_boletos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    polo_id INT NOT NULL,
    numero_boleto VARCHAR(50) NOT NULL,
    nosso_numero VARCHAR(50),
    valor DECIMAL(10,2) NOT NULL,
    data_vencimento DATE NOT NULL,
    descricao TEXT,
    observacoes TEXT,
    status ENUM('pendente', 'pago', 'vencido', 'cancelado') DEFAULT 'pendente',
    nome_pagador VARCHAR(255),
    cpf_pagador VARCHAR(14),
    
    -- Campos para integração com Asaas
    asaas_payment_id VARCHAR(100),
    asaas_customer_id VARCHAR(100),
    asaas_boleto_url TEXT,
    asaas_linha_digitavel TEXT,
    
    -- Campos de auditoria
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_polo_id (polo_id),
    INDEX idx_numero_boleto (numero_boleto),
    INDEX idx_nosso_numero (nosso_numero),
    INDEX idx_data_vencimento (data_vencimento),
    INDEX idx_status (status),
    INDEX idx_asaas_payment_id (asaas_payment_id),
    INDEX idx_cpf_pagador (cpf_pagador),
    
    -- Chave estrangeira
    FOREIGN KEY (polo_id) REFERENCES polos(id) ON DELETE CASCADE
);

-- Inserir dados de exemplo para polos (se não existirem)
INSERT IGNORE INTO polos (id, nome, email, telefone, endereco, cidade, estado, cep, cnpj, responsavel) VALUES
(1, 'Polo Central', '<EMAIL>', '(11) 1234-5678', 'Rua Principal, 123', 'São Paulo', 'SP', '01234-567', '12.345.678/0001-90', 'João Silva'),
(2, 'Polo Norte', '<EMAIL>', '(11) 2345-6789', 'Av. Norte, 456', 'São Paulo', 'SP', '02345-678', '23.456.789/0001-01', 'Maria Santos'),
(3, 'Polo Sul', '<EMAIL>', '(11) 3456-7890', 'Rua Sul, 789', 'São Paulo', 'SP', '03456-789', '34.567.890/0001-12', 'Carlos Oliveira'),
(4, 'Polo Leste', '<EMAIL>', '(11) 4567-8901', 'Av. Leste, 321', 'São Paulo', 'SP', '04567-890', '45.678.901/0001-23', 'Ana Costa'),
(5, 'Polo Oeste', '<EMAIL>', '(11) 5678-9012', 'Rua Oeste, 654', 'São Paulo', 'SP', '05678-901', '56.789.012/0001-34', 'Pedro Lima');

-- Inserir alguns boletos de exemplo (se não existirem)
INSERT IGNORE INTO polos_boletos (id, polo_id, numero_boleto, nosso_numero, valor, data_vencimento, descricao, nome_pagador, cpf_pagador, status) VALUES
(1, 1, 'BOL001', '000001', 500.00, '2025-02-15', 'Mensalidade Janeiro 2025', 'João da Silva', '123.456.789-01', 'pendente'),
(2, 1, 'BOL002', '000002', 750.00, '2025-02-20', 'Taxa de Matrícula', 'Maria Oliveira', '234.567.890-12', 'pendente'),
(3, 2, 'BOL003', '000003', 600.00, '2025-02-10', 'Mensalidade Janeiro 2025', 'Carlos Santos', '345.678.901-23', 'pago'),
(4, 2, 'BOL004', '000004', 450.00, '2025-01-15', 'Mensalidade Dezembro 2024', 'Ana Costa', '456.789.012-34', 'vencido'),
(5, 3, 'BOL005', '000005', 800.00, '2025-02-25', 'Curso de Extensão', 'Pedro Lima', '567.890.123-45', 'pendente'),
(6, 3, 'BOL006', '000006', 550.00, '2025-02-12', 'Mensalidade Janeiro 2025', 'Lucia Ferreira', '678.901.234-56', 'pendente'),
(7, 4, 'BOL007', '000007', 700.00, '2025-02-18', 'Taxa de Laboratório', 'Roberto Silva', '789.012.345-67', 'pendente'),
(8, 4, 'BOL008', '000008', 500.00, '2025-01-20', 'Mensalidade Dezembro 2024', 'Fernanda Costa', '890.123.456-78', 'pago'),
(9, 5, 'BOL009', '000009', 650.00, '2025-02-22', 'Mensalidade Janeiro 2025', 'Marcos Oliveira', '901.234.567-89', 'pendente'),
(10, 5, 'BOL010', '000010', 400.00, '2025-01-10', 'Taxa de Rematrícula', 'Sandra Lima', '012.345.678-90', 'vencido');

-- ============================================================================
-- COMENTÁRIOS FINAIS
-- ============================================================================

-- Este script cria as estruturas necessárias para o módulo de boletos:
-- 
-- 1. TABELA POLOS:
--    - Armazena informações dos polos da instituição
--    - Inclui dados de contato e localização
--    - Suporte para CNPJ e responsável
-- 
-- 2. TABELA POLOS_BOLETOS:
--    - Armazena todos os boletos gerados
--    - Integração com API do Asaas (campos asaas_*)
--    - Controle de status (pendente, pago, vencido, cancelado)
--    - Dados do pagador (nome e CPF)
--    - Campos de auditoria completos
-- 
-- 3. DADOS DE EXEMPLO:
--    - 5 polos de exemplo
--    - 10 boletos de exemplo com diferentes status
--    - Dados realistas para teste
-- 
-- RELACIONAMENTOS:
-- - polos_boletos.polo_id -> polos.id (chave estrangeira)
-- 
-- ÍNDICES CRIADOS:
-- - Otimização para consultas por polo, data, status
-- - Índices para integração com Asaas
-- - Índices para busca por CPF e número do boleto
-- 
-- Execute este script no seu banco de dados MySQL/MariaDB para resolver
-- o erro "Table 'polos_boletos' doesn't exist"
