<?php
/**
 * ============================================================================
 * LOGS DO MÓDULO FINANCEIRO - ADMINISTRADOR
 * ============================================================================
 *
 * Página para visualização e análise detalhada de todos os logs
 * e atividades dos usuários no módulo financeiro.
 *
 * <AUTHOR> Faciência ERP
 * @version 1.0
 * @since 2025-07-14
 *
 * ============================================================================
 */

// Inicializa o módulo administrador
require_once __DIR__ . '/includes/init.php';

// Exige acesso administrativo
exigirAcessoAdministrador();

// ============================================================================
// PROCESSAMENTO DE FILTROS
// ============================================================================

$filtro_usuario = $_GET['filtro_usuario'] ?? '';
$filtro_acao = $_GET['filtro_acao'] ?? '';
$filtro_data_inicio = $_GET['filtro_data_inicio'] ?? '';
$filtro_data_fim = $_GET['filtro_data_fim'] ?? '';
$filtro_ip = $_GET['filtro_ip'] ?? '';
$filtro_busca = $_GET['busca'] ?? '';
$filtro_tipo_objeto = $_GET['filtro_tipo_objeto'] ?? '';
$pagina_atual = (int)($_GET['pagina'] ?? 1);
$itens_por_pagina = 50;
$offset = ($pagina_atual - 1) * $itens_por_pagina;

// Instância do banco de dados
$db = Database::getInstance();

// ============================================================================
// CONSTRUÇÃO DA QUERY COM FILTROS
// ============================================================================

$where = ["l.modulo = 'financeiro'"];
$params = [];

if ($filtro_usuario) {
    $where[] = "l.usuario_id = ?";
    $params[] = $filtro_usuario;
}

if ($filtro_acao) {
    $where[] = "l.acao = ?";
    $params[] = $filtro_acao;
}

if ($filtro_data_inicio) {
    $where[] = "l.created_at >= ?";
    $params[] = $filtro_data_inicio . ' 00:00:00';
}

if ($filtro_data_fim) {
    $where[] = "l.created_at <= ?";
    $params[] = $filtro_data_fim . ' 23:59:59';
}

if ($filtro_ip) {
    $where[] = "l.ip_origem = ?";
    $params[] = $filtro_ip;
}

if ($filtro_tipo_objeto) {
    $where[] = "l.objeto_tipo = ?";
    $params[] = $filtro_tipo_objeto;
}

if ($filtro_busca) {
    $where[] = "(l.descricao LIKE ? OR l.dados_novos LIKE ? OR u.nome LIKE ?)";
    $params[] = "%{$filtro_busca}%";
    $params[] = "%{$filtro_busca}%";
    $params[] = "%{$filtro_busca}%";
}

$where_clause = 'WHERE ' . implode(' AND ', $where);

// ============================================================================
// CARREGAMENTO DE DADOS
// ============================================================================

// Conta o total de logs
$sql_count = "SELECT COUNT(*) as total FROM logs_sistema l 
              LEFT JOIN usuarios u ON l.usuario_id = u.id 
              {$where_clause}";
$total_registros = $db->fetchOne($sql_count, $params)['total'] ?? 0;
$total_paginas = ceil($total_registros / $itens_por_pagina);

// Busca os logs
$sql_logs = "SELECT l.*, u.nome as usuario_nome, u.email as usuario_email
             FROM logs_sistema l
             LEFT JOIN usuarios u ON l.usuario_id = u.id
             {$where_clause}
             ORDER BY l.created_at DESC
             LIMIT {$itens_por_pagina} OFFSET {$offset}";

$logs = $db->fetchAll($sql_logs, $params);

// Busca dados para os filtros
$usuarios_financeiro = $db->fetchAll("
    SELECT DISTINCT u.id, u.nome 
    FROM usuarios u 
    INNER JOIN logs_sistema l ON u.id = l.usuario_id 
    WHERE l.modulo = 'financeiro' 
    ORDER BY u.nome
");

$acoes_financeiro = $db->fetchAll("
    SELECT DISTINCT acao 
    FROM logs_sistema 
    WHERE modulo = 'financeiro' 
    ORDER BY acao
");

$tipos_objeto = $db->fetchAll("
    SELECT DISTINCT objeto_tipo 
    FROM logs_sistema 
    WHERE modulo = 'financeiro' AND objeto_tipo IS NOT NULL 
    ORDER BY objeto_tipo
");

// Estatísticas rápidas
$stats = $db->fetchOne("
    SELECT 
        COUNT(*) as total_logs,
        COUNT(DISTINCT usuario_id) as usuarios_ativos,
        COUNT(DISTINCT DATE(created_at)) as dias_atividade,
        COUNT(CASE WHEN acao LIKE 'crud_%' THEN 1 END) as operacoes_crud,
        COUNT(CASE WHEN acao = 'acesso_pagina' THEN 1 END) as acessos_pagina,
        COUNT(CASE WHEN acao = 'uso_filtros' THEN 1 END) as uso_filtros,
        COUNT(CASE WHEN acao = 'busca' THEN 1 END) as buscas_realizadas
    FROM logs_sistema 
    WHERE modulo = 'financeiro' 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
");

// Registra acesso a esta página
registrarAcaoAdministrativa(
    'logs_financeiro_acesso',
    'Acesso aos logs do módulo financeiro',
    ['filtros' => array_filter($_GET)]
);

// Define o título da página
$titulo_pagina = 'Logs do Módulo Financeiro';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo htmlspecialchars($titulo_pagina); ?></title>

    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --admin-primary: #DC2626;
            --admin-primary-dark: #B91C1C;
        }

        .admin-nav {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
        }

        .admin-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.1);
        }

        .log-row:hover {
            background-color: #FEF2F2;
        }

        .log-details {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .log-details.expanded {
            max-height: 500px;
        }

        .acao-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .acao-acesso_pagina { background: #DBEAFE; color: #1E40AF; }
        .acao-crud_create { background: #D1FAE5; color: #065F46; }
        .acao-crud_update { background: #FEF3C7; color: #92400E; }
        .acao-crud_delete { background: #FEE2E2; color: #991B1B; }
        .acao-uso_filtros { background: #E0E7FF; color: #3730A3; }
        .acao-busca { background: #F3F4F6; color: #374151; }
        .acao-erro_acesso { background: #FEE2E2; color: #991B1B; }
        .acao-exportacao { background: #ECFDF5; color: #14532D; }
        .acao-default { background: #F3F4F6; color: #4B5563; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="admin-nav text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-content-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-shield-alt text-2xl mr-3"></i>
                        <h1 class="text-xl font-bold">Administração</h1>
                    </a>
                </div>

                <nav class="hidden md:flex space-x-1">
                    <a href="index.php" class="px-3 py-2 rounded text-white hover:bg-white hover:bg-opacity-10">Dashboard</a>
                    <a href="usuarios.php" class="px-3 py-2 rounded text-white hover:bg-white hover:bg-opacity-10">Usuários</a>
                    <a href="logs.php" class="px-3 py-2 rounded text-white hover:bg-white hover:bg-opacity-10">Logs</a>
                    <a href="logs_financeiro.php" class="px-3 py-2 rounded bg-white bg-opacity-20 text-white">Logs Financeiro</a>
                    <a href="configuracoes.php" class="px-3 py-2 rounded text-white hover:bg-white hover:bg-opacity-10">Config</a>
                </nav>

                <div class="flex items-center space-x-4">
                    <span class="text-sm"><?php echo htmlspecialchars($_SESSION['user_name'] ?? $_SESSION['user']['nome'] ?? 'Usuário'); ?></span>
                    <a href="../logout.php" class="text-white hover:text-gray-200">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Cabeçalho -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900">Logs do Módulo Financeiro</h2>
                    <p class="mt-2 text-gray-600">Monitoramento completo das atividades dos usuários no módulo financeiro</p>
                </div>
                <button onclick="location.reload()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Atualizar
                </button>
            </div>
        </div>

        <!-- Estatísticas Rápidas -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Total de Logs -->
            <div class="admin-card p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total de Logs (30 dias)</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_logs'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <!-- Usuários Ativos -->
            <div class="admin-card p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Usuários Ativos</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['usuarios_ativos'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <!-- Operações CRUD -->
            <div class="admin-card p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-edit text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Operações CRUD</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['operacoes_crud'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <!-- Acessos às Páginas -->
            <div class="admin-card p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-eye text-2xl text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Acessos às Páginas</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['acessos_pagina'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="admin-card p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-filter mr-2"></i>
                Filtros
            </h3>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div>
                    <label for="filtro_usuario" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-user mr-1"></i>
                        Usuário
                    </label>
                    <select id="filtro_usuario" name="filtro_usuario" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500">
                        <option value="">Todos os usuários</option>
                        <?php foreach ($usuarios_financeiro as $usuario): ?>
                            <option value="<?php echo $usuario['id']; ?>"
                                    <?php echo $filtro_usuario == $usuario['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($usuario['nome']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="filtro_acao" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-cog mr-1"></i>
                        Ação
                    </label>
                    <select id="filtro_acao" name="filtro_acao" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500">
                        <option value="">Todas as ações</option>
                        <?php foreach ($acoes_financeiro as $acao): ?>
                            <option value="<?php echo $acao['acao']; ?>"
                                    <?php echo $filtro_acao == $acao['acao'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($acao['acao']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="filtro_data_inicio" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-calendar mr-1"></i>
                        Data Início
                    </label>
                    <input type="date" id="filtro_data_inicio" name="filtro_data_inicio"
                           value="<?php echo htmlspecialchars($filtro_data_inicio); ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500">
                </div>

                <div>
                    <label for="filtro_data_fim" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-calendar mr-1"></i>
                        Data Fim
                    </label>
                    <input type="date" id="filtro_data_fim" name="filtro_data_fim"
                           value="<?php echo htmlspecialchars($filtro_data_fim); ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500">
                </div>

                <div>
                    <label for="filtro_tipo_objeto" class="block text-sm font-medium text-gray-700 mb-1">
                        <i class="fas fa-tag mr-1"></i>
                        Tipo de Objeto
                    </label>
                    <select id="filtro_tipo_objeto" name="filtro_tipo_objeto" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500">
                        <option value="">Todos os tipos</option>
                        <?php foreach ($tipos_objeto as $tipo): ?>
                            <option value="<?php echo $tipo['objeto_tipo']; ?>"
                                    <?php echo $filtro_tipo_objeto == $tipo['objeto_tipo'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($tipo['objeto_tipo']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md mr-2">
                        <i class="fas fa-filter mr-1"></i>
                        Filtrar
                    </button>
                    <a href="logs_financeiro.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                        <i class="fas fa-times mr-1"></i>
                        Limpar
                    </a>
                </div>
            </form>

            <!-- Busca Geral -->
            <div class="mt-4">
                <form method="GET" class="flex">
                    <?php foreach ($_GET as $key => $value): ?>
                        <?php if ($key !== 'busca'): ?>
                            <input type="hidden" name="<?php echo htmlspecialchars($key); ?>" value="<?php echo htmlspecialchars($value); ?>">
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <div class="flex-1">
                        <input type="text" name="busca" placeholder="Buscar em descrições, dados ou usuários..."
                               value="<?php echo htmlspecialchars($filtro_busca); ?>"
                               class="w-full border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-r-md">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>

        <!-- Lista de Logs -->
        <div class="admin-card">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list mr-2"></i>
                    Logs de Atividade
                    <span class="ml-2 bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded">
                        <?php echo number_format($total_registros); ?> registros
                    </span>
                </h3>
            </div>

            <?php if (empty($logs)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-inbox text-6xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum log encontrado</h3>
                    <p class="text-gray-500">Tente ajustar os filtros ou verificar se há atividade no módulo financeiro.</p>
                </div>
            <?php else: ?>
                <div class="divide-y divide-gray-200">
                    <?php foreach ($logs as $log): ?>
                        <?php
                        // Decodifica dados JSON
                        $dados_novos = null;
                        $dados_antigos = null;
                        if ($log['dados_novos']) {
                            $dados_novos = json_decode($log['dados_novos'], true);
                        }
                        if ($log['dados_antigos']) {
                            $dados_antigos = json_decode($log['dados_antigos'], true);
                        }

                        // Formata a ação para exibição
                        $acao_formatada = ucwords(str_replace('_', ' ', $log['acao']));

                        // Define classe do badge baseado na ação
                        $badge_class = 'acao-default';
                        if (strpos($log['acao'], 'crud_create') !== false) $badge_class = 'acao-crud_create';
                        elseif (strpos($log['acao'], 'crud_update') !== false) $badge_class = 'acao-crud_update';
                        elseif (strpos($log['acao'], 'crud_delete') !== false) $badge_class = 'acao-crud_delete';
                        elseif ($log['acao'] === 'erro_acesso') $badge_class = 'acao-erro_acesso';
                        elseif ($log['acao'] === 'acesso_pagina') $badge_class = 'acao-acesso_pagina';
                        elseif ($log['acao'] === 'uso_filtros') $badge_class = 'acao-uso_filtros';
                        elseif ($log['acao'] === 'busca') $badge_class = 'acao-busca';
                        elseif ($log['acao'] === 'exportacao') $badge_class = 'acao-exportacao';
                        ?>

                        <div class="log-row px-6 py-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="acao-badge <?php echo $badge_class; ?> mr-2">
                                            <?php echo htmlspecialchars($acao_formatada); ?>
                                        </span>

                                        <?php if ($log['objeto_tipo']): ?>
                                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded mr-2">
                                                <?php echo htmlspecialchars($log['objeto_tipo']); ?>
                                                <?php if ($log['objeto_id']): ?>
                                                    #<?php echo $log['objeto_id']; ?>
                                                <?php endif; ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <h4 class="text-sm font-medium text-gray-900 mb-1">
                                        <?php echo htmlspecialchars($log['descricao']); ?>
                                    </h4>

                                    <div class="text-xs text-gray-500 space-x-4">
                                        <span>
                                            <i class="fas fa-user mr-1"></i>
                                            <strong><?php echo htmlspecialchars($log['usuario_nome'] ?? 'Sistema'); ?></strong>
                                        </span>

                                        <span>
                                            <i class="fas fa-clock mr-1"></i>
                                            <?php echo date('d/m/Y H:i:s', strtotime($log['created_at'])); ?>
                                        </span>

                                        <?php if ($log['ip_origem']): ?>
                                            <span>
                                                <i class="fas fa-globe mr-1"></i>
                                                <?php echo htmlspecialchars($log['ip_origem']); ?>
                                            </span>
                                        <?php endif; ?>

                                        <?php if ($log['dispositivo']): ?>
                                            <span>
                                                <i class="fas fa-mobile-alt mr-1"></i>
                                                <?php echo htmlspecialchars($log['dispositivo']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <?php if ($dados_novos || $dados_antigos): ?>
                                        <div class="mt-2">
                                            <button onclick="toggleLogDetails('log-<?php echo $log['id']; ?>')"
                                                    class="text-red-600 hover:text-red-800 text-xs">
                                                <i class="fas fa-chevron-down mr-1"></i>
                                                Ver detalhes
                                            </button>
                                        </div>

                                        <div id="log-<?php echo $log['id']; ?>" class="log-details mt-3">
                                            <div class="bg-gray-50 rounded-lg p-3">
                                                <?php if ($dados_antigos): ?>
                                                    <h5 class="text-sm font-medium text-gray-900 mb-2">
                                                        <i class="fas fa-history mr-1"></i>Dados Anteriores:
                                                    </h5>
                                                    <pre class="bg-gray-800 text-green-400 p-3 rounded text-xs overflow-x-auto mb-3"><?php echo htmlspecialchars(json_encode($dados_antigos, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                                                <?php endif; ?>

                                                <?php if ($dados_novos): ?>
                                                    <h5 class="text-sm font-medium text-gray-900 mb-2">
                                                        <i class="fas fa-plus mr-1"></i>Dados Novos:
                                                    </h5>
                                                    <pre class="bg-gray-800 text-green-400 p-3 rounded text-xs overflow-x-auto"><?php echo htmlspecialchars(json_encode($dados_novos, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Paginação -->
        <?php if ($total_paginas > 1): ?>
            <div class="flex justify-center mt-8">
                <nav class="flex items-center space-x-2">
                    <!-- Primeira página -->
                    <?php if ($pagina_atual > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['pagina' => 1])); ?>"
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Página anterior -->
                    <?php if ($pagina_atual > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['pagina' => $pagina_atual - 1])); ?>"
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Páginas numeradas -->
                    <?php
                    $inicio = max(1, $pagina_atual - 2);
                    $fim = min($total_paginas, $pagina_atual + 2);

                    for ($i = $inicio; $i <= $fim; $i++):
                    ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['pagina' => $i])); ?>"
                           class="px-3 py-2 text-sm border rounded-md <?php echo $i == $pagina_atual ? 'bg-red-600 text-white border-red-600' : 'text-gray-500 hover:text-gray-700 border-gray-300 hover:bg-gray-50'; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <!-- Próxima página -->
                    <?php if ($pagina_atual < $total_paginas): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['pagina' => $pagina_atual + 1])); ?>"
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Última página -->
                    <?php if ($pagina_atual < $total_paginas): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['pagina' => $total_paginas])); ?>"
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>
            </div>

            <div class="text-center text-gray-500 text-sm mt-4">
                Página <?php echo $pagina_atual; ?> de <?php echo $total_paginas; ?>
                (<?php echo number_format($total_registros); ?> registros no total)
            </div>
        <?php endif; ?>

    </div>

    <!-- Scripts -->
    <script>
        // Função para expandir/recolher detalhes dos logs
        function toggleLogDetails(id) {
            const elemento = document.getElementById(id);
            const button = elemento.previousElementSibling.querySelector('button');
            const icone = button.querySelector('i');

            if (elemento.classList.contains('expanded')) {
                elemento.classList.remove('expanded');
                icone.className = 'fas fa-chevron-down mr-1';
                button.innerHTML = '<i class="fas fa-chevron-down mr-1"></i>Ver detalhes';
            } else {
                elemento.classList.add('expanded');
                icone.className = 'fas fa-chevron-up mr-1';
                button.innerHTML = '<i class="fas fa-chevron-up mr-1"></i>Ocultar detalhes';
            }
        }

        // Auto-refresh da página a cada 30 segundos (opcional)
        // setInterval(function() {
        //     location.reload();
        // }, 30000);

        // Função para exportar logs (futura implementação)
        function exportarLogs(formato) {
            const params = new URLSearchParams(window.location.search);
            params.set('exportar', formato);
            window.location.href = '?' + params.toString();
        }

        // Função para limpar filtros
        function limparFiltros() {
            window.location.href = 'logs_financeiro.php';
        }
    </script>

</body>
</html>
