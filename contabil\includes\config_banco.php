<?php
/**
 * Configuração do Banco de Dados - Módulo <PERSON>iro
 * Credenciais corretas para produção e desenvolvimento
 */

// Detectar ambiente
function detectarAmbiente() {
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return (strpos($host, 'localhost') === false && strpos($host, '127.0.0.1') === false);
}

// Configurações do banco de dados
function getConfigBanco() {
    $is_producao = detectarAmbiente();
    
    if ($is_producao) {
        // PRODUÇÃO - Credenciais corretas
        return [
            'host' => 'localhost',
            'username' => 'u682219090_faciencia_erp',
            'password' => 'T3cn0l0g1a@',
            'database' => 'u682219090_faciencia_erp',
            'charset' => 'utf8mb4',
            'ambiente' => 'PRODUÇÃO'
        ];
    } else {
        // LOCAL - Desenvolvimento
        return [
            'host' => 'localhost',
            'username' => 'root',
            'password' => '',
            'database' => 'u682219090_faciencia_erp',
            'charset' => 'utf8mb4',
            'ambiente' => 'LOCAL'
        ];
    }
}

// Função para conectar ao banco
function conectarBanco() {
    $config = getConfigBanco();
    
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
        ]);
        
        return [
            'sucesso' => true,
            'pdo' => $pdo,
            'config' => $config
        ];
    } catch (PDOException $e) {
        return [
            'sucesso' => false,
            'erro' => $e->getMessage(),
            'config' => $config
        ];
    }
}

// Função para testar conexão
function testarConexao() {
    $resultado = conectarBanco();
    
    if ($resultado['sucesso']) {
        try {
            // Testar uma query simples
            $stmt = $resultado['pdo']->query("SELECT 1 as teste, NOW() as agora");
            $teste = $stmt->fetch();
            
            if ($teste && $teste['teste'] == 1) {
                return [
                    'sucesso' => true,
                    'ambiente' => $resultado['config']['ambiente'],
                    'database' => $resultado['config']['database'],
                    'username' => $resultado['config']['username'],
                    'timestamp' => $teste['agora'],
                    'pdo' => $resultado['pdo']
                ];
            } else {
                return [
                    'sucesso' => false,
                    'erro' => 'Query de teste falhou'
                ];
            }
        } catch (PDOException $e) {
            return [
                'sucesso' => false,
                'erro' => 'Erro na query de teste: ' . $e->getMessage()
            ];
        }
    } else {
        return $resultado;
    }
}

// Constantes úteis
define('DB_CHARSET', 'utf8mb4');
define('DB_TIMEOUT', 30);

// Log de conexões (apenas em desenvolvimento)
if (!detectarAmbiente() && defined('DEBUG') && DEBUG === true) {
    error_log("Módulo Financeiro - Configuração de banco carregada");
}
?>
