[{"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência da tabela: Contas a Receber", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência da tabela: Contas a Pagar", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência da tabela: Categorias Financeiras", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência da tabela: Transações Financeiras", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência da tabela: Contas Bancárias", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência da tabela: Boletos", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência da tabela: Configurações Asaas", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar estrutura da tabela contas_receber", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar enum tipo em categorias_financeiras", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar existência de categorias financeiras", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar contas a receber sem categoria", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar valores negativos em contas a receber", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar datas de recebimento futuras", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Testar consulta principal de contas a receber", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Testar filtro por status: pendente", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Testar filtro por status: recebido", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Testar filtro por status: cancelado", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Testar filtro por período de datas", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Testar busca por texto", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar arquivo: View de listagem", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar arquivo: View de formulário", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar arquivo: View de recebimento", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar arquivo: Sidebar do módulo", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "calculo", "mensagem": "Total pendente: R$ 5.500,00", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Calcular total de contas a receber pendentes", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "calculo", "mensagem": "Total recebido no mês: R$ 5.850,00", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Calcular total recebido no mês atual", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "aviso", "categoria": "calculo", "mensagem": "Contas vencidas: 1 contas, R$ 5.000,00", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Calcular contas vencidas", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "distribuicao", "mensagem": "Mensalidade: 3 contas, R$ 10.850,00", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "distribuicao", "mensagem": "Outros Serviços: 1 contas, R$ 500,00", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar distribuição por categoria", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "aviso", "categoria": "teste", "mensagem": "Verificar índices da tabela contas_receber", "detalhes": "Índices faltando: status, data_vencimento"}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar índices da tabela categorias_financeiras", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar índices da tabela transacoes_financeiras", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "performance", "mensagem": "Consulta executada em 0.002 segundos", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Testar performance de consulta complexa", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar sistema de autenticação", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar função de permissões", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar proteção contra SQL Injection", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "sucesso", "categoria": "teste", "mensagem": "Verificar integração com usuários", "detalhes": null}, {"timestamp": "2025-07-14 14:16:08", "tipo": "aviso", "categoria": "teste", "mensagem": "Verificar sincronização com transações financeiras", "detalhes": "2 contas recebidas sem transação correspondente"}]