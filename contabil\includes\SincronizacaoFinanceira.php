<?php
/**
 * ============================================================================
 * SINCRONIZAÇÃO FINANCEIRA COMPLETA - SISTEMA FACIÊNCIA ERP
 * ============================================================================
 *
 * Esta classe é responsável por manter a sincronização completa entre:
 * - Boletos Asaas
 * - Contas a Receber
 * - Transações Financeiras
 * - Saldos Bancários
 * - Caixa
 *
 * <AUTHOR> Faciência ERP
 * @version 1.0
 * @since 2025
 */

class SincronizacaoFinanceira {
    private $db;
    private $asaasAPI;
    
    public function __construct($db, $asaasAPI = null) {
        $this->db = $db;
        $this->asaasAPI = $asaasAPI;
    }
    
    /**
     * Sincronizar pagamento de boleto Asaas com todo o sistema financeiro
     */
    public function sincronizarPagamentoBoleto($boleto_id, $dados_pagamento_asaas = null) {
        try {
            $this->db->beginTransaction();
            
            // 1. Buscar dados do boleto
            $boleto = $this->db->fetchOne("
                SELECT pb.*, p.nome as polo_nome, p.responsavel 
                FROM polos_boletos pb
                JOIN polos p ON pb.polo_id = p.id
                WHERE pb.id = ?
            ", [$boleto_id]);
            
            if (!$boleto) {
                throw new Exception("Boleto não encontrado: {$boleto_id}");
            }
            
            // 2. Se não temos dados do Asaas, buscar
            if (!$dados_pagamento_asaas && $boleto['asaas_payment_id']) {
                if (!$this->asaasAPI) {
                    require_once __DIR__ . '/AsaasAPI.php';
                    $this->asaasAPI = new AsaasAPI($this->db);
                }
                $dados_pagamento_asaas = $this->asaasAPI->buscarCobranca($boleto['asaas_payment_id']);
            }
            
            // 3. Determinar se foi pago
            $foi_pago = false;
            $data_pagamento = null;
            $valor_pago = $boleto['valor'];
            
            if ($dados_pagamento_asaas) {
                $foi_pago = in_array($dados_pagamento_asaas['status'], ['RECEIVED', 'CONFIRMED']);
                $data_pagamento = $dados_pagamento_asaas['paymentDate'] ?? null;
                $valor_pago = $dados_pagamento_asaas['value'] ?? $boleto['valor'];
            }
            
            if ($foi_pago) {
                // 4. Atualizar boleto
                $this->atualizarBoleto($boleto_id, 'pago', $data_pagamento, $valor_pago, $dados_pagamento_asaas);
                
                // 5. Criar/Atualizar conta a receber
                $conta_receber_id = $this->criarOuAtualizarContaReceber($boleto, $data_pagamento, $valor_pago);
                
                // 6. Criar transação financeira
                $transacao_id = $this->criarTransacaoFinanceira($boleto, $conta_receber_id, $data_pagamento, $valor_pago);
                
                // 7. Atualizar saldo bancário
                $this->atualizarSaldoBancario($transacao_id, $data_pagamento, $valor_pago);
                
                // 8. Registrar no caixa
                $this->registrarMovimentacaoCaixa($boleto, $data_pagamento, $valor_pago, $transacao_id);
                
                // 9. Vincular boleto com conta a receber e transação
                $this->vincularBoletoSistemaFinanceiro($boleto_id, $conta_receber_id, $transacao_id);
            }
            
            $this->db->commit();
            
            return [
                'sucesso' => true,
                'foi_pago' => $foi_pago,
                'valor_pago' => $valor_pago,
                'data_pagamento' => $data_pagamento,
                'conta_receber_id' => $conta_receber_id ?? null,
                'transacao_id' => $transacao_id ?? null
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Erro na sincronização financeira: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Atualizar dados do boleto
     */
    private function atualizarBoleto($boleto_id, $status, $data_pagamento, $valor_pago, $dados_asaas) {
        $sql = "UPDATE polos_boletos 
                SET status = ?, data_pagamento = ?, valor_pago = ?, 
                    asaas_status = ?, updated_at = NOW()
                WHERE id = ?";
        
        $this->db->query($sql, [
            $status,
            $data_pagamento,
            $valor_pago,
            $dados_asaas['status'] ?? null,
            $boleto_id
        ]);
    }
    
    /**
     * Criar ou atualizar conta a receber
     */
    private function criarOuAtualizarContaReceber($boleto, $data_pagamento, $valor_pago) {
        // Verificar se já existe conta a receber para este boleto
        $conta_existente = $this->db->fetchOne("
            SELECT id FROM contas_receber 
            WHERE descricao LIKE ? AND valor = ? AND data_vencimento = ?
        ", [
            "%{$boleto['numero_boleto']}%",
            $boleto['valor'],
            $boleto['data_vencimento']
        ]);
        
        if ($conta_existente) {
            // Atualizar existente
            $this->db->query("
                UPDATE contas_receber 
                SET status = 'recebido', data_recebimento = ?, forma_recebimento = 'boleto_asaas',
                    observacoes = CONCAT(COALESCE(observacoes, ''), '\n[ASAAS] Pago via Asaas em ', ?),
                    updated_at = NOW()
                WHERE id = ?
            ", [$data_pagamento, $data_pagamento, $conta_existente['id']]);
            
            return $conta_existente['id'];
        } else {
            // Criar nova
            $this->db->query("
                INSERT INTO contas_receber 
                (descricao, valor, data_vencimento, data_recebimento, cliente_nome, 
                 forma_recebimento, status, observacoes, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, 'boleto_asaas', 'recebido', ?, NOW(), NOW())
            ", [
                "Boleto {$boleto['numero_boleto']} - {$boleto['descricao']}",
                $valor_pago,
                $boleto['data_vencimento'],
                $data_pagamento,
                $boleto['responsavel'] ?: $boleto['polo_nome'],
                "Pagamento via Asaas - Boleto: {$boleto['numero_boleto']}"
            ]);
            
            return $this->db->lastInsertId();
        }
    }
    
    /**
     * Criar transação financeira
     */
    private function criarTransacaoFinanceira($boleto, $conta_receber_id, $data_pagamento, $valor_pago) {
        // Buscar conta bancária padrão para Asaas
        $conta_bancaria = $this->db->fetchOne("
            SELECT id FROM contas_bancarias 
            WHERE nome LIKE '%asaas%' OR nome LIKE '%boleto%' 
            ORDER BY id LIMIT 1
        ");
        
        if (!$conta_bancaria) {
            // Usar conta corrente principal
            $conta_bancaria = $this->db->fetchOne("
                SELECT id FROM contas_bancarias 
                WHERE tipo = 'corrente' AND status = 'ativo' 
                ORDER BY id LIMIT 1
            ");
        }
        
        $conta_bancaria_id = $conta_bancaria['id'] ?? 2; // Fallback para ID 2
        
        $this->db->query("
            INSERT INTO transacoes_financeiras 
            (tipo, descricao, valor, data_transacao, conta_bancaria_id, 
             referencia_tipo, referencia_id, status, forma_pagamento, 
             observacoes, usuario_id, created_at, updated_at)
            VALUES ('receita', ?, ?, ?, ?, 'conta_receber', ?, 'efetivada', 'boleto_asaas', ?, ?, NOW(), NOW())
        ", [
            "Recebimento Boleto Asaas - {$boleto['numero_boleto']}",
            $valor_pago,
            $data_pagamento,
            $conta_bancaria_id,
            $conta_receber_id,
            "Pagamento via Asaas - Boleto: {$boleto['numero_boleto']}",
            $_SESSION['user_id'] ?? 1
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Atualizar saldo bancário
     */
    private function atualizarSaldoBancario($transacao_id, $data_pagamento, $valor_pago) {
        // Buscar dados da transação
        $transacao = $this->db->fetchOne("
            SELECT conta_bancaria_id FROM transacoes_financeiras WHERE id = ?
        ", [$transacao_id]);
        
        if ($transacao) {
            // Atualizar saldo atual da conta
            $this->db->query("
                UPDATE contas_bancarias 
                SET saldo_atual = saldo_atual + ?, 
                    data_saldo = ?, 
                    data_ultima_sincronizacao = NOW(),
                    updated_at = NOW()
                WHERE id = ?
            ", [$valor_pago, $data_pagamento, $transacao['conta_bancaria_id']]);
            
            // Registrar no controle de saldos diários
            $this->registrarSaldoDiario($transacao['conta_bancaria_id'], $data_pagamento, $valor_pago);
        }
    }
    
    /**
     * Registrar saldo diário
     */
    private function registrarSaldoDiario($conta_bancaria_id, $data, $valor) {
        // Verificar se já existe registro para o dia
        $saldo_dia = $this->db->fetchOne("
            SELECT * FROM saldos_contas_bancarias 
            WHERE conta_bancaria_id = ? AND data_saldo = ?
        ", [$conta_bancaria_id, $data]);
        
        if ($saldo_dia) {
            // Atualizar existente
            $this->db->query("
                UPDATE saldos_contas_bancarias 
                SET total_entradas = total_entradas + ?,
                    saldo_final_dia = saldo_inicial_dia + total_entradas - total_saidas,
                    updated_at = NOW()
                WHERE id = ?
            ", [$valor, $saldo_dia['id']]);
        } else {
            // Buscar saldo anterior
            $saldo_anterior = $this->db->fetchOne("
                SELECT saldo_final_dia FROM saldos_contas_bancarias 
                WHERE conta_bancaria_id = ? AND data_saldo < ? 
                ORDER BY data_saldo DESC LIMIT 1
            ", [$conta_bancaria_id, $data]);
            
            $saldo_inicial = $saldo_anterior['saldo_final_dia'] ?? 0;
            
            // Criar novo registro
            $this->db->query("
                INSERT INTO saldos_contas_bancarias 
                (conta_bancaria_id, data_saldo, saldo_inicial_dia, total_entradas, 
                 total_saidas, saldo_final_dia, created_at, updated_at)
                VALUES (?, ?, ?, ?, 0, ?, NOW(), NOW())
            ", [
                $conta_bancaria_id, 
                $data, 
                $saldo_inicial, 
                $valor, 
                $saldo_inicial + $valor
            ]);
        }
    }
    
    /**
     * Registrar movimentação no caixa
     */
    private function registrarMovimentacaoCaixa($boleto, $data_pagamento, $valor_pago, $transacao_id) {
        // Verificar se existe tabela de movimentações de caixa
        $tabelas = $this->db->fetchAll("SHOW TABLES LIKE 'movimentacoes_caixa'");
        
        if (!empty($tabelas)) {
            $this->db->query("
                INSERT INTO movimentacoes_caixa 
                (tipo, descricao, valor, data_movimentacao, categoria, 
                 referencia_tipo, referencia_id, transacao_financeira_id, 
                 usuario_id, created_at)
                VALUES ('entrada', ?, ?, ?, 'boletos', 'boleto_asaas', ?, ?, ?, NOW())
            ", [
                "Recebimento Boleto Asaas - {$boleto['numero_boleto']}",
                $valor_pago,
                $data_pagamento,
                $boleto['id'],
                $transacao_id,
                $_SESSION['user_id'] ?? 1
            ]);
        }
    }
    
    /**
     * Vincular boleto com sistema financeiro
     */
    private function vincularBoletoSistemaFinanceiro($boleto_id, $conta_receber_id, $transacao_id) {
        // Adicionar campos de vinculação se não existirem
        $colunas = $this->db->fetchAll("SHOW COLUMNS FROM polos_boletos LIKE '%financeira%'");
        
        if (empty($colunas)) {
            $this->db->query("
                ALTER TABLE polos_boletos 
                ADD COLUMN conta_receber_id INT(11) NULL,
                ADD COLUMN transacao_financeira_id INT(11) NULL
            ");
        }
        
        // Atualizar vínculos
        $this->db->query("
            UPDATE polos_boletos 
            SET conta_receber_id = ?, transacao_financeira_id = ?, updated_at = NOW()
            WHERE id = ?
        ", [$conta_receber_id, $transacao_id, $boleto_id]);
    }
    
    /**
     * Sincronizar todos os boletos Asaas pendentes
     */
    public function sincronizarTodosBoletos() {
        $boletos_asaas = $this->db->fetchAll("
            SELECT id, asaas_payment_id, status 
            FROM polos_boletos 
            WHERE asaas_payment_id IS NOT NULL 
            AND status IN ('pendente', 'processando')
            ORDER BY created_at DESC
        ");
        
        $resultados = [
            'total_verificados' => count($boletos_asaas),
            'atualizados' => 0,
            'pagos' => 0,
            'erros' => []
        ];
        
        foreach ($boletos_asaas as $boleto) {
            try {
                $resultado = $this->sincronizarPagamentoBoleto($boleto['id']);
                
                if ($resultado['sucesso']) {
                    $resultados['atualizados']++;
                    if ($resultado['foi_pago']) {
                        $resultados['pagos']++;
                    }
                }
                
            } catch (Exception $e) {
                $resultados['erros'][] = "Boleto {$boleto['id']}: " . $e->getMessage();
            }
        }
        
        return $resultados;
    }
}
