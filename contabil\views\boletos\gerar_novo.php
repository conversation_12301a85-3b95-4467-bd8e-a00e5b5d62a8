<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G<PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">

<div class="container mx-auto p-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-plus text-blue-500 mr-2"></i>
                Gerar Novo Boleto - TESTE
            </h3>
        </div>

        <form method="POST" class="p-6">
            <!-- Tipo de Boleto -->
            <div class="mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Tipo de Boleto</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="tipo_entidade" value="polo" class="mr-3" onchange="mostrarCampos('polo')" checked>
                        <div>
                            <div class="font-medium text-gray-900">Boleto para Polo</div>
                            <div class="text-sm text-gray-500">Gerar boleto para polo de ensino</div>
                        </div>
                    </label>
                    
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="tipo_entidade" value="aluno" class="mr-3" onchange="mostrarCampos('aluno')">
                        <div>
                            <div class="font-medium text-gray-900">Boleto para Aluno</div>
                            <div class="text-sm text-gray-500">Gerar boleto para mensalidade ou taxa de aluno</div>
                        </div>
                    </label>
                    
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="tipo_entidade" value="avulso" class="mr-3" onchange="mostrarCampos('avulso')">
                        <div>
                            <div class="font-medium text-gray-900">Boleto Avulso</div>
                            <div class="text-sm text-gray-500">Gerar boleto para pessoa física ou jurídica</div>
                        </div>
                    </label>
                </div>
                
                <!-- Botões de teste -->
                <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p class="text-sm text-yellow-800 mb-2">Teste direto:</p>
                    <button type="button" onclick="mostrarCampos('polo')" class="bg-blue-500 text-white px-3 py-1 rounded mr-2">Polo</button>
                    <button type="button" onclick="mostrarCampos('aluno')" class="bg-green-500 text-white px-3 py-1 rounded mr-2">Aluno</button>
                    <button type="button" onclick="mostrarCampos('avulso')" class="bg-red-500 text-white px-3 py-1 rounded">Avulso</button>
                </div>
            </div>

            <!-- Campos Dinâmicos -->
            <div class="mb-6">
                <!-- Campo Polo -->
                <div id="campo-polo" class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Polo <span class="text-red-500">*</span>
                    </label>
                    <select name="polo_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Selecione um polo...</option>
                        <option value="1">Polo São Paulo</option>
                        <option value="2">Polo Rio de Janeiro</option>
                        <option value="3">Polo Belo Horizonte</option>
                    </select>
                </div>

                <!-- Campo Aluno -->
                <div id="campo-aluno" class="mb-4" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Aluno <span class="text-red-500">*</span>
                    </label>
                    <input type="text" placeholder="Digite o nome do aluno para buscar..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Digite pelo menos 3 caracteres para buscar</p>
                </div>

                <!-- Campo Mensalidade -->
                <div id="campo-mensalidade" class="mb-4" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Mensalidade (Opcional)
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Selecione uma mensalidade pendente</option>
                        <option value="1">Janeiro 2025 - R$ 450,00</option>
                        <option value="2">Fevereiro 2025 - R$ 450,00</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Se não selecionar, será um boleto avulso para o aluno</p>
                </div>

                <!-- Campos Avulso -->
                <div id="campos-avulso" class="mb-4" style="display: none;">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Nome do Pagador <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="nome_pagador"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Nome completo do pagador">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                CPF/CNPJ <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="cpf_pagador"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="000.000.000-00">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Email <span class="text-red-500">*</span>
                            </label>
                            <input type="email" name="email_pagador"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Telefone <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="telefone_pagador"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="(00) 00000-0000">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dados do Boleto -->
            <div class="mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Dados do Boleto</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Valor <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="valor" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="0,00">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Data de Vencimento <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="data_vencimento" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Descrição <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="descricao" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Descrição do boleto">
                    </div>
                </div>
            </div>

            <!-- Botões -->
            <div class="flex justify-end space-x-4">
                <a href="boletos.php" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancelar
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Gerar Boleto
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function mostrarCampos(tipo) {
    console.log('Mostrando campos para tipo:', tipo);
    
    // Buscar todos os campos
    var campoPolo = document.getElementById('campo-polo');
    var campoAluno = document.getElementById('campo-aluno');
    var campoMensalidade = document.getElementById('campo-mensalidade');
    var camposAvulso = document.getElementById('campos-avulso');
    
    // Ocultar todos
    campoPolo.style.display = 'none';
    campoAluno.style.display = 'none';
    campoMensalidade.style.display = 'none';
    camposAvulso.style.display = 'none';
    
    // Mostrar o correto
    if (tipo === 'polo') {
        campoPolo.style.display = 'block';
        console.log('Mostrando campo polo');
    } else if (tipo === 'aluno') {
        campoAluno.style.display = 'block';
        campoMensalidade.style.display = 'block';
        console.log('Mostrando campos aluno e mensalidade');
    } else if (tipo === 'avulso') {
        camposAvulso.style.display = 'block';
        console.log('Mostrando campos avulso');
    }
    
    // Marcar o radio button correto
    var radios = document.querySelectorAll('input[name="tipo_entidade"]');
    for (var i = 0; i < radios.length; i++) {
        radios[i].checked = (radios[i].value === tipo);
    }
}

// Inicializar com polo
document.addEventListener('DOMContentLoaded', function() {
    mostrarCampos('polo');
});
</script>

</body>
</html>
