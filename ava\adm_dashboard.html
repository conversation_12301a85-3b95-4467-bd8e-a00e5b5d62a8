<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Administrativo - Faciencia EAD</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
            background-color: var(--light-bg);
        }

        /* Sidebar Moderna */
        .sidebar {
            background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .sidebar-logo img {
            max-width: 50px;
            margin-right: 10px;
        }

        .sidebar-menu {
            list-style: none;
            flex-grow: 1;
            padding-left: 0;
        }

        .sidebar-menu li {
            margin-bottom: 10px;
        }

        .sidebar-menu a {
            color: var(--white);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.2);
        }

        .sidebar-menu a i {
            margin-right: 15px;
            font-size: 1.2rem;
            width: 30px;
            text-align: center;
        }

        .sidebar-section {
            margin-bottom: 20px;
        }

        .sidebar-section-header {
            font-size: 0.9rem;
            text-transform: uppercase;
            color: rgba(255,255,255,0.6);
            margin-left: 15px;
            margin-bottom: 10px;
        }

        /* Conteúdo Principal */
        .main-content {
            background-color: var(--white);
            padding: 30px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-purple);
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-info img {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid var(--primary-purple);
        }

        .admin-card {
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(106, 90, 205, 0.1);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .admin-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-purple), var(--light-purple));
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .stat-item {
            background-color: var(--light-bg);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item:hover {
            transform: translateY(-10px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-purple);
        }

        /* Cartão de polo com métricas de licença */
        .polo-card {
            display: flex;
            align-items: center;
            background-color: var(--light-bg);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .polo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(106, 90, 205, 0.2);
        }

        .polo-icon {
            width: 60px;
            height: 60px;
            background-color: var(--primary-purple);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .polo-info {
            flex-grow: 1;
        }

        .polo-metrics {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .metric-box {
            background-color: white;
            border-radius: 8px;
            padding: 10px 15px;
            min-width: 120px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }

        .metric-title {
            font-size: 0.8rem;
            color: var(--text-dark);
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-purple);
            display: flex;
            align-items: center;
        }

        .metric-limit {
            font-size: 0.8rem;
            color: #777;
            margin-left: 5px;
        }

        .polo-status {
            position: absolute;
            top: 15px;
            right: 15px;
        }

        .progress-sm {
            height: 5px;
            margin-top: 5px;
        }

        .license-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            color: white;
            margin-left: 10px;
        }

        .expires-soon {
            background-color: var(--warning-yellow);
        }

        .active-status {
            background-color: var(--success-green);
        }

        .near-limit {
            background-color: var(--warning-yellow);
        }

        .over-limit {
            background-color: var(--danger-red);
        }

        .actions-menu {
            position: absolute;
            top: 15px;
            right: 15px;
        }

        /* Tabelas com status de licença */
        .license-table th, .license-table td {
            vertical-align: middle;
        }

        .license-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .notification-icon {
            position: relative;
            margin-right: 20px;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }

        .quick-stat-card {
            flex: 1;
            background-color: var(--white);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
        }

        .quick-stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 1.5rem;
        }

        .bg-purple {
            background-color: var(--primary-purple);
        }

        .bg-blue {
            background-color: var(--info-blue);
        }

        .bg-green {
            background-color: var(--success-green);
        }

        .bg-orange {
            background-color: var(--warning-yellow);
        }

        .quick-stat-text h5 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .quick-stat-text p {
            font-size: 0.9rem;
            color: #777;
            margin-bottom: 0;
        }

        /* Responsividade */
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                height: 100vh;
                width: 280px;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 15px;
            }

            .quick-stats {
                flex-wrap: wrap;
            }

            .quick-stat-card {
                flex: 1 1 calc(50% - 15px);
                min-width: calc(50% - 15px);
            }
        }

        @media (max-width: 576px) {
            .quick-stat-card {
                flex: 1 1 100%;
            }
        }

        .alert-warning-custom {
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 4px solid var(--warning-yellow);
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .license-tabs .nav-link {
            padding: 10px 15px;
            border-radius: 0;
            font-weight: 500;
        }

        .license-tabs .nav-link.active {
            background-color: var(--primary-purple);
            color: white;
        }
    </style>
</head>
<body>
    <div class="dashboard-grid">
        <!-- Sidebar de Administração Geral -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-graduation-cap fa-2x"></i>
                <h2 class="ms-2">Faciencia</h2>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Principal</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_dashboard.html" class="active">
                            <i class="fas fa-tachometer-alt"></i> Painel Geral
                        </a>
                    </li>
                    <li>
                        <a href="adm_license.html">
                            <i class="fas fa-id-card"></i> Licenciamento
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Gerenciamento</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_polo.html">
                            <i class="fas fa-globe"></i> Polos
                        </a>
                    </li>
                    <li>
                        <a href="adm_cursos.html">
                            <i class="fas fa-book-open"></i> Cursos
                        </a>
                    </li>
                    <li>
                        <a href="adm_alunos.html">
                            <i class="fas fa-users"></i> Alunos
                        </a>
                    </li>
                    <li>
                        <a href="adm_professores.html">
                            <i class="fas fa-chalkboard-teacher"></i> Professores
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Relatórios</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_relatorio_financeiro.html">
                            <i class="fas fa-dollar-sign"></i> Financeiro
                        </a>
                    </li>
                    <li>
                        <a href="adm_relatorio_desempenho.html">
                            <i class="fas fa-chart-line"></i> Desempenho
                        </a>
                    </li>
                    <li>
                        <a href="adm_relatorio_licencas.html">
                            <i class="fas fa-id-badge"></i> Licenças
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Sistema</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_configuracoes.html">
                            <i class="fas fa-cogs"></i> Configurações
                        </a>
                    </li>
                    <li>
                        <a href="index.html" class="text-danger">
                            <i class="fas fa-sign-out-alt"></i> Sair
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Cabeçalho do Painel -->
            <header class="dashboard-header">
                <h1 class="m-0">Painel de Controle Geral</h1>
                <div class="d-flex align-items-center">
                    <div class="notification-icon">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="user-info">
                        <img src="/api/placeholder/45/45" alt="Foto de Perfil">
                        <div>
                            <h6 class="m-0">Admin Master</h6>
                            <small class="text-muted">Administrador</small>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Alertas do Sistema -->
            <div class="alert alert-warning-custom mb-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle me-3 fa-2x text-warning"></i>
                    <div>
                        <h5 class="m-0">Atenção: Licenças próximas ao vencimento</h5>
                        <p class="m-0">3 polos com licenças que vencem nos próximos 15 dias. <a href="#" class="alert-link">Verificar agora</a></p>
                    </div>
                </div>
            </div>

            <!-- Cartões rápidos de estatísticas -->
            <div class="quick-stats">
                <div class="quick-stat-card">
                    <div class="quick-stat-icon bg-purple">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="quick-stat-text">
                        <h5>12</h5>
                        <p>Total de Polos</p>
                    </div>
                </div>
                <div class="quick-stat-card">
                    <div class="quick-stat-icon bg-blue">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <div class="quick-stat-text">
                        <h5>87 / 120</h5>
                        <p>Cursos / Limite</p>
                    </div>
                </div>
                <div class="quick-stat-card">
                    <div class="quick-stat-icon bg-green">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="quick-stat-text">
                        <h5>2,345 / 3,500</h5>
                        <p>Alunos / Limite</p>
                    </div>
                </div>
                <div class="quick-stat-card">
                    <div class="quick-stat-icon bg-orange">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div class="quick-stat-text">
                        <h5>9/12</h5>
                        <p>Licenças Ativas</p>
                    </div>
                </div>
            </div>

            <!-- Status das Licenças e Limites -->
            <section class="admin-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="m-0">Status de Licenciamento do Sistema</h4>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nova Licença
                    </button>
                </div>
                
                <ul class="nav nav-tabs license-tabs mb-4">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#active-licenses">Licenças Ativas (9)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#expiring-licenses">Expirando (3)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#expired-licenses">Expiradas (3)</a>
                    </li>
                </ul>
                
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="active-licenses">
                        <div class="table-responsive">
                            <table class="table license-table">
                                <thead>
                                    <tr>
                                        <th>Polo</th>
                                        <th>Plano</th>
                                        <th>Alunos</th>
                                        <th>Cursos</th>
                                        <th>Vencimento</th>
                                        <th>Status</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Polo São Paulo</td>
                                        <td>Premium</td>
                                        <td>423/500</td>
                                        <td>15/25</td>
                                        <td>15/12/2024</td>
                                        <td><span class="badge bg-success">Ativo</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="#" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-sync-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Polo Rio de Janeiro</td>
                                        <td>Standard</td>
                                        <td>356/400</td>
                                        <td>12/15</td>
                                        <td>30/11/2024</td>
                                        <td><span class="badge bg-warning text-dark">Expira em 15 dias</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="#" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-sync-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Polo Belo Horizonte</td>
                                        <td>Premium</td>
                                        <td>487/500</td>
                                        <td>10/25</td>
                                        <td>15/01/2025</td>
                                        <td><span class="badge bg-success">Ativo</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="#" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-sync-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="expiring-licenses">
                        <!-- Conteúdo para licenças que estão expirando -->
                    </div>
                    
                    <div class="tab-pane fade" id="expired-licenses">
                        <!-- Conteúdo para licenças expiradas -->
                    </div>
                </div>
            </section>

            <!-- Listagem de Polos com Informações de Licença -->
            <section class="admin-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="m-0">Polos e Seus Limites de Licença</h4>
                    <div>
                        <button class="btn btn-primary me-2">
                            <i class="fas fa-plus me-2"></i>Adicionar Polo
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-filter me-2"></i>Filtrar
                        </button>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="polo-card">
                            <div class="polo-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="polo-info">
                                <div class="d-flex justify-content-between">
                                    <h5 class="mb-1">Polo São Paulo</h5>
                                    <span class="license-status active-status">Premium</span>
                                </div>
                                <p class="text-muted mb-2">Vence em: 15/12/2024</p>
                                
                                <div class="polo-metrics">
                                    <div class="metric-box">
                                        <div class="metric-title">Alunos</div>
                                        <div class="metric-value">423 <span class="metric-limit">/ 500</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-success" style="width: 85%"></div>
                                        </div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-title">Cursos</div>
                                        <div class="metric-value">15 <span class="metric-limit">/ 25</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-info" style="width: 60%"></div>
                                        </div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-title">Professores</div>
                                        <div class="metric-value">12 <span class="metric-limit">/ 15</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-warning" style="width: 80%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="polo-card">
                            <div class="polo-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="polo-info">
                                <div class="d-flex justify-content-between">
                                    <h5 class="mb-1">Polo Rio de Janeiro</h5>
                                    <span class="license-status expires-soon">Standard</span>
                                </div>
                                <p class="text-muted mb-2">Vence em: 30/11/2024</p>
                                
                                <div class="polo-metrics">
                                    <div class="metric-box">
                                        <div class="metric-title">Alunos</div>
                                        <div class="metric-value">356 <span class="metric-limit">/ 400</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-warning" style="width: 89%"></div>
                                        </div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-title">Cursos</div>
                                        <div class="metric-value">12 <span class="metric-limit">/ 15</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-warning" style="width: 80%"></div>
                                        </div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-title">Professores</div>
                                        <div class="metric-value">8 <span class="metric-limit">/ 10</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-info" style="width: 80%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="polo-card">
                            <div class="polo-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="polo-info">
                                <div class="d-flex justify-content-between">
                                    <h5 class="mb-1">Polo Belo Horizonte</h5>
                                    <span class="license-status active-status">Premium</span>
                                </div>
                                <p class="text-muted mb-2">Vence em: 15/01/2025</p>
                                
                                <div class="polo-metrics">
                                    <div class="metric-box">
                                        <div class="metric-title">Alunos</div>
                                        <div class="metric-value">487 <span class="metric-limit">/ 500</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-danger" style="width: 97%"></div>
                                        </div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-title">Cursos</div>
                                        <div class="metric-value">10 <span class="metric-limit">/ 25</span></div>
                                        <div class="progress progress-sm">
                                            <div class="progress-bar bg-success" style="width: 40%"></div>
                                        </div>
                                    </div>
                                    <div class="metric-box">
                                        <div class