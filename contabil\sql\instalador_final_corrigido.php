<?php
/**
 * ============================================================================
 * INSTALADOR FINAL CORRIGIDO - MÓDULO FINANCEIRO
 * ============================================================================
 * 
 * Este script cria TODAS as tabelas necessárias para o módulo financeiro
 * funcionar completamente, usando a estrutura correta da classe Database
 * ============================================================================
 */

// Configurações de erro
ini_set('display_errors', 1);
error_reporting(E_ALL);
set_time_limit(300); // 5 minutos

// Conectar ao banco
require_once '../../secretaria/includes/Database.php';

$log_messages = [];
$total_success = 0;
$total_errors = 0;

function logMessage($message, $type = 'info') {
    global $log_messages;
    $log_messages[] = ['message' => $message, 'type' => $type];
    echo "<p class='log-$type'>$message</p>\n";
    flush();
}

function executeSQL($db, $sql, $description = '') {
    global $total_success, $total_errors;
    
    try {
        // Usar getConnection() em vez de getPDO()
        $pdo = $db->getConnection();
        $pdo->exec($sql);
        $total_success++;
        logMessage("✅ $description", 'success');
        return true;
    } catch (Exception $e) {
        $total_errors++;
        $error_msg = $e->getMessage();
        
        // Ignorar erros de "já existe"
        if (strpos($error_msg, 'already exists') !== false || 
            strpos($error_msg, 'Duplicate entry') !== false ||
            strpos($error_msg, 'Duplicate key') !== false) {
            logMessage("ℹ️ $description (já existe)", 'info');
            return true;
        }
        
        logMessage("❌ $description - ERRO: $error_msg", 'error');
        return false;
    }
}

try {
    $db = Database::getInstance();
    
    logMessage("🚀 Iniciando instalação final corrigida do módulo financeiro...", 'info');
    logMessage("📅 Data/Hora: " . date('d/m/Y H:i:s'), 'info');
    
    // ========================================================================
    // 1. TABELAS DE POLOS E BOLETOS (PRIORIDADE)
    // ========================================================================
    
    logMessage("🏢 Criando tabelas de Polos e Boletos (prioridade)...", 'info');
    
    // Polos
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS polos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            telefone VARCHAR(20),
            endereco TEXT,
            cidade VARCHAR(100),
            estado VARCHAR(2),
            cep VARCHAR(10),
            cnpj VARCHAR(18),
            responsavel VARCHAR(255),
            status ENUM('ativo', 'inativo') DEFAULT 'ativo',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_nome (nome),
            INDEX idx_status (status),
            INDEX idx_cnpj (cnpj)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela polos");
    
    // Boletos dos Polos
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS polos_boletos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            polo_id INT NOT NULL,
            numero_boleto VARCHAR(50) NOT NULL,
            nosso_numero VARCHAR(50),
            valor DECIMAL(10,2) NOT NULL,
            data_vencimento DATE NOT NULL,
            descricao TEXT,
            observacoes TEXT,
            status ENUM('pendente', 'pago', 'vencido', 'cancelado') DEFAULT 'pendente',
            nome_pagador VARCHAR(255),
            cpf_pagador VARCHAR(14),
            asaas_payment_id VARCHAR(100),
            asaas_customer_id VARCHAR(100),
            asaas_boleto_url TEXT,
            asaas_linha_digitavel TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_polo_id (polo_id),
            INDEX idx_numero_boleto (numero_boleto),
            INDEX idx_nosso_numero (nosso_numero),
            INDEX idx_data_vencimento (data_vencimento),
            INDEX idx_status (status),
            INDEX idx_asaas_payment_id (asaas_payment_id),
            INDEX idx_cpf_pagador (cpf_pagador)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela polos_boletos");
    
    // ========================================================================
    // 2. TABELAS BÁSICAS DO FINANCEIRO
    // ========================================================================
    
    logMessage("💰 Criando tabelas básicas do financeiro...", 'info');
    
    // Contas a Pagar
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_contas_pagar (
            id INT AUTO_INCREMENT PRIMARY KEY,
            fornecedor VARCHAR(255) NOT NULL,
            cnpj_cpf VARCHAR(18),
            descricao TEXT NOT NULL,
            numero_documento VARCHAR(100),
            valor_original DECIMAL(15,2) NOT NULL,
            valor_pago DECIMAL(15,2) DEFAULT 0,
            valor_pendente DECIMAL(15,2) NOT NULL,
            data_vencimento DATE NOT NULL,
            data_pagamento DATE,
            categoria VARCHAR(100),
            observacoes TEXT,
            status ENUM('pendente', 'pago', 'vencido', 'cancelado') DEFAULT 'pendente',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_fornecedor (fornecedor),
            INDEX idx_data_vencimento (data_vencimento),
            INDEX idx_status (status),
            INDEX idx_categoria (categoria)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_contas_pagar");
    
    // Contas a Receber
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_contas_receber (
            id INT AUTO_INCREMENT PRIMARY KEY,
            cliente VARCHAR(255) NOT NULL,
            cpf_cnpj VARCHAR(18),
            descricao TEXT NOT NULL,
            numero_documento VARCHAR(100),
            valor_original DECIMAL(15,2) NOT NULL,
            valor_recebido DECIMAL(15,2) DEFAULT 0,
            valor_pendente DECIMAL(15,2) NOT NULL,
            data_vencimento DATE NOT NULL,
            data_recebimento DATE,
            categoria VARCHAR(100),
            observacoes TEXT,
            status ENUM('pendente', 'recebido', 'vencido', 'cancelado') DEFAULT 'pendente',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_cliente (cliente),
            INDEX idx_data_vencimento (data_vencimento),
            INDEX idx_status (status),
            INDEX idx_categoria (categoria)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_contas_receber");
    
    // Movimentação Bancária
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_movimentacao_bancaria (
            id INT AUTO_INCREMENT PRIMARY KEY,
            banco VARCHAR(100) NOT NULL,
            agencia VARCHAR(20),
            conta VARCHAR(30),
            tipo_movimentacao ENUM('entrada', 'saida') NOT NULL,
            valor DECIMAL(15,2) NOT NULL,
            data_movimentacao DATE NOT NULL,
            descricao TEXT NOT NULL,
            categoria VARCHAR(100),
            documento VARCHAR(100),
            saldo_anterior DECIMAL(15,2),
            saldo_atual DECIMAL(15,2),
            observacoes TEXT,
            status ENUM('confirmado', 'pendente', 'cancelado') DEFAULT 'confirmado',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_banco (banco),
            INDEX idx_data_movimentacao (data_movimentacao),
            INDEX idx_tipo_movimentacao (tipo_movimentacao),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_movimentacao_bancaria");
    
    // Impostos a Recolher
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_impostos_recolher (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tipo_imposto VARCHAR(50) NOT NULL,
            mes_referencia DATE NOT NULL,
            base_calculo DECIMAL(15,2) NOT NULL,
            aliquota DECIMAL(8,4) NOT NULL,
            valor_devido DECIMAL(15,2) NOT NULL,
            valor_pago DECIMAL(15,2) DEFAULT 0,
            data_vencimento DATE NOT NULL,
            data_pagamento DATE,
            codigo_darf VARCHAR(20),
            numero_documento VARCHAR(100),
            observacoes TEXT,
            status ENUM('pendente', 'pago', 'vencido', 'cancelado') DEFAULT 'pendente',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_tipo_imposto (tipo_imposto),
            INDEX idx_mes_referencia (mes_referencia),
            INDEX idx_data_vencimento (data_vencimento),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_impostos_recolher");
    
    // Retenções na Fonte
    executeSQL($db, "
        CREATE TABLE IF NOT EXISTS financeiro_retencoes_fonte (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tipo_retencao VARCHAR(50) NOT NULL,
            documento_origem_tipo VARCHAR(50),
            fornecedor_nome VARCHAR(255),
            fornecedor_cnpj VARCHAR(18),
            base_calculo DECIMAL(15,2) NOT NULL,
            aliquota DECIMAL(8,4) NOT NULL,
            valor_retido DECIMAL(15,2) NOT NULL,
            data_retencao DATE NOT NULL,
            mes_apuracao DATE NOT NULL,
            numero_darf VARCHAR(50),
            data_recolhimento DATE,
            observacoes TEXT,
            status ENUM('retido', 'recolhido', 'cancelado') DEFAULT 'retido',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_tipo_retencao (tipo_retencao),
            INDEX idx_data_retencao (data_retencao),
            INDEX idx_mes_apuracao (mes_apuracao),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ", "Tabela financeiro_retencoes_fonte");
    
    // ========================================================================
    // 3. INSERIR DADOS BÁSICOS
    // ========================================================================
    
    logMessage("📊 Inserindo dados básicos...", 'info');
    
    // Inserir polos
    $polos_data = [
        [1, 'Polo Central', '<EMAIL>', '(11) 1234-5678', 'Rua Principal, 123', 'São Paulo', 'SP', '01234-567', '12.345.678/0001-90', 'João Silva'],
        [2, 'Polo Norte', '<EMAIL>', '(11) 2345-6789', 'Av. Norte, 456', 'São Paulo', 'SP', '02345-678', '23.456.789/0001-01', 'Maria Santos'],
        [3, 'Polo Sul', '<EMAIL>', '(11) 3456-7890', 'Rua Sul, 789', 'São Paulo', 'SP', '03456-789', '34.567.890/0001-12', 'Carlos Oliveira']
    ];
    
    foreach ($polos_data as $polo) {
        try {
            $db->query("INSERT IGNORE INTO polos (id, nome, email, telefone, endereco, cidade, estado, cep, cnpj, responsavel) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $polo);
            logMessage("✅ Polo inserido: {$polo[1]}", 'success');
        } catch (Exception $e) {
            logMessage("ℹ️ Polo já existe: {$polo[1]}", 'info');
        }
    }
    
    // Inserir boletos
    $boletos_data = [
        [1, 1, 'BOL001', '000001', 500.00, '2025-02-15', 'Mensalidade Janeiro 2025', 'João da Silva', '123.456.789-01', 'pendente'],
        [2, 1, 'BOL002', '000002', 750.00, '2025-02-20', 'Taxa de Matrícula', 'Maria Oliveira', '234.567.890-12', 'pendente'],
        [3, 2, 'BOL003', '000003', 600.00, '2025-02-10', 'Mensalidade Janeiro 2025', 'Carlos Santos', '345.678.901-23', 'pago']
    ];
    
    foreach ($boletos_data as $boleto) {
        try {
            $db->query("INSERT IGNORE INTO polos_boletos (id, polo_id, numero_boleto, nosso_numero, valor, data_vencimento, descricao, nome_pagador, cpf_pagador, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $boleto);
            logMessage("✅ Boleto inserido: {$boleto[2]}", 'success');
        } catch (Exception $e) {
            logMessage("ℹ️ Boleto já existe: {$boleto[2]}", 'info');
        }
    }
    
    logMessage("📊 Resumo da instalação:", 'info');
    logMessage("✅ Comandos executados com sucesso: $total_success", 'success');
    logMessage("❌ Comandos com erro: $total_errors", ($total_errors > 0 ? 'error' : 'success'));
    
    if ($total_errors === 0) {
        logMessage("🎉 INSTALAÇÃO CONCLUÍDA COM SUCESSO!", 'success');
        logMessage("Todas as tabelas principais foram criadas!", 'success');
    } else {
        logMessage("⚠️ Instalação concluída com alguns avisos.", 'warning');
        logMessage("A maioria dos erros são normais (tabelas já existentes).", 'info');
    }
    
} catch (Exception $e) {
    logMessage("❌ ERRO FATAL: " . $e->getMessage(), 'error');
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalador Final Corrigido - Módulo Financeiro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .content {
            padding: 30px;
            max-height: 600px;
            overflow-y: auto;
        }
        .log-success { color: #10B981; font-weight: 500; }
        .log-error { color: #EF4444; font-weight: 500; }
        .log-warning { color: #F59E0B; font-weight: 500; }
        .log-info { color: #6B7280; }
        .actions {
            background: #F9FAFB;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #E5E7EB;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #3B82F6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2563EB;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #10B981;
        }
        .btn.success:hover {
            background: #059669;
        }
        .btn.danger {
            background: #EF4444;
        }
        .btn.danger:hover {
            background: #DC2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Instalador Final Corrigido</h1>
            <p>Módulo Financeiro - Sistema Reinandus</p>
        </div>
        
        <div class="content">
            <h2>📋 Log de Instalação</h2>
            <div id="log-container">
                <!-- Log messages aparecem aqui -->
            </div>
        </div>
        
        <div class="actions">
            <h3>🎯 Próximos Passos</h3>
            <a href="../boletos.php" class="btn success">📄 Testar Boletos</a>
            <a href="verificar_tabelas_final.php" class="btn">🔍 Verificar Sistema</a>
            <a href="../index.php" class="btn">🏠 Voltar ao Financeiro</a>
            <a href="criar_tabelas_restantes.php" class="btn danger">➕ Criar Tabelas Restantes</a>
            
            <div style="margin-top: 20px; padding: 15px; background: #DBEAFE; border-radius: 8px;">
                <h4 style="margin: 0 0 10px 0; color: #1E40AF;">🎯 Foco desta instalação:</h4>
                <p style="margin: 0; color: #1E40AF; font-size: 0.9em;">
                    ✅ Tabelas de Boletos | ✅ Tabelas Básicas | ✅ Dados de Exemplo | ✅ Correção de Erros
                </p>
            </div>
        </div>
    </div>
</body>
</html>
