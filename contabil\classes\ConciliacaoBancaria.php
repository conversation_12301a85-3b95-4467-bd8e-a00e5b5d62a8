<?php
/**
 * CLASSE CONCILIAÇÃO BANCÁRIA - CORRIGIDA
 *
 * Não carrega configurações diretamente - assume que já foram carregadas
 */

class ConciliacaoBancaria {
    private $db;

    public function __construct() {
        // Usa a instância já criada do Database
        $this->db = Database::getInstance();
    }
    
    /**
     * Importar extrato bancário (OFX/CSV)
     */
    public function importarExtrato($conta_bancaria_id, $arquivo_ofx) {
        $transacoes_importadas = [];
        
        if (pathinfo($arquivo_ofx, PATHINFO_EXTENSION) === 'ofx') {
            $transacoes_importadas = $this->processarOFX($arquivo_ofx);
        } else {
            $transacoes_importadas = $this->processarCSV($arquivo_ofx);
        }
        
        $importadas = 0;
        $duplicadas = 0;
        
        foreach ($transacoes_importadas as $transacao) {
            if ($this->transacaoJaExiste($conta_bancaria_id, $transacao)) {
                $duplicadas++;
                continue;
            }
            
            $this->inserirTransacaoBancaria($conta_bancaria_id, $transacao);
            $importadas++;
        }
        
        return [
            'importadas' => $importadas,
            'duplicadas' => $duplicadas,
            'total' => count($transacoes_importadas)
        ];
    }
    
    /**
     * Conciliação automática
     */
    public function conciliarAutomatico($conta_bancaria_id, $mes_referencia) {
        $transacoes_banco = $this->getTransacoesBanco($conta_bancaria_id, $mes_referencia);
        $transacoes_sistema = $this->getTransacoesSistema($conta_bancaria_id, $mes_referencia);
        
        $conciliadas = 0;
        $pendentes = 0;
        
        foreach ($transacoes_banco as $tb) {
            $encontrada = false;
            
            foreach ($transacoes_sistema as $ts) {
                if ($this->transacoesCombinam($tb, $ts)) {
                    $this->marcarComoConciliada($tb['id'], $ts['id']);
                    $conciliadas++;
                    $encontrada = true;
                    break;
                }
            }
            
            if (!$encontrada) {
                $pendentes++;
            }
        }
        
        return [
            'conciliadas' => $conciliadas,
            'pendentes' => $pendentes,
            'total_banco' => count($transacoes_banco),
            'total_sistema' => count($transacoes_sistema)
        ];
    }
    
    /**
     * Processar arquivo OFX
     */
    private function processarOFX($arquivo) {
        $conteudo = file_get_contents($arquivo);
        $transacoes = [];
        
        // Parser básico OFX (simplificado)
        preg_match_all('/<STMTTRN>(.*?)<\/STMTTRN>/s', $conteudo, $matches);
        
        foreach ($matches[1] as $transacao_xml) {
            $transacao = [];
            
            // Extrair dados da transação
            if (preg_match('/<DTPOSTED>(\d{8})/s', $transacao_xml, $data)) {
                $transacao['data'] = DateTime::createFromFormat('Ymd', $data[1])->format('Y-m-d');
            }
            
            if (preg_match('/<TRNAMT>([-\d\.]+)/s', $transacao_xml, $valor)) {
                $transacao['valor'] = floatval($valor[1]);
            }
            
            if (preg_match('/<MEMO>(.*?)</s', $transacao_xml, $descricao)) {
                $transacao['descricao'] = trim($descricao[1]);
            }
            
            if (preg_match('/<FITID>(.*?)</s', $transacao_xml, $id)) {
                $transacao['id_banco'] = trim($id[1]);
            }
            
            $transacao['tipo'] = $transacao['valor'] > 0 ? 'credito' : 'debito';
            $transacao['valor'] = abs($transacao['valor']);
            
            $transacoes[] = $transacao;
        }
        
        return $transacoes;
    }
    
    /**
     * Processar arquivo CSV
     */
    private function processarCSV($arquivo) {
        $transacoes = [];
        $handle = fopen($arquivo, 'r');
        
        // Pular cabeçalho
        fgetcsv($handle);
        
        while (($linha = fgetcsv($handle)) !== FALSE) {
            $transacao = [
                'data' => date('Y-m-d', strtotime($linha[0])),
                'descricao' => $linha[1],
                'valor' => abs(floatval(str_replace(',', '.', $linha[2]))),
                'tipo' => floatval(str_replace(',', '.', $linha[2])) > 0 ? 'credito' : 'debito',
                'id_banco' => $linha[3] ?? uniqid()
            ];
            
            $transacoes[] = $transacao;
        }
        
        fclose($handle);
        return $transacoes;
    }
    
    /**
     * Verificar se transação já existe
     */
    private function transacaoJaExiste($conta_id, $transacao) {
        $existe = $this->db->fetch("
            SELECT id FROM extrato_bancario 
            WHERE conta_bancaria_id = ? 
            AND id_banco = ? 
            AND data_transacao = ?
        ", [$conta_id, $transacao['id_banco'], $transacao['data']]);
        
        return $existe !== false;
    }
    
    /**
     * Inserir transação bancária
     */
    private function inserirTransacaoBancaria($conta_id, $transacao) {
        $sql = "INSERT INTO extrato_bancario 
                (conta_bancaria_id, data_transacao, descricao, valor, tipo, 
                 id_banco, status_conciliacao, data_importacao)
                VALUES (?, ?, ?, ?, ?, ?, 'pendente', NOW())";
        
        $this->db->execute($sql, [
            $conta_id,
            $transacao['data'],
            $transacao['descricao'],
            $transacao['valor'],
            $transacao['tipo'],
            $transacao['id_banco']
        ]);
    }
    
    /**
     * Buscar transações do banco
     */
    private function getTransacoesBanco($conta_id, $mes_referencia) {
        return $this->db->fetchAll("
            SELECT * FROM extrato_bancario 
            WHERE conta_bancaria_id = ? 
            AND DATE_FORMAT(data_transacao, '%Y-%m') = ?
            AND status_conciliacao = 'pendente'
            ORDER BY data_transacao
        ", [$conta_id, $mes_referencia]);
    }
    
    /**
     * Buscar transações do sistema
     */
    private function getTransacoesSistema($conta_id, $mes_referencia) {
        return $this->db->fetchAll("
            SELECT tf.*, 'transacao_financeira' as origem
            FROM transacoes_financeiras tf
            WHERE tf.conta_bancaria_id = ? 
            AND DATE_FORMAT(tf.data_transacao, '%Y-%m') = ?
            AND tf.conciliado = 0
            
            UNION ALL
            
            SELECT tb.id, tb.conta_origem_id as conta_bancaria_id, tb.data_operacao as data_transacao,
                   CONCAT('Transferência para ', cb.nome) as descricao,
                   (tb.valor + tb.tarifa) as valor, 'debito' as tipo,
                   'transferencia_bancaria' as origem, 0 as conciliado
            FROM transferencias_bancarias tb
            JOIN contas_bancarias cb ON tb.conta_destino_id = cb.id
            WHERE tb.conta_origem_id = ? 
            AND DATE_FORMAT(tb.data_operacao, '%Y-%m') = ?
            AND tb.status = 'processada'
            AND tb.conciliado = 0
            
            UNION ALL
            
            SELECT tb.id, tb.conta_destino_id as conta_bancaria_id, tb.data_operacao as data_transacao,
                   CONCAT('Transferência de ', cb.nome) as descricao,
                   tb.valor as valor, 'credito' as tipo,
                   'transferencia_bancaria' as origem, 0 as conciliado
            FROM transferencias_bancarias tb
            JOIN contas_bancarias cb ON tb.conta_origem_id = cb.id
            WHERE tb.conta_destino_id = ? 
            AND DATE_FORMAT(tb.data_operacao, '%Y-%m') = ?
            AND tb.status = 'processada'
            AND tb.conciliado = 0
            
            ORDER BY data_transacao
        ", [$conta_id, $mes_referencia, $conta_id, $mes_referencia, $conta_id, $mes_referencia]);
    }
    
    /**
     * Verificar se transações combinam
     */
    private function transacoesCombinam($transacao_banco, $transacao_sistema) {
        // Verificar data (tolerância de 2 dias)
        $data_banco = strtotime($transacao_banco['data_transacao']);
        $data_sistema = strtotime($transacao_sistema['data_transacao']);
        $diferenca_dias = abs($data_banco - $data_sistema) / (60 * 60 * 24);
        
        if ($diferenca_dias > 2) {
            return false;
        }
        
        // Verificar valor (tolerância de R$ 0,10)
        $diferenca_valor = abs($transacao_banco['valor'] - $transacao_sistema['valor']);
        if ($diferenca_valor > 0.10) {
            return false;
        }
        
        // Verificar tipo
        if ($transacao_banco['tipo'] !== $transacao_sistema['tipo']) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Marcar como conciliada
     */
    private function marcarComoConciliada($extrato_id, $transacao_sistema_id) {
        // Marcar extrato como conciliado
        $this->db->execute("
            UPDATE extrato_bancario 
            SET status_conciliacao = 'conciliado', 
                transacao_sistema_id = ?,
                data_conciliacao = NOW()
            WHERE id = ?
        ", [$transacao_sistema_id, $extrato_id]);
        
        // Marcar transação do sistema como conciliada
        $this->db->execute("
            UPDATE transacoes_financeiras 
            SET conciliado = 1, data_conciliacao = NOW()
            WHERE id = ?
        ", [$transacao_sistema_id]);
    }
    
    /**
     * Relatório de conciliação
     */
    public function relatorioConciliacao($conta_bancaria_id, $mes_referencia) {
        $saldo_inicial = $this->getSaldoInicial($conta_bancaria_id, $mes_referencia);
        $movimentacoes_banco = $this->getMovimentacoesBanco($conta_bancaria_id, $mes_referencia);
        $movimentacoes_sistema = $this->getMovimentacoesSistema($conta_bancaria_id, $mes_referencia);
        
        $saldo_final_banco = $saldo_inicial + $movimentacoes_banco['creditos'] - $movimentacoes_banco['debitos'];
        $saldo_final_sistema = $saldo_inicial + $movimentacoes_sistema['creditos'] - $movimentacoes_sistema['debitos'];
        
        return [
            'saldo_inicial' => $saldo_inicial,
            'banco' => $movimentacoes_banco,
            'sistema' => $movimentacoes_sistema,
            'saldo_final_banco' => $saldo_final_banco,
            'saldo_final_sistema' => $saldo_final_sistema,
            'diferenca' => $saldo_final_banco - $saldo_final_sistema,
            'conciliado' => abs($saldo_final_banco - $saldo_final_sistema) < 0.01
        ];
    }
    
    /**
     * Buscar itens não conciliados
     */
    public function getItensNaoConciliados($conta_bancaria_id, $mes_referencia) {
        $banco_pendentes = $this->db->fetchAll("
            SELECT *, 'banco' as origem FROM extrato_bancario 
            WHERE conta_bancaria_id = ? 
            AND DATE_FORMAT(data_transacao, '%Y-%m') = ?
            AND status_conciliacao = 'pendente'
        ", [$conta_bancaria_id, $mes_referencia]);
        
        $sistema_pendentes = $this->db->fetchAll("
            SELECT *, 'sistema' as origem FROM transacoes_financeiras 
            WHERE conta_bancaria_id = ? 
            AND DATE_FORMAT(data_transacao, '%Y-%m') = ?
            AND conciliado = 0
        ", [$conta_bancaria_id, $mes_referencia]);
        
        return [
            'banco' => $banco_pendentes,
            'sistema' => $sistema_pendentes
        ];
    }

    /**
     * Conciliação manual
     */
    public function conciliarManual($extrato_id, $transacao_sistema_id) {
        // Verificar se os itens existem
        $extrato = $this->db->fetch("SELECT * FROM extrato_bancario WHERE id = ?", [$extrato_id]);
        $transacao = $this->db->fetch("SELECT * FROM transacoes_financeiras WHERE id = ?", [$transacao_sistema_id]);
        
        if (!$extrato || !$transacao) {
            throw new Exception("Item não encontrado para conciliação");
        }
        
        // Verificar se valores são compatíveis (tolerância de 10%)
        $diferenca_percentual = abs($extrato['valor'] - $transacao['valor']) / $extrato['valor'] * 100;
        if ($diferenca_percentual > 10) {
            throw new Exception("Valores muito diferentes para conciliação manual");
        }
        
        $this->marcarComoConciliada($extrato_id, $transacao_sistema_id);
        
        return true;
    }

    /**
     * Desfazer conciliação
     */
    public function desfazerConciliacao($extrato_id) {
        $extrato = $this->db->fetch("
            SELECT * FROM extrato_bancario 
            WHERE id = ? AND status_conciliacao = 'conciliado'
        ", [$extrato_id]);
        
        if (!$extrato) {
            throw new Exception("Item não encontrado ou não conciliado");
        }
        
        // Desfazer no extrato
        $this->db->execute("
            UPDATE extrato_bancario 
            SET status_conciliacao = 'pendente', 
                transacao_sistema_id = NULL,
                data_conciliacao = NULL
            WHERE id = ?
        ", [$extrato_id]);
        
        // Desfazer na transação do sistema
        if ($extrato['transacao_sistema_id']) {
            $this->db->execute("
                UPDATE transacoes_financeiras 
                SET conciliado = 0, data_conciliacao = NULL
                WHERE id = ?
            ", [$extrato['transacao_sistema_id']]);
        }
        
        return true;
    }

    /**
     * Criar lançamento para item não conciliado
     */
    public function criarLancamentoParaItem($extrato_id, $categoria_id, $observacoes = '') {
        $extrato = $this->db->fetch("SELECT * FROM extrato_bancario WHERE id = ?", [$extrato_id]);
        
        if (!$extrato || $extrato['status_conciliacao'] !== 'pendente') {
            throw new Exception("Item não encontrado ou já conciliado");
        }
        
        // Criar transação financeira
        $transacao_id = $this->db->execute("
            INSERT INTO transacoes_financeiras 
            (conta_bancaria_id, categoria_id, data_transacao, descricao, valor, tipo, 
             status, origem, observacoes, data_criacao)
            VALUES (?, ?, ?, ?, ?, ?, 'efetivada', 'conciliacao_bancaria', ?, NOW())
        ", [
            $extrato['conta_bancaria_id'],
            $categoria_id,
            $extrato['data_transacao'],
            $extrato['descricao'],
            $extrato['valor'],
            $extrato['tipo'] === 'credito' ? 'receita' : 'despesa',
            $observacoes
        ]);
        
        // Marcar como conciliado
        $this->marcarComoConciliada($extrato_id, $transacao_id);
        
        return $transacao_id;
    }

    /**
     * Estatísticas de conciliação
     */
    public function getEstatisticasConciliacao($conta_id, $periodo_inicio, $periodo_fim) {
        $stats = $this->db->fetch("
            SELECT 
                COUNT(*) as total_transacoes,
                SUM(CASE WHEN status_conciliacao = 'conciliado' THEN 1 ELSE 0 END) as conciliadas,
                SUM(CASE WHEN status_conciliacao = 'pendente' THEN 1 ELSE 0 END) as pendentes,
                SUM(CASE WHEN tipo = 'credito' THEN valor ELSE 0 END) as total_creditos,
                SUM(CASE WHEN tipo = 'debito' THEN valor ELSE 0 END) as total_debitos
            FROM extrato_bancario 
            WHERE conta_bancaria_id = ? 
            AND data_transacao BETWEEN ? AND ?
        ", [$conta_id, $periodo_inicio, $periodo_fim]);
        
        $percentual_conciliado = $stats['total_transacoes'] > 0 
            ? ($stats['conciliadas'] / $stats['total_transacoes']) * 100 
            : 0;
        
        return array_merge($stats, [
            'percentual_conciliado' => $percentual_conciliado,
            'saldo_periodo' => $stats['total_creditos'] - $stats['total_debitos']
        ]);
    }
}
?>

