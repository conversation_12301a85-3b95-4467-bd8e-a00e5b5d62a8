-- ============================================================================
-- ESTRUTURA COMPLETA DO MÓDULO FINANCEIRO
-- Sistema ERP FaCiência - Implementação Completa
-- ============================================================================
-- 
-- Este script cria toda a estrutura necessária para um sistema financeiro
-- completo, incluindo todas as funcionalidades identificadas como faltantes:
-- - Obrigações Trabalhistas
-- - Obrigações Tributárias  
-- - Patrimônio Líquido
-- - Ativo Não Circulante
-- - Centro de Custos Avançado
-- - Conciliação Bancária
-- - Demonstrações Contábeis
-- - Gestão de Caixa Avançada
-- - Orçamento e Planejamento
-- ============================================================================

-- ============================================================================
-- 1. PLANO DE CONTAS COMPLETO
-- ============================================================================

CREATE TABLE IF NOT EXISTS `plano_contas` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `codigo` VARCHAR(20) NOT NULL UNIQUE,
    `nome` VARCHAR(255) NOT NULL,
    `tipo` ENUM('ativo', 'passivo', 'patrimonio_liquido', 'receita', 'despesa') NOT NULL,
    `subtipo` VARCHAR(50),
    `nivel` INT NOT NULL DEFAULT 1,
    `conta_pai_id` INT NULL,
    `aceita_lancamento` BOOLEAN DEFAULT TRUE,
    `natureza` ENUM('debito', 'credito') NOT NULL,
    `ativo` BOOLEAN DEFAULT TRUE,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_codigo` (`codigo`),
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_nivel` (`nivel`),
    INDEX `idx_conta_pai` (`conta_pai_id`),
    FOREIGN KEY (`conta_pai_id`) REFERENCES `plano_contas`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 2. CENTRO DE CUSTOS AVANÇADO
-- ============================================================================

CREATE TABLE IF NOT EXISTS `centro_custos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `codigo` VARCHAR(20) NOT NULL UNIQUE,
    `nome` VARCHAR(255) NOT NULL,
    `tipo` ENUM('curso', 'departamento', 'projeto', 'polo', 'geral') NOT NULL,
    `responsavel_id` INT,
    `orcamento_anual` DECIMAL(15,2) DEFAULT 0.00,
    `ativo` BOOLEAN DEFAULT TRUE,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_codigo` (`codigo`),
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_responsavel` (`responsavel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 3. OBRIGAÇÕES TRABALHISTAS
-- ============================================================================

-- Folha de Pagamento
CREATE TABLE IF NOT EXISTS `folha_pagamento` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `funcionario_id` INT NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `salario_base` DECIMAL(10,2) NOT NULL,
    `horas_extras` DECIMAL(10,2) DEFAULT 0.00,
    `adicional_noturno` DECIMAL(10,2) DEFAULT 0.00,
    `comissoes` DECIMAL(10,2) DEFAULT 0.00,
    `gratificacoes` DECIMAL(10,2) DEFAULT 0.00,
    `vale_transporte` DECIMAL(10,2) DEFAULT 0.00,
    `vale_refeicao` DECIMAL(10,2) DEFAULT 0.00,
    `plano_saude` DECIMAL(10,2) DEFAULT 0.00,
    `outros_proventos` DECIMAL(10,2) DEFAULT 0.00,
    `inss_funcionario` DECIMAL(10,2) DEFAULT 0.00,
    `irrf` DECIMAL(10,2) DEFAULT 0.00,
    `vale_transporte_desconto` DECIMAL(10,2) DEFAULT 0.00,
    `vale_refeicao_desconto` DECIMAL(10,2) DEFAULT 0.00,
    `plano_saude_desconto` DECIMAL(10,2) DEFAULT 0.00,
    `outros_descontos` DECIMAL(10,2) DEFAULT 0.00,
    `salario_liquido` DECIMAL(10,2) NOT NULL,
    `inss_empresa` DECIMAL(10,2) DEFAULT 0.00,
    `fgts` DECIMAL(10,2) DEFAULT 0.00,
    `contribuicao_sindical` DECIMAL(10,2) DEFAULT 0.00,
    `status` ENUM('calculada', 'aprovada', 'paga') DEFAULT 'calculada',
    `data_pagamento` DATE NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_funcionario` (`funcionario_id`),
    INDEX `idx_mes_referencia` (`mes_referencia`),
    INDEX `idx_status` (`status`),
    UNIQUE KEY `uk_funcionario_mes` (`funcionario_id`, `mes_referencia`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Provisões Trabalhistas
CREATE TABLE IF NOT EXISTS `provisoes_trabalhistas` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `funcionario_id` INT NOT NULL,
    `tipo` ENUM('ferias', 'decimo_terceiro', 'fgts', 'inss', 'rescisao') NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `valor_provisionado` DECIMAL(10,2) NOT NULL,
    `valor_utilizado` DECIMAL(10,2) DEFAULT 0.00,
    `saldo_atual` DECIMAL(10,2) NOT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_funcionario` (`funcionario_id`),
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_mes_referencia` (`mes_referencia`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Obrigações Trabalhistas a Pagar
CREATE TABLE IF NOT EXISTS `obrigacoes_trabalhistas` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `tipo` ENUM('salario', 'inss', 'fgts', 'irrf', 'contribuicao_sindical', 'vale_transporte', 'vale_refeicao') NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `valor_total` DECIMAL(12,2) NOT NULL,
    `valor_pago` DECIMAL(12,2) DEFAULT 0.00,
    `valor_pendente` DECIMAL(12,2) NOT NULL,
    `data_vencimento` DATE NOT NULL,
    `status` ENUM('pendente', 'parcial', 'pago', 'vencido') DEFAULT 'pendente',
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_mes_referencia` (`mes_referencia`),
    INDEX `idx_status` (`status`),
    INDEX `idx_vencimento` (`data_vencimento`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 4. OBRIGAÇÕES TRIBUTÁRIAS
-- ============================================================================

-- Impostos e Tributos
CREATE TABLE IF NOT EXISTS `impostos_tributos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `tipo` ENUM('irpj', 'csll', 'pis', 'cofins', 'iss', 'icms', 'simples_nacional', 'retencao_irrf', 'retencao_iss', 'retencao_pis_cofins') NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `base_calculo` DECIMAL(15,2) NOT NULL,
    `aliquota` DECIMAL(5,4) NOT NULL,
    `valor_calculado` DECIMAL(12,2) NOT NULL,
    `valor_pago` DECIMAL(12,2) DEFAULT 0.00,
    `valor_pendente` DECIMAL(12,2) NOT NULL,
    `data_vencimento` DATE NOT NULL,
    `codigo_receita` VARCHAR(10),
    `status` ENUM('calculado', 'pago', 'vencido', 'parcelado') DEFAULT 'calculado',
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_mes_referencia` (`mes_referencia`),
    INDEX `idx_status` (`status`),
    INDEX `idx_vencimento` (`data_vencimento`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Retenções na Fonte
CREATE TABLE IF NOT EXISTS `retencoes_fonte` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `conta_pagar_id` INT,
    `fornecedor_id` INT NOT NULL,
    `tipo_retencao` ENUM('irrf', 'iss', 'pis', 'cofins', 'csll', 'inss') NOT NULL,
    `valor_base` DECIMAL(12,2) NOT NULL,
    `aliquota` DECIMAL(5,4) NOT NULL,
    `valor_retido` DECIMAL(10,2) NOT NULL,
    `data_retencao` DATE NOT NULL,
    `codigo_receita` VARCHAR(10),
    `status` ENUM('retido', 'recolhido') DEFAULT 'retido',
    `data_recolhimento` DATE NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_conta_pagar` (`conta_pagar_id`),
    INDEX `idx_fornecedor` (`fornecedor_id`),
    INDEX `idx_tipo` (`tipo_retencao`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 5. PATRIMÔNIO LÍQUIDO
-- ============================================================================

-- Capital Social
CREATE TABLE IF NOT EXISTS `capital_social` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `valor_subscrito` DECIMAL(15,2) NOT NULL,
    `valor_integralizado` DECIMAL(15,2) NOT NULL,
    `valor_a_integralizar` DECIMAL(15,2) NOT NULL,
    `data_constituicao` DATE NOT NULL,
    `data_alteracao` DATE,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reservas
CREATE TABLE IF NOT EXISTS `reservas` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `tipo` ENUM('legal', 'estatutaria', 'contingencia', 'expansao', 'lucros_acumulados') NOT NULL,
    `descricao` VARCHAR(255) NOT NULL,
    `valor_inicial` DECIMAL(15,2) DEFAULT 0.00,
    `valor_atual` DECIMAL(15,2) NOT NULL,
    `data_constituicao` DATE NOT NULL,
    `finalidade` TEXT,
    `limite_legal` DECIMAL(15,2),
    `ativo` BOOLEAN DEFAULT TRUE,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_data_constituicao` (`data_constituicao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Movimentação de Reservas
CREATE TABLE IF NOT EXISTS `movimentacao_reservas` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `reserva_id` INT NOT NULL,
    `tipo_movimento` ENUM('constituicao', 'reversao', 'utilizacao', 'transferencia') NOT NULL,
    `valor` DECIMAL(15,2) NOT NULL,
    `data_movimento` DATE NOT NULL,
    `historico` TEXT NOT NULL,
    `documento_origem` VARCHAR(100),
    `usuario_id` INT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_reserva` (`reserva_id`),
    INDEX `idx_tipo_movimento` (`tipo_movimento`),
    INDEX `idx_data_movimento` (`data_movimento`),
    FOREIGN KEY (`reserva_id`) REFERENCES `reservas`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 6. ATIVO NÃO CIRCULANTE
-- ============================================================================

-- Imobilizado
CREATE TABLE IF NOT EXISTS `imobilizado` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `codigo` VARCHAR(50) NOT NULL UNIQUE,
    `descricao` VARCHAR(255) NOT NULL,
    `categoria` ENUM('moveis_utensilios', 'equipamentos_informatica', 'veiculos', 'imoveis', 'instalacoes', 'outros') NOT NULL,
    `data_aquisicao` DATE NOT NULL,
    `valor_aquisicao` DECIMAL(12,2) NOT NULL,
    `vida_util_anos` INT NOT NULL,
    `valor_residual` DECIMAL(12,2) DEFAULT 0.00,
    `metodo_depreciacao` ENUM('linear', 'soma_digitos', 'unidades_producao') DEFAULT 'linear',
    `centro_custo_id` INT,
    `fornecedor_id` INT,
    `numero_nota_fiscal` VARCHAR(50),
    `localizacao` VARCHAR(255),
    `responsavel_id` INT,
    `status` ENUM('ativo', 'baixado', 'vendido', 'sucateado') DEFAULT 'ativo',
    `data_baixa` DATE NULL,
    `valor_baixa` DECIMAL(12,2) NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_codigo` (`codigo`),
    INDEX `idx_categoria` (`categoria`),
    INDEX `idx_status` (`status`),
    INDEX `idx_centro_custo` (`centro_custo_id`),
    INDEX `idx_responsavel` (`responsavel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Depreciação
CREATE TABLE IF NOT EXISTS `depreciacao` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `imobilizado_id` INT NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `valor_depreciacao_mensal` DECIMAL(10,2) NOT NULL,
    `valor_depreciacao_acumulada` DECIMAL(12,2) NOT NULL,
    `valor_liquido` DECIMAL(12,2) NOT NULL,
    `percentual_depreciado` DECIMAL(5,2) NOT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_imobilizado` (`imobilizado_id`),
    INDEX `idx_mes_referencia` (`mes_referencia`),
    UNIQUE KEY `uk_imobilizado_mes` (`imobilizado_id`, `mes_referencia`),
    FOREIGN KEY (`imobilizado_id`) REFERENCES `imobilizado`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Investimentos
CREATE TABLE IF NOT EXISTS `investimentos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `tipo` ENUM('renda_fixa', 'renda_variavel', 'fundos', 'imoveis', 'outros') NOT NULL,
    `descricao` VARCHAR(255) NOT NULL,
    `instituicao_financeira` VARCHAR(255),
    `data_aplicacao` DATE NOT NULL,
    `valor_aplicado` DECIMAL(15,2) NOT NULL,
    `valor_atual` DECIMAL(15,2) NOT NULL,
    `rentabilidade_percentual` DECIMAL(8,4) DEFAULT 0.0000,
    `data_vencimento` DATE,
    `liquidez` ENUM('diaria', 'semanal', 'mensal', 'trimestral', 'semestral', 'anual', 'vencimento') NOT NULL,
    `status` ENUM('ativo', 'resgatado', 'vencido') DEFAULT 'ativo',
    `data_resgate` DATE NULL,
    `valor_resgate` DECIMAL(15,2) NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_status` (`status`),
    INDEX `idx_data_vencimento` (`data_vencimento`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 7. CONCILIAÇÃO BANCÁRIA
-- ============================================================================

-- Extratos Bancários
CREATE TABLE IF NOT EXISTS `extratos_bancarios` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `conta_bancaria_id` INT NOT NULL,
    `data_movimento` DATE NOT NULL,
    `numero_documento` VARCHAR(50),
    `historico` TEXT NOT NULL,
    `valor_debito` DECIMAL(12,2) DEFAULT 0.00,
    `valor_credito` DECIMAL(12,2) DEFAULT 0.00,
    `saldo` DECIMAL(15,2) NOT NULL,
    `codigo_movimento` VARCHAR(10),
    `conciliado` BOOLEAN DEFAULT FALSE,
    `data_conciliacao` DATETIME NULL,
    `usuario_conciliacao` INT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_conta_bancaria` (`conta_bancaria_id`),
    INDEX `idx_data_movimento` (`data_movimento`),
    INDEX `idx_conciliado` (`conciliado`),
    INDEX `idx_numero_documento` (`numero_documento`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Conciliação Bancária
CREATE TABLE IF NOT EXISTS `conciliacao_bancaria` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `conta_bancaria_id` INT NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `saldo_inicial_banco` DECIMAL(15,2) NOT NULL,
    `saldo_final_banco` DECIMAL(15,2) NOT NULL,
    `saldo_inicial_sistema` DECIMAL(15,2) NOT NULL,
    `saldo_final_sistema` DECIMAL(15,2) NOT NULL,
    `total_debitos_nao_conciliados` DECIMAL(12,2) DEFAULT 0.00,
    `total_creditos_nao_conciliados` DECIMAL(12,2) DEFAULT 0.00,
    `diferenca_conciliacao` DECIMAL(12,2) NOT NULL,
    `status` ENUM('aberta', 'conciliada', 'com_diferenca') DEFAULT 'aberta',
    `data_conciliacao` DATETIME NULL,
    `usuario_conciliacao` INT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX `idx_conta_bancaria` (`conta_bancaria_id`),
    INDEX `idx_mes_referencia` (`mes_referencia`),
    INDEX `idx_status` (`status`),
    UNIQUE KEY `uk_conta_mes` (`conta_bancaria_id`, `mes_referencia`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cheques Emitidos
CREATE TABLE IF NOT EXISTS `cheques_emitidos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `conta_bancaria_id` INT NOT NULL,
    `numero_cheque` VARCHAR(20) NOT NULL,
    `data_emissao` DATE NOT NULL,
    `valor` DECIMAL(12,2) NOT NULL,
    `favorecido` VARCHAR(255) NOT NULL,
    `historico` TEXT,
    `status` ENUM('emitido', 'compensado', 'devolvido', 'cancelado') DEFAULT 'emitido',
    `data_compensacao` DATE NULL,
    `data_devolucao` DATE NULL,
    `motivo_devolucao` VARCHAR(255) NULL,
    `conta_pagar_id` INT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX `idx_conta_bancaria` (`conta_bancaria_id`),
    INDEX `idx_numero_cheque` (`numero_cheque`),
    INDEX `idx_status` (`status`),
    INDEX `idx_data_emissao` (`data_emissao`),
    UNIQUE KEY `uk_conta_numero` (`conta_bancaria_id`, `numero_cheque`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 8. LANÇAMENTOS CONTÁBEIS
-- ============================================================================

-- Lançamentos Contábeis
CREATE TABLE IF NOT EXISTS `lancamentos_contabeis` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_lancamento` VARCHAR(20) NOT NULL UNIQUE,
    `data_lancamento` DATE NOT NULL,
    `tipo_lancamento` ENUM('manual', 'automatico') DEFAULT 'manual',
    `origem` VARCHAR(50) COMMENT 'Módulo que gerou o lançamento',
    `origem_id` INT COMMENT 'ID do registro de origem',
    `historico` TEXT NOT NULL,
    `valor_total` DECIMAL(15,2) NOT NULL,
    `status` ENUM('provisorio', 'definitivo', 'cancelado') DEFAULT 'provisorio',
    `usuario_id` INT,
    `data_cancelamento` DATETIME NULL,
    `motivo_cancelamento` TEXT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX `idx_numero_lancamento` (`numero_lancamento`),
    INDEX `idx_data_lancamento` (`data_lancamento`),
    INDEX `idx_tipo_lancamento` (`tipo_lancamento`),
    INDEX `idx_status` (`status`),
    INDEX `idx_origem` (`origem`, `origem_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Partidas dos Lançamentos
CREATE TABLE IF NOT EXISTS `partidas_lancamentos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `lancamento_id` INT NOT NULL,
    `conta_contabil_id` INT NOT NULL,
    `centro_custo_id` INT NULL,
    `tipo_partida` ENUM('debito', 'credito') NOT NULL,
    `valor` DECIMAL(15,2) NOT NULL,
    `historico_complementar` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_lancamento` (`lancamento_id`),
    INDEX `idx_conta_contabil` (`conta_contabil_id`),
    INDEX `idx_centro_custo` (`centro_custo_id`),
    INDEX `idx_tipo_partida` (`tipo_partida`),
    FOREIGN KEY (`lancamento_id`) REFERENCES `lancamentos_contabeis`(`id`),
    FOREIGN KEY (`conta_contabil_id`) REFERENCES `plano_contas`(`id`),
    FOREIGN KEY (`centro_custo_id`) REFERENCES `centro_custos`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 9. ORÇAMENTO E PLANEJAMENTO
-- ============================================================================

-- Orçamento Anual
CREATE TABLE IF NOT EXISTS `orcamento_anual` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `ano_exercicio` YEAR NOT NULL,
    `conta_contabil_id` INT NOT NULL,
    `centro_custo_id` INT NULL,
    `janeiro` DECIMAL(12,2) DEFAULT 0.00,
    `fevereiro` DECIMAL(12,2) DEFAULT 0.00,
    `marco` DECIMAL(12,2) DEFAULT 0.00,
    `abril` DECIMAL(12,2) DEFAULT 0.00,
    `maio` DECIMAL(12,2) DEFAULT 0.00,
    `junho` DECIMAL(12,2) DEFAULT 0.00,
    `julho` DECIMAL(12,2) DEFAULT 0.00,
    `agosto` DECIMAL(12,2) DEFAULT 0.00,
    `setembro` DECIMAL(12,2) DEFAULT 0.00,
    `outubro` DECIMAL(12,2) DEFAULT 0.00,
    `novembro` DECIMAL(12,2) DEFAULT 0.00,
    `dezembro` DECIMAL(12,2) DEFAULT 0.00,
    `total_anual` DECIMAL(15,2) NOT NULL,
    `status` ENUM('elaboracao', 'aprovado', 'revisado') DEFAULT 'elaboracao',
    `data_aprovacao` DATE NULL,
    `usuario_aprovacao` INT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX `idx_ano_exercicio` (`ano_exercicio`),
    INDEX `idx_conta_contabil` (`conta_contabil_id`),
    INDEX `idx_centro_custo` (`centro_custo_id`),
    INDEX `idx_status` (`status`),
    UNIQUE KEY `uk_orcamento` (`ano_exercicio`, `conta_contabil_id`, `centro_custo_id`),
    FOREIGN KEY (`conta_contabil_id`) REFERENCES `plano_contas`(`id`),
    FOREIGN KEY (`centro_custo_id`) REFERENCES `centro_custos`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Controle Orçamentário
CREATE TABLE IF NOT EXISTS `controle_orcamentario` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `orcamento_id` INT NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `valor_orcado` DECIMAL(12,2) NOT NULL,
    `valor_realizado` DECIMAL(12,2) NOT NULL,
    `variacao_absoluta` DECIMAL(12,2) NOT NULL,
    `variacao_percentual` DECIMAL(8,4) NOT NULL,
    `valor_acumulado_orcado` DECIMAL(15,2) NOT NULL,
    `valor_acumulado_realizado` DECIMAL(15,2) NOT NULL,
    `variacao_acumulada_absoluta` DECIMAL(15,2) NOT NULL,
    `variacao_acumulada_percentual` DECIMAL(8,4) NOT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_orcamento` (`orcamento_id`),
    INDEX `idx_mes_referencia` (`mes_referencia`),
    UNIQUE KEY `uk_controle_mes` (`orcamento_id`, `mes_referencia`),
    FOREIGN KEY (`orcamento_id`) REFERENCES `orcamento_anual`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 10. FLUXO DE CAIXA PROJETADO
-- ============================================================================

-- Projeção de Fluxo de Caixa
CREATE TABLE IF NOT EXISTS `fluxo_caixa_projetado` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `data_projecao` DATE NOT NULL,
    `tipo` ENUM('entrada', 'saida') NOT NULL,
    `categoria` VARCHAR(100) NOT NULL,
    `descricao` TEXT NOT NULL,
    `valor_projetado` DECIMAL(12,2) NOT NULL,
    `valor_realizado` DECIMAL(12,2) DEFAULT 0.00,
    `origem` VARCHAR(50) COMMENT 'Origem da projeção',
    `origem_id` INT COMMENT 'ID do registro de origem',
    `probabilidade` DECIMAL(5,2) DEFAULT 100.00 COMMENT 'Probabilidade de realização (%)',
    `status` ENUM('projetado', 'confirmado', 'realizado', 'cancelado') DEFAULT 'projetado',
    `data_realizacao` DATE NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX `idx_data_projecao` (`data_projecao`),
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_categoria` (`categoria`),
    INDEX `idx_status` (`status`),
    INDEX `idx_origem` (`origem`, `origem_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 11. ANÁLISE DE CUSTOS E RENTABILIDADE
-- ============================================================================

-- Rateio de Custos
CREATE TABLE IF NOT EXISTS `rateio_custos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `mes_referencia` DATE NOT NULL,
    `conta_origem_id` INT NOT NULL,
    `valor_total_rateio` DECIMAL(12,2) NOT NULL,
    `criterio_rateio` ENUM('proporcional_receita', 'numero_alunos', 'horas_aula', 'area_utilizada', 'manual') NOT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_mes_referencia` (`mes_referencia`),
    INDEX `idx_conta_origem` (`conta_origem_id`),
    FOREIGN KEY (`conta_origem_id`) REFERENCES `plano_contas`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Detalhes do Rateio
CREATE TABLE IF NOT EXISTS `rateio_custos_detalhes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `rateio_id` INT NOT NULL,
    `centro_custo_destino_id` INT NOT NULL,
    `percentual_rateio` DECIMAL(8,4) NOT NULL,
    `valor_rateado` DECIMAL(12,2) NOT NULL,
    `base_calculo` DECIMAL(12,2) COMMENT 'Valor base usado no cálculo do rateio',
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_rateio` (`rateio_id`),
    INDEX `idx_centro_custo_destino` (`centro_custo_destino_id`),
    FOREIGN KEY (`rateio_id`) REFERENCES `rateio_custos`(`id`),
    FOREIGN KEY (`centro_custo_destino_id`) REFERENCES `centro_custos`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Análise de Rentabilidade por Curso
CREATE TABLE IF NOT EXISTS `rentabilidade_cursos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `curso_id` INT NOT NULL,
    `mes_referencia` DATE NOT NULL,
    `numero_alunos` INT NOT NULL,
    `receita_bruta` DECIMAL(12,2) NOT NULL,
    `descontos_bolsas` DECIMAL(12,2) DEFAULT 0.00,
    `receita_liquida` DECIMAL(12,2) NOT NULL,
    `custos_diretos` DECIMAL(12,2) NOT NULL,
    `custos_indiretos` DECIMAL(12,2) NOT NULL,
    `custo_total` DECIMAL(12,2) NOT NULL,
    `margem_contribuicao` DECIMAL(12,2) NOT NULL,
    `margem_contribuicao_percentual` DECIMAL(8,4) NOT NULL,
    `custo_por_aluno` DECIMAL(10,2) NOT NULL,
    `receita_por_aluno` DECIMAL(10,2) NOT NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_curso` (`curso_id`),
    INDEX `idx_mes_referencia` (`mes_referencia`),
    UNIQUE KEY `uk_curso_mes` (`curso_id`, `mes_referencia`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 12. AUDITORIA E CONTROLES INTERNOS
-- ============================================================================

-- Log de Auditoria Financeira
CREATE TABLE IF NOT EXISTS `auditoria_financeira` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `tabela` VARCHAR(100) NOT NULL,
    `registro_id` INT NOT NULL,
    `operacao` ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    `dados_anteriores` JSON,
    `dados_novos` JSON,
    `usuario_id` INT,
    `ip_usuario` VARCHAR(45),
    `user_agent` TEXT,
    `data_operacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_tabela` (`tabela`),
    INDEX `idx_registro_id` (`registro_id`),
    INDEX `idx_operacao` (`operacao`),
    INDEX `idx_usuario` (`usuario_id`),
    INDEX `idx_data_operacao` (`data_operacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Aprovações Hierárquicas
CREATE TABLE IF NOT EXISTS `aprovacoes_hierarquicas` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `tipo_documento` ENUM('conta_pagar', 'transferencia', 'investimento', 'orcamento', 'lancamento_manual') NOT NULL,
    `documento_id` INT NOT NULL,
    `nivel_aprovacao` INT NOT NULL,
    `usuario_aprovador_id` INT NOT NULL,
    `status` ENUM('pendente', 'aprovado', 'rejeitado') DEFAULT 'pendente',
    `data_aprovacao` DATETIME NULL,
    `observacoes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_tipo_documento` (`tipo_documento`),
    INDEX `idx_documento_id` (`documento_id`),
    INDEX `idx_usuario_aprovador` (`usuario_aprovador_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_nivel_aprovacao` (`nivel_aprovacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 13. VIEWS PARA RELATÓRIOS
-- ============================================================================

-- View para DRE Completa
CREATE OR REPLACE VIEW `vw_dre_completa` AS
SELECT
    DATE_FORMAT(lc.data_lancamento, '%Y-%m') as mes_referencia,
    pc.codigo as conta_codigo,
    pc.nome as conta_nome,
    pc.tipo as conta_tipo,
    pc.subtipo as conta_subtipo,
    cc.nome as centro_custo,
    SUM(CASE WHEN pl.tipo_partida = 'debito' AND pc.natureza = 'debito' THEN pl.valor
             WHEN pl.tipo_partida = 'credito' AND pc.natureza = 'credito' THEN pl.valor
             ELSE -pl.valor END) as valor_liquido
FROM lancamentos_contabeis lc
JOIN partidas_lancamentos pl ON lc.id = pl.lancamento_id
JOIN plano_contas pc ON pl.conta_contabil_id = pc.id
LEFT JOIN centro_custos cc ON pl.centro_custo_id = cc.id
WHERE lc.status = 'definitivo'
  AND pc.tipo IN ('receita', 'despesa')
GROUP BY DATE_FORMAT(lc.data_lancamento, '%Y-%m'), pc.id, cc.id;

-- View para Balanço Patrimonial
CREATE OR REPLACE VIEW `vw_balanco_patrimonial` AS
SELECT
    DATE_FORMAT(lc.data_lancamento, '%Y-%m') as mes_referencia,
    pc.codigo as conta_codigo,
    pc.nome as conta_nome,
    pc.tipo as conta_tipo,
    pc.subtipo as conta_subtipo,
    SUM(CASE WHEN pl.tipo_partida = 'debito' AND pc.natureza = 'debito' THEN pl.valor
             WHEN pl.tipo_partida = 'credito' AND pc.natureza = 'credito' THEN pl.valor
             ELSE -pl.valor END) as saldo_atual
FROM lancamentos_contabeis lc
JOIN partidas_lancamentos pl ON lc.id = pl.lancamento_id
JOIN plano_contas pc ON pl.conta_contabil_id = pc.id
WHERE lc.status = 'definitivo'
  AND pc.tipo IN ('ativo', 'passivo', 'patrimonio_liquido')
GROUP BY DATE_FORMAT(lc.data_lancamento, '%Y-%m'), pc.id;

-- ============================================================================
-- 14. INSERÇÃO DE DADOS BÁSICOS
-- ============================================================================

-- Inserir Plano de Contas Básico
INSERT IGNORE INTO `plano_contas` (`codigo`, `nome`, `tipo`, `subtipo`, `nivel`, `natureza`, `aceita_lancamento`) VALUES
-- ATIVO
('1', 'ATIVO', 'ativo', 'grupo', 1, 'debito', FALSE),
('1.1', 'ATIVO CIRCULANTE', 'ativo', 'subgrupo', 2, 'debito', FALSE),
('1.1.1', 'Disponível', 'ativo', 'subgrupo', 3, 'debito', FALSE),
('*********', 'Caixa', 'ativo', 'conta', 4, 'debito', TRUE),
('*********', 'Bancos Conta Movimento', 'ativo', 'conta', 4, 'debito', TRUE),
('*********', 'Aplicações Financeiras', 'ativo', 'conta', 4, 'debito', TRUE),
('1.1.2', 'Realizável a Curto Prazo', 'ativo', 'subgrupo', 3, 'debito', FALSE),
('*********', 'Contas a Receber - Alunos', 'ativo', 'conta', 4, 'debito', TRUE),
('*********', 'Provisão para Devedores Duvidosos', 'ativo', 'conta', 4, 'credito', TRUE),
('1.2', 'ATIVO NÃO CIRCULANTE', 'ativo', 'subgrupo', 2, 'debito', FALSE),
('1.2.1', 'Imobilizado', 'ativo', 'subgrupo', 3, 'debito', FALSE),
('*********', 'Móveis e Utensílios', 'ativo', 'conta', 4, 'debito', TRUE),
('*********', 'Equipamentos de Informática', 'ativo', 'conta', 4, 'debito', TRUE),
('*********', 'Depreciação Acumulada', 'ativo', 'conta', 4, 'credito', TRUE),

-- PASSIVO
('2', 'PASSIVO', 'passivo', 'grupo', 1, 'credito', FALSE),
('2.1', 'PASSIVO CIRCULANTE', 'passivo', 'subgrupo', 2, 'credito', FALSE),
('*********', 'Fornecedores', 'passivo', 'conta', 4, 'credito', TRUE),
('*********', 'Salários a Pagar', 'passivo', 'conta', 4, 'credito', TRUE),
('*********', 'Encargos Sociais a Recolher', 'passivo', 'conta', 4, 'credito', TRUE),
('*********', 'Impostos a Recolher', 'passivo', 'conta', 4, 'credito', TRUE),
('*********', 'Comissões a Pagar - Roxinhos', 'passivo', 'conta', 4, 'credito', TRUE),

-- PATRIMÔNIO LÍQUIDO
('3', 'PATRIMÔNIO LÍQUIDO', 'patrimonio_liquido', 'grupo', 1, 'credito', FALSE),
('3.1.001', 'Capital Social', 'patrimonio_liquido', 'conta', 3, 'credito', TRUE),
('3.2.001', 'Reservas Legais', 'patrimonio_liquido', 'conta', 3, 'credito', TRUE),
('3.2.002', 'Reservas Estatutárias', 'patrimonio_liquido', 'conta', 3, 'credito', TRUE),
('3.3.001', 'Lucros Acumulados', 'patrimonio_liquido', 'conta', 3, 'credito', TRUE),

-- RECEITAS
('4', 'RECEITAS', 'receita', 'grupo', 1, 'credito', FALSE),
('4.1', 'RECEITA OPERACIONAL', 'receita', 'subgrupo', 2, 'credito', FALSE),
('4.1.1.001', 'Mensalidades - Graduação', 'receita', 'conta', 4, 'credito', TRUE),
('4.1.1.002', 'Mensalidades - Pós-Graduação', 'receita', 'conta', 4, 'credito', TRUE),
('4.1.1.003', 'Mensalidades - Extensão', 'receita', 'conta', 4, 'credito', TRUE),
('4.1.2.001', 'Taxas de Matrícula', 'receita', 'conta', 4, 'credito', TRUE),

-- DESPESAS
('5', 'DESPESAS', 'despesa', 'grupo', 1, 'debito', FALSE),
('5.1', 'DESPESAS OPERACIONAIS', 'despesa', 'subgrupo', 2, 'debito', FALSE),
('5.1.1', 'Despesas com Pessoal', 'despesa', 'subgrupo', 3, 'debito', FALSE),
('5.1.1.001', 'Salários', 'despesa', 'conta', 4, 'debito', TRUE),
('5.1.1.002', 'Encargos Sociais', 'despesa', 'conta', 4, 'debito', TRUE),
('5.1.1.003', 'Férias e 13º Salário', 'despesa', 'conta', 4, 'debito', TRUE),
('5.1.2', 'Despesas Administrativas', 'despesa', 'subgrupo', 3, 'debito', FALSE),
('5.1.2.001', 'Aluguéis', 'despesa', 'conta', 4, 'debito', TRUE),
('5.1.2.002', 'Energia Elétrica', 'despesa', 'conta', 4, 'debito', TRUE),
('*********', 'Telefone e Internet', 'despesa', 'conta', 4, 'debito', TRUE),
('5.1.3', 'Despesas Comerciais', 'despesa', 'subgrupo', 3, 'debito', FALSE),
('*********', 'Marketing e Publicidade', 'despesa', 'conta', 4, 'debito', TRUE),
('*********', 'Comissões de Vendas', 'despesa', 'conta', 4, 'debito', TRUE),
('*********', 'Comissões de Roxinhos', 'despesa', 'conta', 4, 'debito', TRUE);

-- Inserir Centro de Custos Básico
INSERT IGNORE INTO `centro_custos` (`codigo`, `nome`, `tipo`) VALUES
('001', 'Administração Geral', 'departamento'),
('002', 'Graduação - Direito', 'curso'),
('003', 'Graduação - Administração', 'curso'),
('004', 'Pós-Graduação', 'curso'),
('005', 'Extensão', 'curso'),
('006', 'Marketing', 'departamento'),
('007', 'Tecnologia da Informação', 'departamento'),
('008', 'Recursos Humanos', 'departamento');

-- ============================================================================
-- SCRIPT FINALIZADO
-- ============================================================================

SELECT 'Estrutura completa do módulo financeiro criada com sucesso!' as status;
