<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - Faciencia</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Moderna */
        .sidebar {
            width: 260px;
            background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 20px;
            transition: width 0.3s ease;
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: var(--white);
            text-decoration: none;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.2);
        }

        .sidebar-menu a i {
            margin-right: 15px;
            width: 25px;
            text-align: center;
        }

        /* Conteúdo Principal */
        .main-content {
            flex-grow: 1;
            margin-left: 260px;
            padding: 30px;
            background-color: var(--light-bg);
            transition: margin-left 0.3s ease;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-purple);
        }

        .report-card {
            background-color: var(--white);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(106, 90, 205, 0.1);
            padding: 25px;
            margin-bottom: 30px;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .stat-item {
            background-color: var(--light-bg);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-10px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-purple);
        }

        .btn-export {
            background-color: var(--primary-purple);
            color: var(--white);
        }

        .btn-export:hover {
            background-color: var(--secondary-purple);
        }

        /* Responsividade */
        @media (max-width: 992px) {
            .sidebar {
                width: 0;
                overflow: hidden;
            }

            .main-content {
                margin-left: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <h2>Faciencia</h2>
            </div>
             <ul class="sidebar-menu">
                <li>
                    <a href="adm_dashboard.html">
                        <i class="fas fa-tachometer-alt"></i> Painel Geral
                    </a>
                </li>
                <li>
                    <a href="adm_polo.html" >
                        <i class="fas fa-globe"></i> Polos
                    </a>
                </li>
                <li>
                    <a href="adm_cursos.html" >
                        <i class="fas fa-book-open"></i> Todos os Cursos
                    </a>
                </li>
                <li>
                    <a href="adm_alunos.html" >
                        <i class="fas fa-users"></i> Alunos
                    </a>
                </li>
                <li>
                    <a href="adm_relatorio.html" class="active">
                        <i class="fas fa-chart-bar"></i> Relatórios Consolidados
                    </a>
                </li>
                <li>
                    <a href="#">
                        <i class="fas fa-cogs"></i> Configurações Globais
                    </a>
                </li>
                <li>
                    <a href="index.html" class="text-danger">
                        <i class="fas fa-sign-out-alt"></i> Sair
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Cabeçalho -->
            <header class="dashboard-header">
                <div>
                    <h1 class="m-0">Relatórios Consolidados</h1>
                    <p class="text-muted mb-0">Visualize e exporte relatórios detalhados</p>
                </div>
                <div>
                    <button class="btn btn-export me-2">
                        <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                    </button>
                    <button class="btn btn-export">
                        <i class="fas fa-file-excel me-2"></i>Exportar Excel
                    </button>
                </div>
            </header>

            <!-- Filtros de Relatório -->
            <div class="report-card mb-4">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <select class="form-select">
                            <option selected>Todos os Polos</option>
                            <option>São Paulo</option>
                            <option>Rio de Janeiro</option>
                            <option>Belo Horizonte</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <select class="form-select">
                            <option selected>Período</option>
                            <option>Últimos 30 dias</option>
                            <option>Últimos 3 meses</option>
                            <option>Ano atual</option>
                            <option>Personalizado</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Buscar relatório">
                            <button class="btn btn-primary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Estatísticas Gerais -->
            <div class="report-card mb-4">
                <h4 class="mb-4">Resumo Geral</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <i class="fas fa-graduation-cap stat-icon"></i>
                        <h6>Total de Cursos</h6>
                        <p class="h4">45</p>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-users stat-icon"></i>
                        <h6>Total de Alunos</h6>
                        <p class="h4">2,345</p>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-globe stat-icon"></i>
                        <h6>Total de Polos</h6>
                        <p class="h4">12</p>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <h6>Novos Alunos (Mês)</h6>
                        <p class="h4">247</p>
                    </div>
                </div>
            </div>

            <!-- Gráfico de Matrículas -->
            <div class="report-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="m-0">Matrículas por Mês</h4>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary active">Ano</button>
                        <button class="btn btn-sm btn-outline-primary">Semestre</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="matriculasChart"></canvas>
                </div>
            </div>

            <!-- Gráfico de Desempenho por Curso -->
            <div class="report-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="m-0">Desempenho por Curso</h4>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary active">Notas</button>
                        <button class="btn btn-sm btn-outline-primary">Conclusão</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="desempenhoCursoChart"></canvas>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS e Dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js para gráficos -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Gráfico de Matrículas
            const ctxMatriculas = document.getElementById('matriculasChart').getContext('2d');
            new Chart(ctxMatriculas, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                    datasets: [{
                        label: 'Matrículas',
                        data: [120, 190, 230, 280, 320, 350, 380, 420, 390, 340, 290, 250],
                        backgroundColor: 'rgba(106, 90, 205, 0.6)',
                        borderColor: 'rgba(106, 90, 205, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Gráfico de Desempenho por Curso
            const ctxDesempenho = document.getElementById('desempenhoCursoChart').getContext('2d');
            new Chart(ctxDesempenho, {
                type: 'radar',
                data: {
                    labels: ['Marketing', 'Desenvolvimento', 'Negócios', 'Design', 'Gestão'],
                    datasets: [{
                        label: 'Nota Média',
                        data: [8.5, 7.9, 8.2, 7.6, 8.0],
                        backgroundColor: 'rgba(106, 90, 205, 0.2)',
                        borderColor: 'rgba(106, 90, 205, 1)',
                        pointBackgroundColor: 'rgba(106, 90, 205, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(106, 90, 205, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scale: {
                        ticks: {
                            beginAtZero: true,
                            max: 10
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>