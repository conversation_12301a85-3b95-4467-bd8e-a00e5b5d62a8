<?php
/**
 * Script para inserir dados de exemplo em todas as tabelas do módulo financeiro
 * Execute após criar as tabelas para ter dados para teste
 */

// Configurações de erro
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Conectar ao banco
require_once '../../secretaria/includes/Database.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    echo "<h2>📊 Inserindo dados de exemplo no módulo financeiro...</h2>\n";
    echo "<p><em>Inserção realizada em: " . date('d/m/Y H:i:s') . "</em></p>\n";
    
    $total_inseridos = 0;
    $total_erros = 0;
    
    // ========================================================================
    // 1. DADOS PARA POLOS
    // ========================================================================
    
    echo "<h3>🏢 Inserindo dados de Polos...</h3>\n";
    
    $polos_data = [
        [1, 'Polo Central', '<EMAIL>', '(11) 1234-5678', 'Rua Principal, 123', 'São Paulo', 'SP', '01234-567', '12.345.678/0001-90', 'João Silva'],
        [2, 'Polo Norte', '<EMAIL>', '(11) 2345-6789', 'Av. Norte, 456', 'São Paulo', 'SP', '02345-678', '23.456.789/0001-01', 'Maria Santos'],
        [3, 'Polo Sul', '<EMAIL>', '(11) 3456-7890', 'Rua Sul, 789', 'São Paulo', 'SP', '03456-789', '34.567.890/0001-12', 'Carlos Oliveira'],
        [4, 'Polo Leste', '<EMAIL>', '(11) 4567-8901', 'Av. Leste, 321', 'São Paulo', 'SP', '04567-890', '45.678.901/0001-23', 'Ana Costa'],
        [5, 'Polo Oeste', '<EMAIL>', '(11) 5678-9012', 'Rua Oeste, 654', 'São Paulo', 'SP', '05678-901', '56.789.012/0001-34', 'Pedro Lima']
    ];
    
    foreach ($polos_data as $polo) {
        try {
            $pdo->prepare("INSERT IGNORE INTO polos (id, nome, email, telefone, endereco, cidade, estado, cep, cnpj, responsavel) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")->execute($polo);
            echo "<p style='color: green;'>✅ Polo: {$polo[1]}</p>\n";
            $total_inseridos++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao inserir polo {$polo[1]}: " . $e->getMessage() . "</p>\n";
            $total_erros++;
        }
    }
    
    // ========================================================================
    // 2. DADOS PARA BOLETOS
    // ========================================================================
    
    echo "<h3>📄 Inserindo dados de Boletos...</h3>\n";
    
    $boletos_data = [
        [1, 1, 'BOL001', '000001', 500.00, '2025-02-15', 'Mensalidade Janeiro 2025', 'João da Silva', '123.456.789-01', 'pendente'],
        [2, 1, 'BOL002', '000002', 750.00, '2025-02-20', 'Taxa de Matrícula', 'Maria Oliveira', '234.567.890-12', 'pendente'],
        [3, 2, 'BOL003', '000003', 600.00, '2025-02-10', 'Mensalidade Janeiro 2025', 'Carlos Santos', '345.678.901-23', 'pago'],
        [4, 2, 'BOL004', '000004', 450.00, '2025-01-15', 'Mensalidade Dezembro 2024', 'Ana Costa', '456.789.012-34', 'vencido'],
        [5, 3, 'BOL005', '000005', 800.00, '2025-02-25', 'Curso de Extensão', 'Pedro Lima', '567.890.123-45', 'pendente']
    ];
    
    foreach ($boletos_data as $boleto) {
        try {
            $pdo->prepare("INSERT IGNORE INTO polos_boletos (id, polo_id, numero_boleto, nosso_numero, valor, data_vencimento, descricao, nome_pagador, cpf_pagador, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")->execute($boleto);
            echo "<p style='color: green;'>✅ Boleto: {$boleto[2]} - R$ {$boleto[4]}</p>\n";
            $total_inseridos++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao inserir boleto {$boleto[2]}: " . $e->getMessage() . "</p>\n";
            $total_erros++;
        }
    }
    
    // ========================================================================
    // 3. DADOS PARA CENTRO DE CUSTOS
    // ========================================================================
    
    echo "<h3>📊 Inserindo dados de Centro de Custos...</h3>\n";
    
    $centros_data = [
        ['CURSO-ADM', 'Curso de Administração', 'curso', 'Prof. João Silva', 'Curso de graduação em Administração', 50000.00, 35000.00],
        ['CURSO-CONT', 'Curso de Contabilidade', 'curso', 'Prof. Maria Santos', 'Curso de graduação em Contabilidade', 40000.00, 28000.00],
        ['DEPTO-ADM', 'Departamento Administrativo', 'departamento', 'Ana Costa', 'Departamento de gestão administrativa', 0.00, 15000.00],
        ['DEPTO-TI', 'Departamento de TI', 'departamento', 'Carlos Oliveira', 'Departamento de tecnologia da informação', 0.00, 12000.00]
    ];
    
    foreach ($centros_data as $centro) {
        try {
            $pdo->prepare("INSERT IGNORE INTO financeiro_centro_custos (codigo, nome, tipo, responsavel, descricao, meta_receita_mensal, meta_custo_mensal) VALUES (?, ?, ?, ?, ?, ?, ?)")->execute($centro);
            echo "<p style='color: green;'>✅ Centro de Custos: {$centro[0]} - {$centro[1]}</p>\n";
            $total_inseridos++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao inserir centro {$centro[0]}: " . $e->getMessage() . "</p>\n";
            $total_erros++;
        }
    }
    
    // ========================================================================
    // 4. DADOS PARA ORÇAMENTO
    // ========================================================================
    
    echo "<h3>📋 Inserindo dados de Orçamento...</h3>\n";
    
    $ano_atual = date('Y');
    $orcamento_data = [
        [$ano_atual, 'mensalidades', 'graduacao', 'Mensalidades de Graduação', 'receita', 80000, 85000, 90000, 85000, 80000, 75000, 70000, 75000, 85000, 90000, 85000, 80000, 980000],
        [$ano_atual, 'pessoal', 'professores', 'Salários de Professores', 'despesa', 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 540000],
        [$ano_atual, 'infraestrutura', 'energia', 'Energia Elétrica', 'despesa', 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 42000]
    ];
    
    foreach ($orcamento_data as $orcamento) {
        try {
            $pdo->prepare("INSERT IGNORE INTO financeiro_orcamento (ano, categoria, subcategoria, descricao, tipo, jan, fev, mar, abr, mai, jun, jul, ago, set, out, nov, dez, valor_total_anual) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")->execute($orcamento);
            echo "<p style='color: green;'>✅ Orçamento: {$orcamento[3]} - R$ " . number_format($orcamento[17], 2, ',', '.') . "</p>\n";
            $total_inseridos++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao inserir orçamento {$orcamento[3]}: " . $e->getMessage() . "</p>\n";
            $total_erros++;
        }
    }
    
    // ========================================================================
    // 5. DADOS PARA METAS
    // ========================================================================
    
    echo "<h3>🎯 Inserindo dados de Metas...</h3>\n";
    
    $metas_data = [
        [$ano_atual, 'receita', 'Meta de Receita Anual', 1200000.00, "$ano_atual-12-31", 'Diretor Financeiro'],
        [$ano_atual, 'margem', 'Meta de Margem Líquida (15%)', 15.00, "$ano_atual-12-31", 'Diretor Financeiro'],
        [$ano_atual, 'crescimento', 'Meta de Crescimento de Alunos (10%)', 10.00, "$ano_atual-12-31", 'Diretor Acadêmico']
    ];
    
    foreach ($metas_data as $meta) {
        try {
            $pdo->prepare("INSERT IGNORE INTO financeiro_metas (ano, tipo_meta, descricao, valor_meta, prazo, responsavel) VALUES (?, ?, ?, ?, ?, ?)")->execute($meta);
            echo "<p style='color: green;'>✅ Meta: {$meta[2]}</p>\n";
            $total_inseridos++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao inserir meta {$meta[2]}: " . $e->getMessage() . "</p>\n";
            $total_erros++;
        }
    }
    
    // ========================================================================
    // 6. DADOS PARA CONTAS A PAGAR
    // ========================================================================
    
    echo "<h3>💸 Inserindo dados de Contas a Pagar...</h3>\n";
    
    $contas_pagar_data = [
        ['Energia Elétrica SP', '12.345.678/0001-90', 'Conta de energia elétrica - Janeiro 2025', 'CONTA-001', 3500.00, 0.00, 3500.00, '2025-02-15', null, 'infraestrutura', 'Conta mensal de energia', 'pendente'],
        ['Fornecedor ABC Ltda', '23.456.789/0001-01', 'Material de escritório', 'NF-12345', 1200.00, 1200.00, 0.00, '2025-01-20', '2025-01-18', 'material', 'Pagamento realizado', 'pago'],
        ['Telefonia XYZ', '34.567.890/0001-12', 'Serviços de telefonia e internet', 'FAT-789', 800.00, 0.00, 800.00, '2025-02-10', null, 'servicos', 'Fatura mensal', 'pendente']
    ];
    
    foreach ($contas_pagar_data as $conta) {
        try {
            $pdo->prepare("INSERT IGNORE INTO financeiro_contas_pagar (fornecedor, cnpj_cpf, descricao, numero_documento, valor_original, valor_pago, valor_pendente, data_vencimento, data_pagamento, categoria, observacoes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")->execute($conta);
            echo "<p style='color: green;'>✅ Conta a Pagar: {$conta[0]} - R$ {$conta[4]}</p>\n";
            $total_inseridos++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao inserir conta a pagar {$conta[0]}: " . $e->getMessage() . "</p>\n";
            $total_erros++;
        }
    }
    
    // ========================================================================
    // 7. DADOS PARA CONTAS A RECEBER
    // ========================================================================
    
    echo "<h3>💰 Inserindo dados de Contas a Receber...</h3>\n";
    
    $contas_receber_data = [
        ['João da Silva', '123.456.789-01', 'Mensalidade Janeiro 2025', 'MENS-001', 500.00, 500.00, 0.00, '2025-01-15', '2025-01-10', 'mensalidade', 'Pagamento em dia', 'recebido'],
        ['Maria Oliveira', '234.567.890-12', 'Mensalidade Janeiro 2025', 'MENS-002', 500.00, 0.00, 500.00, '2025-02-15', null, 'mensalidade', 'Aguardando pagamento', 'pendente'],
        ['Empresa ABC Ltda', '12.345.678/0001-90', 'Curso corporativo', 'CORP-001', 5000.00, 0.00, 5000.00, '2025-02-20', null, 'curso_corporativo', 'Contrato empresarial', 'pendente']
    ];
    
    foreach ($contas_receber_data as $conta) {
        try {
            $pdo->prepare("INSERT IGNORE INTO financeiro_contas_receber (cliente, cpf_cnpj, descricao, numero_documento, valor_original, valor_recebido, valor_pendente, data_vencimento, data_recebimento, categoria, observacoes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")->execute($conta);
            echo "<p style='color: green;'>✅ Conta a Receber: {$conta[0]} - R$ {$conta[4]}</p>\n";
            $total_inseridos++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao inserir conta a receber {$conta[0]}: " . $e->getMessage() . "</p>\n";
            $total_erros++;
        }
    }
    
    echo "<hr>\n";
    echo "<h3>📊 Resumo da Inserção:</h3>\n";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>\n";
    
    echo "<div style='background: #D1FAE5; padding: 15px; border-radius: 8px; text-align: center;'>\n";
    echo "<div style='font-size: 2em; font-weight: bold; color: #065F46;'>$total_inseridos</div>\n";
    echo "<div style='color: #065F46;'>Registros Inseridos</div>\n";
    echo "</div>\n";
    
    echo "<div style='background: #FEE2E2; padding: 15px; border-radius: 8px; text-align: center;'>\n";
    echo "<div style='font-size: 2em; font-weight: bold; color: #991B1B;'>$total_erros</div>\n";
    echo "<div style='color: #991B1B;'>Erros</div>\n";
    echo "</div>\n";
    
    echo "</div>\n";
    
    if ($total_erros === 0) {
        echo "<div style='background: #D1FAE5; border: 1px solid #10B981; color: #065F46; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
        echo "<h4>🎉 Dados inseridos com sucesso!</h4>\n";
        echo "<p>Todos os dados de exemplo foram inseridos corretamente no banco de dados.</p>\n";
        echo "<p>O sistema agora possui dados para teste em todas as funcionalidades.</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #FEF3C7; border: 1px solid #F59E0B; color: #92400E; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
        echo "<h4>⚠️ Inserção concluída com avisos</h4>\n";
        echo "<p>A maioria dos dados foi inserida com sucesso. Alguns erros podem ser normais (dados já existentes).</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Erro de conexão: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inserção de Dados - Módulo Financeiro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h2 {
            color: #333;
            border-bottom: 3px solid #3B82F6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .actions {
            background: #F3F4F6;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            text-align: center;
        }
        .actions a {
            display: inline-block;
            background: #3B82F6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .actions a:hover {
            background: #2563EB;
            transform: translateY(-2px);
        }
        .actions a.success {
            background: #10B981;
        }
        .actions a.success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="actions">
            <h3>🎯 Próximos Passos</h3>
            <a href="../index.php" class="success">🏠 Acessar Módulo Financeiro</a>
            <a href="verificar_tabelas_corrigido.php">🔍 Verificar Sistema</a>
            <a href="../boletos.php">📄 Testar Boletos</a>
            <a href="../centro_custos.php">📊 Testar Centro de Custos</a>
            <a href="../orcamento.php">📋 Testar Orçamento</a>
            
            <div style="margin-top: 20px; padding: 15px; background: #DBEAFE; border-radius: 8px;">
                <h4 style="margin: 0 0 10px 0; color: #1E40AF;">📊 Dados inseridos:</h4>
                <p style="margin: 0; color: #1E40AF; font-size: 0.9em;">
                    ✅ 5 Polos | ✅ 5 Boletos | ✅ 4 Centros de Custos | ✅ 3 Orçamentos | ✅ 3 Metas | ✅ 3 Contas a Pagar | ✅ 3 Contas a Receber
                </p>
            </div>
        </div>
        
        <hr>
        <p style="text-align: center;"><small>Sistema Reinandus - Módulo Financeiro - Dados de Exemplo v1.0</small></p>
    </div>
</body>
</html>
