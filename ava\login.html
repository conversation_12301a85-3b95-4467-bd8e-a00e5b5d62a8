<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Faciencia</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6a0dad;
            --secondary-color: #9370db;
            --dark-color: #4b0082;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            overflow: hidden;
        }

        .login-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            text-align: center;
            padding: 30px;
        }

        .login-form {
            padding: 30px;
        }

        .form-control {
            border-radius: 25px;
            padding: 10px 20px;
        }

        .btn-login {
            border-radius: 25px;
            padding: 10px 20px;
            font-weight: bold;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            transition: transform 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-login:hover {
            transform: scale(1.05);
        }

        .user-type-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .user-type-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            border: 2px solid var(--primary-color);
            background-color: white;
            color: var(--primary-color);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .user-type-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .user-type-btn i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .background-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(106,13,173,0.6), rgba(147,112,219,0.6));
            z-index: 1;
            pointer-events: none;
        }

        #loginForm, #userTypeSelection {
            transition: all 0.3s ease;
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 576px) {
            .login-container {
                width: 95%;
                margin: 0 10px;
            }
        }
    </style>
</head>
<body>
    <div class="background-overlay"></div>
    <div class="login-container">
        <div class="login-header">
            <h2>
                <i class="fas fa-graduation-cap me-2"></i>Faciencia
            </h2>
            <p>Sua plataforma de educação online</p>
        </div>
        
        <div id="userTypeSelection" class="login-form">
            <h4 class="text-center mb-4">Escolha seu tipo de acesso</h4>
            <div class="user-type-buttons">
                <button id="btnAluno" class="user-type-btn">
                    <i class="fas fa-user-graduate"></i>
                    Acesso Aluno
                </button>
                <button id="btnPolo" class="user-type-btn">
                    <i class="fas fa-building"></i>
                    Acesso Polo
                </button>
                <button id="btnAdministrador" class="user-type-btn">
                    <i class="fas fa-user-shield"></i>
                    Acesso Administrador
                </button>
            </div>
        </div>
        
        <form id="loginForm" class="login-form hidden">
            <div class="mb-3">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope me-2"></i>E-mail
                </label>
                <input type="email" class="form-control" id="email" placeholder="Digite seu e-mail" required>
            </div>
            
            <div class="mb-3">
                <label for="senha" class="form-label">
                    <i class="fas fa-lock me-2"></i>Senha
                </label>
                <input type="password" class="form-control" id="senha" placeholder="Digite sua senha" required>
            </div>
            
            <div class="mb-3 d-flex justify-content-between align-items-center">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="manterConectado">
                    <label class="form-check-label" for="manterConectado">Manter conectado</label>
                </div>
                <a href="#" class="text-primary text-decoration-none">Esqueceu a senha?</a>
            </div>
            
            <button type="submit" class="btn btn-login w-100 text-white">
                Entrar <i class="fas fa-sign-in-alt ms-2"></i>
            </button>
            
            <button type="button" id="btnVoltar" class="btn btn-outline-primary w-100 mt-3">
                Voltar <i class="fas fa-arrow-left ms-2"></i>
            </button>
        </form>
    </div>

    <!-- Bootstrap JS e Dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userTypeSelection = document.getElementById('userTypeSelection');
            const loginForm = document.getElementById('loginForm');
            const btnVoltar = document.getElementById('btnVoltar');
            let currentUserType = '';

            // User type selection buttons
            const userTypeButtons = [
                { id: 'btnAluno', type: 'Aluno' },
                { id: 'btnPolo', type: 'Polo' },
                { id: 'btnAdministrador', type: 'Administrador' }
            ];

            userTypeButtons.forEach(button => {
                document.getElementById(button.id).addEventListener('click', function() {
                    currentUserType = button.type;
                    userTypeSelection.classList.add('hidden');
                    loginForm.classList.remove('hidden');
                });
            });

            // Voltar button
            btnVoltar.addEventListener('click', function() {
                loginForm.classList.add('hidden');
                userTypeSelection.classList.remove('hidden');
                currentUserType = '';
            });

            // Form submission
            document.querySelector('#loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const email = document.getElementById('email').value;
                const senha = document.getElementById('senha').value;
                
                // Here you would typically add your authentication logic
                console.log(`Attempting login for ${currentUserType}:`, {
                    email: email,
                    password: senha
                });

                // Simulated redirect based on user type
                switch(currentUserType) {
                    case 'Aluno':
                        window.location.href = 'aluno_dashboard.html';
                        break;
                    case 'Polo':
                        window.location.href = 'polo_dashboard.html';
                        break;
                    case 'Administrador':
                        window.location.href = 'adm_dashboard.html';
                        break;
                }
            });
        });
    </script>
</body>
</html>