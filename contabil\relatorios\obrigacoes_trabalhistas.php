<?php
require_once '../../includes/config.php';

class RelatorioObrigacoesTrabalhistas {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Relatório de provisões trabalhistas
     */
    public function relatorioProvisoes($mes_referencia) {
        $sql = "
            SELECT 
                f.nome as funcionario,
                fp.salario_base,
                fp.provisao_ferias,
                fp.provisao_13_salario,
                fp.fgts,
                (fp.provisao_ferias + fp.provisao_13_salario + fp.fgts) as total_provisoes
            FROM folha_pagamento_itens fp
            JOIN funcionarios f ON fp.funcionario_id = f.id
            JOIN folha_pagamento fol ON fp.folha_id = fol.id
            WHERE CONCAT(fol.ano_referencia, '-', LPAD(fol.mes_referencia, 2, '0')) = ?
            ORDER BY f.nome
        ";
        
        return $this->db->fetchAll($sql, [$mes_referencia]);
    }
    
    /**
     * Resumo de impostos trabalhistas
     */
    public function resumoImpostos($mes_referencia) {
        $sql = "
            SELECT 
                SUM(fp.inss_funcionario) as total_inss_funcionario,
                SUM(fp.inss_empresa) as total_inss_empresa,
                SUM(fp.fgts) as total_fgts,
                SUM(fp.irrf) as total_irrf,
                COUNT(*) as total_funcionarios
            FROM folha_pagamento_itens fp
            JOIN folha_pagamento fol ON fp.folha_id = fol.id
            WHERE CONCAT(fol.ano_referencia, '-', LPAD(fol.mes_referencia, 2, '0')) = ?
        ";
        
        return $this->db->fetch($sql, [$mes_referencia]);
    }
}

// Interface web
$relatorio = new RelatorioObrigacoesTrabalhistas();
$mes_atual = date('Y-m');

if ($_GET['mes'] ?? '') {
    $mes_selecionado = $_GET['mes'];
} else {
    $mes_selecionado = $mes_atual;
}

$provisoes = $relatorio->relatorioProvisoes($mes_selecionado);
$resumo = $relatorio->resumoImpostos($mes_selecionado);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Relatório - Obrigações Trabalhistas</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body>
    <div class="container">
        <h1>📊 Obrigações Trabalhistas</h1>
        
        <!-- Filtros -->
        <div class="card">
            <form method="GET">
                <div class="form-group inline">
                    <label>Mês/Ano:</label>
                    <input type="month" name="mes" value="<?= $mes_selecionado ?>">
                    <button type="submit" class="btn btn-primary">Filtrar</button>
                </div>
            </form>
        </div>
        
        <!-- Resumo -->
        <div class="card">
            <h2>Resumo do Período</h2>
            <div class="row">
                <div class="col-3">
                    <div class="metric-card">
                        <h3>INSS Funcionários</h3>
                        <p class="metric-value">R$ <?= number_format($resumo['total_inss_funcionario'] ?? 0, 2, ',', '.') ?></p>
                    </div>
                </div>
                <div class="col-3">
                    <div class="metric-card">
                        <h3>FGTS</h3>
                        <p class="metric-value">R$ <?= number_format($resumo['total_fgts'] ?? 0, 2, ',', '.') ?></p>
                    </div>
                </div>
                <div class="col-3">
                    <div class="metric-card">
                        <h3>IRRF</h3>
                        <p class="metric-value">R$ <?= number_format($resumo['total_irrf'] ?? 0, 2, ',', '.') ?></p>
                    </div>
                </div>
                <div class="col-3">
                    <div class="metric-card">
                        <h3>Funcionários</h3>
                        <p class="metric-value"><?= $resumo['total_funcionarios'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Detalhamento por Funcionário -->
        <div class="card">
            <h2>Provisões por Funcionário</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Funcionário</th>
                        <th>Salário Base</th>
                        <th>Provisão Férias</th>
                        <th>Provisão 13º</th>
                        <th>FGTS</th>
                        <th>Total Provisões</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($provisoes as $provisao): ?>
                    <tr>
                        <td><?= $provisao['funcionario'] ?></td>
                        <td>R$ <?= number_format($provisao['salario_base'], 2, ',', '.') ?></td>
                        <td>R$ <?= number_format($provisao['provisao_ferias'], 2, ',', '.') ?></td>
                        <td>R$ <?= number_format($provisao['provisao_13_salario'], 2, ',', '.') ?></td>
                        <td>R$ <?= number_format($provisao['fgts'], 2, ',', '.') ?></td>
                        <td><strong>R$ <?= number_format($provisao['total_provisoes'], 2, ',', '.') ?></strong></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>