<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administração de Licenças - Faciencia EAD</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
            background-color: var(--light-bg);
        }

        /* Sidebar Moderna */
        .sidebar {
            background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .sidebar-logo img {
            max-width: 50px;
            margin-right: 10px;
        }

        .sidebar-menu {
            list-style: none;
            flex-grow: 1;
            padding-left: 0;
        }

        .sidebar-menu li {
            margin-bottom: 10px;
        }

        .sidebar-menu a {
            color: var(--white);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.2);
        }

        .sidebar-menu a i {
            margin-right: 15px;
            font-size: 1.2rem;
            width: 30px;
            text-align: center;
        }

        .sidebar-section {
            margin-bottom: 20px;
        }

        .sidebar-section-header {
            font-size: 0.9rem;
            text-transform: uppercase;
            color: rgba(255,255,255,0.6);
            margin-left: 15px;
            margin-bottom: 10px;
        }

        /* Conteúdo Principal */
        .main-content {
            background-color: var(--white);
            padding: 30px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-purple);
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-info img {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid var(--primary-purple);
        }

        .admin-card {
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(106, 90, 205, 0.1);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .admin-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-purple), var(--light-purple));
        }

        .license-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .search-box {
            position: relative;
            max-width: 300px;
        }

        .search-box input {
            padding-left: 40px;
            border-radius: 20px;
            border: 1px solid #ddd;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .license-tabs .nav-link {
            padding: 12px 20px;
            border-radius: 10px 10px 0 0;
            font-weight: 500;
            color: var(--text-dark);
        }

        .license-tabs .nav-link.active {
            background-color: var(--primary-purple);
            color: white;
            border: none;
        }

        .license-table th {
            background-color: var(--light-bg);
            color: var(--text-dark);
            font-weight: 600;
            border: none;
        }

        .license-table td {
            vertical-align: middle;
            border-bottom: 1px solid #eee;
        }

        .license-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-premium {
            background-color: #8A2BE2;
            color: white;
        }

        .badge-standard {
            background-color: #4682B4;
            color: white;
        }

        .badge-basic {
            background-color: #20B2AA;
            color: white;
        }

        .badge-expired {
            background-color: #777;
            color: white;
        }

        .progress-thin {
            height: 6px;
            margin-top: 5px;
            border-radius: 3px;
        }

        .quota-text {
            font-size: 0.8rem;
            display: flex;
            justify-content: space-between;
            margin-top: 3px;
        }

        .license-action-btn {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
            transition: all 0.2s;
        }

        .license-action-btn:hover {
            transform: translateY(-2px);
        }

        .status-pill {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .modal-header {
            background-color: var(--primary-purple);
            color: white;
        }

        .plan-card {
            border: 2px solid #eee;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .plan-card:hover, .plan-card.selected {
            border-color: var(--primary-purple);
            box-shadow: 0 5px 20px rgba(106, 90, 205, 0.1);
        }

        .plan-card.selected {
            background-color: rgba(106, 90, 205, 0.05);
        }

        .plan-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .plan-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-purple);
        }

        .plan-details {
            margin-top: 15px;
        }

        .plan-details .feature-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .plan-details .feature-item i {
            color: var(--success-green);
            margin-right: 10px;
        }

        .notification-icon {
            position: relative;
            margin-right: 20px;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
        }

        .summary-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 1.5rem;
        }

        .bg-purple {
            background-color: var(--primary-purple);
        }

        .bg-blue {
            background-color: var(--info-blue);
        }

        .bg-green {
            background-color: var(--success-green);
        }

        .bg-yellow {
            background-color: var(--warning-yellow);
        }

        .summary-text h4 {
            font-size: 1.4rem;
            margin-bottom: 5px;
        }

        .summary-text p {
            font-size: 0.9rem;
            color: #777;
            margin-bottom: 0;
        }

        /* Responsividade */
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                height: 100vh;
                width: 280px;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 15px;
            }

            .summary-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-grid">
        <!-- Sidebar de Administração Geral -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-graduation-cap fa-2x"></i>
                <h2 class="ms-2">Faciencia</h2>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Principal</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> Painel Geral
                        </a>
                    </li>
                    <li>
                        <a href="adm_license.html" class="active">
                            <i class="fas fa-id-card"></i> Licenciamento
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Gerenciamento</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_polo.html">
                            <i class="fas fa-globe"></i> Polos
                        </a>
                    </li>
                    <li>
                        <a href="adm_cursos.html">
                            <i class="fas fa-book-open"></i> Cursos
                        </a>
                    </li>
                    <li>
                        <a href="adm_alunos.html">
                            <i class="fas fa-users"></i> Alunos
                        </a>
                    </li>
                    <li>
                        <a href="adm_professores.html">
                            <i class="fas fa-chalkboard-teacher"></i> Professores
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Relatórios</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_relatorio_financeiro.html">
                            <i class="fas fa-dollar-sign"></i> Financeiro
                        </a>
                    </li>
                    <li>
                        <a href="adm_relatorio_desempenho.html">
                            <i class="fas fa-chart-line"></i> Desempenho
                        </a>
                    </li>
                    <li>
                        <a href="adm_relatorio_licencas.html">
                            <i class="fas fa-id-badge"></i> Licenças
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Sistema</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_configuracoes.html">
                            <i class="fas fa-cogs"></i> Configurações
                        </a>
                    </li>
                    <li>
                        <a href="index.html" class="text-danger">
                            <i class="fas fa-sign-out-alt"></i> Sair
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Cabeçalho do Painel -->
            <header class="dashboard-header">
                <h1 class="m-0">Gerenciamento de Licenças</h1>
                <div class="d-flex align-items-center">
                    <div class="notification-icon">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="user-info">
                        <img src="/api/placeholder/45/45" alt="Foto de Perfil">
                        <div>
                            <h6 class="m-0">Admin Master</h6>
                            <small class="text-muted">Administrador</small>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Resumo das Licenças -->
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-icon bg-purple">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div class="summary-text">
                        <h4>12</h4>
                        <p>Total de Licenças</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon bg-green">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="summary-text">
                        <h4>9</h4>
                        <p>Licenças Ativas</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon bg-yellow">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="summary-text">
                        <h4>3</h4>
                        <p>Expirando em 15 dias</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon bg-blue">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="summary-text">
                        <h4>R$ 9.800</h4>
                        <p>Renovações este mês</p>
                    </div>
                </div>
            </div>

            <!-- Gerenciador de Licenças -->
            <section class="admin-card">
                <div class="license-header">
                    <h4 class="m-0">Licenças do Sistema</h4>
                    <div class="d-flex">
                        <div class="search-box me-3">
                            <input type="text" class="form-control" placeholder="Buscar polo...">
                            <i class="fas fa-search"></i>
                        </div>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLicenseModal">
                            <i class="fas fa-plus me-2"></i>Nova Licença
                        </button>
                    </div>
                </div>

                <ul class="nav nav-tabs license-tabs mb-4">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#all-licenses">Todas (12)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#active-licenses">Ativas (9)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#expiring-licenses">Expirando (3)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#expired-licenses">Expiradas (3)</a>
                    </li>
                </ul>

                <div class="tab-content">
                    <div class="tab-pane fade show active" id="all-licenses">
                        <div class="table-responsive">
                            <table class="table license-table">
                                <thead>
                                    <tr>
                                        <th>Polo</th>
                                        <th>Plano</th>
                                        <th>Uso da Licença</th>
                                        <th>Data de Ativação</th>
                                        <th>Expira em</th>
                                        <th>Status</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="/api/placeholder/35/35" class="rounded-circle me-2" alt="Polo">
                                                <div>
                                                    <h6 class="mb-0">Polo São Paulo</h6>
                                                    <small class="text-muted">ID: SP001</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="license-badge badge-premium">Premium</span></td>
                                        <td>
                                            <div>Alunos (423/500)</div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-success" style="width: 85%"></div>
                                            </div>
                                            <div class="quota-text">
                                                <span>Cursos</span>
                                                <span>15/25</span>
                                            </div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-success" style="width: 60%"></div>
                                            </div>
                                        </td>
                                        <td>10/01/2024</td>
                                        <td>15/12/2024</td>
                                        <td><span class="status-pill bg-success">Ativo</span></td>
                                        <td>
                                            <button class="btn btn-light license-action-btn" title="Editar">
                                                <i class="fas fa-edit text-primary"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Renovar">
                                                <i class="fas fa-sync-alt text-success"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Detalhes">
                                                <i class="fas fa-info-circle text-info"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="/api/placeholder/35/35" class="rounded-circle me-2" alt="Polo">
                                                <div>
                                                    <h6 class="mb-0">Polo Rio de Janeiro</h6>
                                                    <small class="text-muted">ID: RJ001</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="license-badge badge-standard">Standard</span></td>
                                        <td>
                                            <div>Alunos (356/400)</div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-warning" style="width: 89%"></div>
                                            </div>
                                            <div class="quota-text">
                                                <span>Cursos</span>
                                                <span>12/15</span>
                                            </div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-warning" style="width: 80%"></div>
                                            </div>
                                        </td>
                                        <td>15/02/2024</td>
                                        <td>30/11/2024</td>
                                        <td><span class="status-pill bg-warning text-dark">Expira em 15 dias</span></td>
                                        <td>
                                            <button class="btn btn-light license-action-btn" title="Editar">
                                                <i class="fas fa-edit text-primary"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Renovar">
                                                <i class="fas fa-sync-alt text-success"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Detalhes">
                                                <i class="fas fa-info-circle text-info"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="/api/placeholder/35/35" class="rounded-circle me-2" alt="Polo">
                                                <div>
                                                    <h6 class="mb-0">Polo Belo Horizonte</h6>
                                                    <small class="text-muted">ID: BH001</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="license-badge badge-premium">Premium</span></td>
                                        <td>
                                            <div>Alunos (487/500)</div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-danger" style="width: 97%"></div>
                                            </div>
                                            <div class="quota-text">
                                                <span>Cursos</span>
                                                <span>10/25</span>
                                            </div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-success" style="width: 40%"></div>
                                            </div>
                                        </td>
                                        <td>05/03/2024</td>
                                        <td>15/01/2025</td>
                                        <td><span class="status-pill bg-success">Ativo</span></td>
                                        <td>
                                            <button class="btn btn-light license-action-btn" title="Editar">
                                                <i class="fas fa-edit text-primary"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Renovar">
                                                <i class="fas fa-sync-alt text-success"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Detalhes">
                                                <i class="fas fa-info-circle text-info"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="/api/placeholder/35/35" class="rounded-circle me-2" alt="Polo">
                                                <div>
                                                    <h6 class="mb-0">Polo Curitiba</h6>
                                                    <small class="text-muted">ID: CT001</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="license-badge badge-basic">Basic</span></td>
                                        <td>
                                            <div>Alunos (245/300)</div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-info" style="width: 82%"></div>
                                            </div>
                                            <div class="quota-text">
                                                <span>Cursos</span>
                                                <span>8/10</span>
                                            </div>
                                            <div class="progress progress-thin">
                                                <div class="progress-bar bg-warning" style="width: 80%"></div>
                                            </div>
                                        </td>
                                        <td>20/01/2024</td>
                                        <td>20/01/2024</td>
                                        <td><span class="status-pill bg-secondary">Expirado</span></td>
                                        <td>
                                            <button class="btn btn-light license-action-btn" title="Editar">
                                                <i class="fas fa-edit text-primary"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Renovar">
                                                <i class="fas fa-sync-alt text-success"></i>
                                            </button>
                                            <button class="btn btn-light license-action-btn" title="Detalhes">
                                                <i class="fas fa-info-circle text-info"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>Mostrando 4 de 12 licenças</div>
                            <nav>
                                <ul class="pagination">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">Próximo</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="active-licenses">
                        <!-- Conteúdo para licenças ativas -->
                    </div>
                    
                    <div class="tab-pane fade" id="expiring-licenses">
                        <!-- Conteúdo para licenças expirando -->
                    </div>
                    
                    <div class="tab-pane fade" id="expired-licenses">
                        <!-- Conteúdo para licenças expiradas -->
                    </div>
                </div>
            </section>

            <!-- Histórico de Atualizações de Licenças -->
            <section class="admin-card">
                <h4 class="mb-4">Histórico de Atividades</h4>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Polo</th>
                                <th>Ação</th>
                                <th>Usuário</th>
                                <th>Detalhes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>20/04/2024 15:30</td>
                                <td>Polo São Paulo</td>
                                <td><span class="badge bg-info">Atualização de Limites</span></td>
                                <td>Admin Master</td>
                                <td>Aumento do limite de alunos de 450 para 500</td>
                            </tr>
                            <tr>
                                <td>15/04/2024 10:15</td>
                                <td>Polo Rio de Janeiro</td>
                                <td><span class="badge bg-success">Renovação</span></td>
                                <td>Admin Master</td>
                                <td>Renovação por 6 meses do plano Standard</td>
                            </tr>
                            <tr>
                                <td>10/04/2024 09:22</td>
                                <td>Polo Belo Horizonte</td>
                                <td><span class="badge bg-primary">Upgrade</span></td>
                                <td>Admin Master</td>
                                <td>Upgrade de plano Standard para Premium</td>
                            </tr>
                            <tr>
                                <td>05/04/2024 16:45</td>
                                <td>Polo Recife</td>
                                <td><span class="badge bg-warning text-dark">Notificação</span></td>
                                <td>Sistema</td>
                                <td>Licença prestes a expirar (15 dias restantes)</td>
                            </tr>
                            <tr>
                                <td>02/04/2024 11:30</td>
                                <td>Polo Fortaleza</td>
                                <td><span class="badge bg-danger">Expirada</span></td>
                                <td>Sistema</td>
                                <td>Licença expirou, recursos limitados ativos</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Modal de Adicionar Nova Licença -->
    <div class="modal fade" id="addLicenseModal" tabindex="-1" aria-labelledby="addLicenseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addLicenseModalLabel">Adicionar Nova Licença</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <!-- Etapa 1: Selecionar Polo -->
                        <div class="mb-4">
                            <h5 class="mb-3">1. Selecione o Polo</h5>
                            <div class="mb-3">
                                <label for="poloSelect" class="form-label">Polo</label>
                                <select class="form-select" id="poloSelect">
                                    <option selected disabled>Selecione o polo...</option>
                                    <option value="1">Polo Salvador</option>
                                    <option value="2">Polo Manaus</option>
                                    <option value="3">Polo Brasília</option>
                                    <option value="4">Polo Porto Alegre</option>
                                    <option value="5">Novo Polo</option>
                                </select>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="createNewPolo">
                                <label class="form-check-label" for="createNewPolo">
                                    Criar novo polo
                                </label>
                            </div>
                            <div id="newPoloInfo" class="d-none">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="poloName" class="form-label">Nome do Polo</label>
                                        <input type="text" class="form-control" id="poloName">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="poloCode" class="form-label">Código</label>
                                        <input type="text" class="form-control" id="poloCode">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="poloCity" class="form-label">Cidade</label>
                                        <input type="text" class="form-control" id="poloCity">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="poloState" class="form-label">Estado</label>
                                        <select class="form-select" id="poloState">
                                            <option selected disabled>Selecione...</option>
                                            <option>SP</option>
                                            <option>RJ</option>
                                            <option>MG</option>
                                            <!-- Outros estados -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Etapa 2: Selecionar Plano -->
                        <div class="mb-4">
                            <h5 class="mb-3">2. Selecione o Plano</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="plan-card" data-plan="basic">
                                        <div class="plan-header">
                                            <h5>Básico</h5>
                                            <span class="license-badge badge-basic">Basic</span>
                                        </div>
                                        <div class="plan-price">R$ 299/mês</div>
                                        <div class="plan-details">
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 300 alunos</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 10 cursos</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 5 professores</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Suporte básico</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="plan-card selected" data-plan="standard">
                                        <div class="plan-header">
                                            <h5>Padrão</h5>
                                            <span class="license-badge badge-standard">Standard</span>
                                        </div>
                                        <div class="plan-price">R$ 599/mês</div>
                                        <div class="plan-details">
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 400 alunos</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 15 cursos</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 10 professores</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Suporte prioritário</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="plan-card" data-plan="premium">
                                        <div class="plan-header">
                                            <h5>Premium</h5>
                                            <span class="license-badge badge-premium">Premium</span>
                                        </div>
                                        <div class="plan-price">R$ 999/mês</div>
                                        <div class="plan-details">
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 500 alunos</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 25 cursos</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Até 15 professores</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Suporte VIP 24/7</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Etapa 3: Definir Período -->
                        <div class="mb-4">
                            <h5 class="mb-3">3. Defina o Período</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="startDate" class="form-label">Data de Início</label>
                                    <input type="date" class="form-control" id="startDate">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="duration" class="form-label">Duração</label>
                                    <select class="form-select" id="duration">
                                        <option value="3">3 meses</option>
                                        <option value="6" selected>6 meses</option>
                                        <option value="12">1 ano</option>
                                        <option value="24">2 anos</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="endDate" class="form-label">Data de Término</label>
                                    <input type="date" class="form-control" id="endDate" disabled>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="renewalAlert" class="form-label">Alerta de Renovação</label>
                                    <select class="form-select" id="renewalAlert">
                                        <option value="7">7 dias antes</option>
                                        <option value="15" selected>15 dias antes</option>
                                        <option value="30">30 dias antes</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Etapa 4: Informações de Pagamento -->
                        <div>
                            <h5 class="mb-3">4. Informações de Pagamento</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="paymentMethod" class="form-label">Método de Pagamento</label>
                                    <select class="form-select" id="paymentMethod">
                                        <option value="creditCard">Cartão de Crédito</option>
                                        <option value="bankTransfer">Transferência Bancária</option>
                                        <option value="boleto">Boleto</option>
                                        <option value="pix">PIX</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="invoice" class="form-label">Gerar Fatura</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" id="invoice" checked>
                                        <label class="form-check-label" for="invoice">Sim, gerar fatura</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="notes" class="form-label">Observações</label>
                                <textarea class="form-control" id="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary">Adicionar Licença</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Renovação de Licença -->
    <div class="modal fade" id="renewLicenseModal" tabindex="-1" aria-labelledby="renewLicenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="renewLicenseModalLabel">Renovar Licença</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img src="/api/placeholder/80/80" class="rounded-circle mb-3" alt="Polo">
                        <h4>Polo Rio de Janeiro</h4>
                        <p class="text-muted">Plano Standard - Expira em 15 dias</p>
                    </div>
                    
                    <form>
                        <div class="mb-3">
                            <label for="renewDuration" class="form-label">Período de Renovação</label>
                            <select class="form-select" id="renewDuration">
                                <option value="3">3 meses</option>
                                <option value="6" selected>6 meses</option>
                                <option value="12">1 ano</option>
                                <option value="24">2 anos</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="upgradePlan" class="form-label">Atualizar Plano?</label>
                            <select class="form-select" id="upgradePlan">
                                <option value="current" selected>Manter plano atual (Standard)</option>
                                <option value="premium">Upgrade para Premium (+R$ 400/mês)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="totalPrice" class="form-label">Valor Total</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control" id="totalPrice" value="3.594,00" disabled>
                            </div>
                            <small class="text-muted">6 meses × R$ 599,00/mês</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success">Confirmar Renovação</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Detalhes da Licença -->
    <div class="modal fade" id="licenseDetailsModal" tabindex="-1" aria-labelledby="licenseDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="licenseDetailsModalLabel">Detalhes da Licença</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informações do Polo</h5>
                            <table class="table table-sm">
                                <tr>
                                    <th>Nome:</th>
                                    <td>Polo São Paulo</td>
                                </tr>
                                <tr>
                                    <th>ID:</th>
                                    <td>SP001</td>
                                </tr>
                                <tr>
                                    <th>Endereço:</th>
                                    <td>Av. Paulista, 1000</td>
                                </tr>
                                <tr>
                                    <th>Responsável:</th>
                                    <td>Carlos Oliveira</td>
                                </tr>
                                <tr>
                                    <th>Contato:</th>
                                    <td><EMAIL></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Detalhes da Licença</h5>
                            <table class="table table-sm">
                                <tr>
                                    <th>Plano:</th>
                                    <td><span class="license-badge badge-premium">Premium</span></td>
                                </tr>
                                <tr>
                                    <th>Ativação:</th>
                                    <td>10/01/2024</td>
                                </tr>
                                <tr>
                                    <th>Vencimento:</th>
                                    <td>15/12/2024</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td><span class="status-pill bg-success">Ativo</span></td>
                                </tr>
                                <tr>
                                    <th>Última renovação:</th>
                                    <td>10/01/2024</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <h5 class="mb-3">Utilização de Recursos</h5>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Alunos</h6>
                                    <div class="d-flex justify-content-center mb-2">
                                        <div style="position: relative; width: 120px; height: 120px;">
                                            <div class="position-absolute top-50 start-50 translate-middle">
                                                <h3>85%</h3>
                                            </div>
                                            <canvas id="alunosChart" width="120" height="120"></canvas>
                                        </div>
                                    </div>
                                    <p class="card-text">423 de 500 utilizados</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Cursos</h6>
                                    <div class="d-flex justify-content-center mb-2">
                                        <div style="position: relative; width: 120px; height: 120px;">
                                            <div class="position-absolute top-50 start-50 translate-middle">
                                                <h3>60%</h3>
                                            </div>
                                            <canvas id="cursosChart" width="120" height="120"></canvas>
                                        </div>
                                    </div>
                                    <p class="card-text">15 de 25 utilizados</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Professores</h6>
                                    <div class="d-flex justify-content-center mb-2">
                                        <div style="position: relative; width: 120px; height: 120px;">
                                            <div class="position-absolute top-50 start-50 translate-middle">
                                                <h3>80%</h3>
                                            </div>
                                            <canvas id="professoresChart" width="120" height="120"></canvas>
                                        </div>
                                    </div>
                                    <p class="card-text">12 de 15 utilizados</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="mb-3">Histórico de Transações</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Transação</th>
                                    <th>Valor</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>10/01/2024</td>
                                    <td>Renovação Premium - 12 meses</td>
                                    <td>R$ 11.988,00</td>
                                    <td><span class="badge bg-success">Pago</span></td>
                                </tr>
                                <tr>
                                    <td>15/12/2023</td>
                                    <td>Upgrade Standard → Premium</td>
                                    <td>R$ 2.400,00</td>
                                    <td><span class="badge bg-success">Pago</span></td>
                                </tr>
                                <tr>
                                    <td>10/01/2023</td>
                                    <td>Renovação Standard - 12 meses</td>
                                    <td>R$ 7.188,00</td>
                                    <td><span class="badge bg-success">Pago</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#renewLicenseModal">Renovar Licença</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS e Dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js para os gráficos de detalhes da licença -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    
    <script>
        // Script para mostrar/ocultar o formulário de novo polo
        document.getElementById('createNewPolo').addEventListener('change', function() {
            const newPoloInfo = document.getElementById('newPoloInfo');
            if (this.checked) {
                newPoloInfo.classList.remove('d-none');
                document.getElementById('poloSelect').disabled = true;
            } else {
                newPoloInfo.classList.add('d-none');
                document.getElementById('poloSelect').disabled = false;
            }
        });
        
        // Script para selecionar plano
        document.querySelectorAll('.plan-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.plan-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // Script para mostrar data de término com base na duração
        document.getElementById('duration').addEventListener('change', function() {
            // Implementação da lógica de cálculo de data de término
            const startDate = new Date(document.getElementById('startDate').value);
            const duration = parseInt(this.value);
            
            if (!isNaN(startDate.getTime())) {
                const endDate = new Date(startDate);
                endDate.setMonth(endDate.getMonth() + duration);
                
                const formattedEndDate = endDate.toISOString().split('T')[0];
                document.getElementById('endDate').value = formattedEndDate;
            }
        });
        
        // Inicialização dos gráficos de donut para detalhes da licença
        // (Estes seriam inicializados quando o modal de detalhes for aberto)
        /*
        function initCharts() {
            const alunosChart = new Chart(document.getElementById('alunosChart'), {
                type: 'doughnut',
                data: {
                    labels: ['Utilizados', 'Disponíveis'],
                    datasets: [{
                        data: [85, 15],
                        backgroundColor: ['#6A5ACD', '#eee']
                    }]
                },
                options: {
                    cutout: '75%',
                    plugins: { legend: { display: false } }
                }
            });
            
            const cursosChart = new Chart(document.getElementById('cursosChart'), {
                type: 'doughnut',
                data: {
                    labels: ['Utilizados', 'Disponíveis'],
                    datasets: [{
                        data: [60, 40],
                        backgroundColor: ['#4682B4', '#eee']
                    }]
                },
                options: {
                    cutout: '75%',
                    plugins: { legend: { display: false } }
                }
            });
            
            const professoresChart = new Chart(document.getElementById('professoresChart'), {
                type: 'doughnut',
                data: {
                    labels: ['Utilizados', 'Disponíveis'],
                    datasets: [{
                        data: [80, 20],
                        backgroundColor: ['#20B2AA', '#eee']
                    }]
                },
                options: {
                    cutout: '75%',
                    plugins: { legend: { display: false } }
                }
            });
        }
        */
    </script>
</body>
</html>