<?php
/**
 * Verificador corrigido das tabelas do módulo financeiro
 * Este script verifica se todas as tabelas estão criadas corretamente
 */

// Configurações de erro
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Conectar ao banco
require_once '../../secretaria/includes/Database.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Lista de todas as tabelas necessárias para o módulo financeiro
    $tabelas_necessarias = [
        // Tabelas básicas do financeiro
        'financeiro_contas_pagar' => 'Contas a Pagar',
        'financeiro_contas_receber' => 'Contas a Receber',
        'financeiro_movimentacao_bancaria' => 'Movimentação Bancária',
        'financeiro_impostos_recolher' => 'Impostos a Recolher',
        'financeiro_retencoes_fonte' => 'Retenções na Fonte',
        
        // Tabelas das páginas implementadas
        'financeiro_ativo_nao_circulante' => 'Ativo Não Circulante',
        'financeiro_centro_custos' => 'Centro de Custos',
        'financeiro_alocacao_custos' => 'Alocação de Custos',
        'financeiro_fluxo_projetado' => 'Fluxo de Caixa Projetado',
        'financeiro_aplicacoes' => 'Aplicações Financeiras',
        'financeiro_cenarios' => 'Cenários de Planejamento',
        'financeiro_orcamento' => 'Orçamento Anual',
        'financeiro_orcamento_realizado' => 'Orçamento Realizado',
        'financeiro_metas' => 'Metas Financeiras',
        
        // Tabelas de boletos
        'polos' => 'Polos da Instituição',
        'polos_boletos' => 'Boletos dos Polos'
    ];
    
    echo "<h2>🔍 Verificação Corrigida das Tabelas do Módulo Financeiro</h2>\n";
    echo "<p><em>Verificação realizada em: " . date('d/m/Y H:i:s') . "</em></p>\n";
    
    $tabelas_existentes = 0;
    $tabelas_faltantes = 0;
    $tabelas_com_erro = [];
    $detalhes_tabelas = [];
    
    foreach ($tabelas_necessarias as $tabela => $descricao) {
        try {
            // Verificar se a tabela existe usando SHOW TABLES sem prepared statement
            $stmt = $pdo->query("SHOW TABLES LIKE '$tabela'");
            $result = $stmt->fetch();
            
            if ($result) {
                // Contar registros na tabela
                try {
                    $count_stmt = $pdo->query("SELECT COUNT(*) as total FROM `$tabela`");
                    $count_result = $count_stmt->fetch(PDO::FETCH_ASSOC);
                    $total_registros = $count_result['total'] ?? 0;
                    
                    // Obter informações da estrutura da tabela
                    $structure_stmt = $pdo->query("DESCRIBE `$tabela`");
                    $columns = $structure_stmt->fetchAll(PDO::FETCH_ASSOC);
                    $total_colunas = count($columns);
                    
                    echo "<p style='color: green;'>✅ <strong>$tabela</strong> - $descricao</p>\n";
                    echo "<div style='margin-left: 20px; color: #666; font-size: 0.9em;'>\n";
                    echo "📊 $total_registros registros | 🏗️ $total_colunas colunas\n";
                    echo "</div>\n";
                    
                    $detalhes_tabelas[$tabela] = [
                        'existe' => true,
                        'registros' => $total_registros,
                        'colunas' => $total_colunas,
                        'estrutura' => $columns
                    ];
                    
                    $tabelas_existentes++;
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ <strong>$tabela</strong> - $descricao (existe, mas erro ao contar: " . htmlspecialchars($e->getMessage()) . ")</p>\n";
                    $tabelas_existentes++;
                }
            } else {
                echo "<p style='color: red;'>❌ <strong>$tabela</strong> - $descricao (NÃO EXISTE)</p>\n";
                $tabelas_faltantes++;
                $detalhes_tabelas[$tabela] = ['existe' => false];
            }
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ <strong>$tabela</strong> - $descricao (ERRO: " . htmlspecialchars($e->getMessage()) . ")</p>\n";
            $tabelas_com_erro[] = $tabela;
        }
    }
    
    echo "<hr>\n";
    echo "<h3>📊 Resumo da Verificação:</h3>\n";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>\n";
    
    echo "<div style='background: #F3F4F6; padding: 15px; border-radius: 8px; text-align: center;'>\n";
    echo "<div style='font-size: 2em; font-weight: bold; color: #374151;'>" . count($tabelas_necessarias) . "</div>\n";
    echo "<div style='color: #6B7280;'>Total Necessárias</div>\n";
    echo "</div>\n";
    
    echo "<div style='background: #D1FAE5; padding: 15px; border-radius: 8px; text-align: center;'>\n";
    echo "<div style='font-size: 2em; font-weight: bold; color: #065F46;'>$tabelas_existentes</div>\n";
    echo "<div style='color: #065F46;'>Existentes</div>\n";
    echo "</div>\n";
    
    echo "<div style='background: #FEE2E2; padding: 15px; border-radius: 8px; text-align: center;'>\n";
    echo "<div style='font-size: 2em; font-weight: bold; color: #991B1B;'>$tabelas_faltantes</div>\n";
    echo "<div style='color: #991B1B;'>Faltantes</div>\n";
    echo "</div>\n";
    
    echo "<div style='background: #FEF3C7; padding: 15px; border-radius: 8px; text-align: center;'>\n";
    echo "<div style='font-size: 2em; font-weight: bold; color: #92400E;'>" . count($tabelas_com_erro) . "</div>\n";
    echo "<div style='color: #92400E;'>Com Erro</div>\n";
    echo "</div>\n";
    
    echo "</div>\n";
    
    $percentual_completo = round(($tabelas_existentes / count($tabelas_necessarias)) * 100, 1);
    echo "<div style='text-align: center; margin: 20px 0;'>\n";
    echo "<div style='font-size: 1.5em; font-weight: bold; color: " . ($percentual_completo == 100 ? '#10B981' : '#F59E0B') . ";'>\n";
    echo "Completude do módulo: $percentual_completo%\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // Barra de progresso
    echo "<div style='background: #E5E7EB; border-radius: 10px; height: 20px; margin: 20px 0; overflow: hidden;'>\n";
    echo "<div style='background: linear-gradient(90deg, #10B981, #34D399); height: 100%; width: $percentual_completo%; transition: width 0.5s ease;'></div>\n";
    echo "</div>\n";
    
    if ($tabelas_faltantes > 0) {
        echo "<div style='background: #FEE2E2; border: 1px solid #EF4444; color: #991B1B; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
        echo "<h4>🚨 Ação Necessária:</h4>\n";
        echo "<p>Algumas tabelas estão faltando. Execute o instalador completo para criar todas as estruturas:</p>\n";
        echo "<div style='text-align: center; margin: 15px 0;'>\n";
        echo "<a href='instalador_completo.php' style='background: #EF4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold;'>🔧 Executar Instalador Completo</a>\n";
        echo "</div>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #D1FAE5; border: 1px solid #10B981; color: #065F46; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
        echo "<h4>🎉 Parabéns!</h4>\n";
        echo "<p>Todas as tabelas do módulo financeiro estão criadas e funcionando corretamente!</p>\n";
        echo "<p>O sistema está pronto para uso em produção.</p>\n";
        
        // Calcular total de registros
        $total_registros = 0;
        foreach ($detalhes_tabelas as $detalhes) {
            if ($detalhes['existe']) {
                $total_registros += $detalhes['registros'] ?? 0;
            }
        }
        
        echo "<p><strong>Total de registros no sistema:</strong> " . number_format($total_registros, 0, ',', '.') . "</p>\n";
        echo "</div>\n";
    }
    
    // Estatísticas detalhadas
    if ($tabelas_existentes > 0) {
        echo "<h3>📈 Estatísticas Detalhadas:</h3>\n";
        echo "<div style='background: #F9FAFB; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
        
        $total_registros_por_categoria = [
            'Básicas' => 0,
            'Patrimônio' => 0,
            'Custos' => 0,
            'Planejamento' => 0,
            'Boletos' => 0
        ];
        
        foreach ($detalhes_tabelas as $tabela => $detalhes) {
            if (!$detalhes['existe']) continue;
            
            $registros = $detalhes['registros'] ?? 0;
            
            if (strpos($tabela, 'financeiro_contas_') === 0 || strpos($tabela, 'financeiro_movimentacao_') === 0 || strpos($tabela, 'financeiro_impostos_') === 0 || strpos($tabela, 'financeiro_retencoes_') === 0) {
                $total_registros_por_categoria['Básicas'] += $registros;
            } elseif (strpos($tabela, 'financeiro_ativo_') === 0) {
                $total_registros_por_categoria['Patrimônio'] += $registros;
            } elseif (strpos($tabela, 'financeiro_centro_') === 0 || strpos($tabela, 'financeiro_alocacao_') === 0) {
                $total_registros_por_categoria['Custos'] += $registros;
            } elseif (strpos($tabela, 'financeiro_fluxo_') === 0 || strpos($tabela, 'financeiro_aplicacoes') === 0 || strpos($tabela, 'financeiro_cenarios') === 0 || strpos($tabela, 'financeiro_orcamento') === 0 || strpos($tabela, 'financeiro_metas') === 0) {
                $total_registros_por_categoria['Planejamento'] += $registros;
            } elseif (strpos($tabela, 'polos') === 0) {
                $total_registros_por_categoria['Boletos'] += $registros;
            }
        }
        
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;'>\n";
        foreach ($total_registros_por_categoria as $categoria => $total) {
            echo "<div style='text-align: center; padding: 10px; background: white; border-radius: 5px;'>\n";
            echo "<div style='font-weight: bold; color: #374151;'>$total</div>\n";
            echo "<div style='font-size: 0.8em; color: #6B7280;'>$categoria</div>\n";
            echo "</div>\n";
        }
        echo "</div>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Erro de conexão: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação Corrigida - Módulo Financeiro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h2 {
            color: #333;
            border-bottom: 3px solid #3B82F6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .actions {
            background: #F3F4F6;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            text-align: center;
        }
        .actions a {
            display: inline-block;
            background: #3B82F6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .actions a:hover {
            background: #2563EB;
            transform: translateY(-2px);
        }
        .actions a.danger {
            background: #EF4444;
        }
        .actions a.danger:hover {
            background: #DC2626;
        }
        .actions a.success {
            background: #10B981;
        }
        .actions a.success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="actions">
            <h3>🛠️ Ações Disponíveis</h3>
            <a href="instalador_completo.php" class="danger">🚀 Instalador Completo</a>
            <a href="verificar_tabelas_corrigido.php">🔄 Atualizar Verificação</a>
            <a href="../index.php" class="success">🏠 Voltar ao Financeiro</a>
            <a href="../boletos.php">📄 Testar Boletos</a>
            <a href="../ativo_nao_circulante.php">🏢 Testar Patrimônio</a>
        </div>
        
        <hr>
        <p style="text-align: center;"><small>Sistema Reinandus - Módulo Financeiro - Verificação de Integridade v2.0</small></p>
    </div>
</body>
</html>
