<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        Filtros
    </h3>
    
    <form id="form-filtros" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <input type="hidden" name="acao" value="listar">
        
        <!-- Status -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select name="status" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="">Todos</option>
                <option value="pendente" <?php echo ($_GET['status'] ?? '') === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                <option value="recebido" <?php echo ($_GET['status'] ?? '') === 'recebido' ? 'selected' : ''; ?>>Recebido</option>
                <option value="vencido" <?php echo ($_GET['status'] ?? '') === 'vencido' ? 'selected' : ''; ?>>Vencido</option>
            </select>
        </div>

        <!-- Categoria -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
            <select name="categoria" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="">Todas</option>
                <?php foreach ($categorias as $categoria): ?>
                    <option value="<?php echo $categoria['id']; ?>" <?php echo ($_GET['categoria'] ?? '') == $categoria['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($categoria['nome']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Data Início -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
            <input type="date" name="data_inicio" value="<?php echo htmlspecialchars($_GET['data_inicio'] ?? ''); ?>" 
                   class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
        </div>

        <!-- Data Fim -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
            <input type="date" name="data_fim" value="<?php echo htmlspecialchars($_GET['data_fim'] ?? ''); ?>" 
                   class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
        </div>

        <!-- Busca -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
            <div class="flex">
                <input type="text" name="busca" value="<?php echo htmlspecialchars($_GET['busca'] ?? ''); ?>" 
                       placeholder="Descrição ou cliente..." 
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-r-lg transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Resumo -->
<?php
$total_pendente = 0;
$total_recebido = 0;
$total_vencido = 0;
$count_pendente = 0;
$count_recebido = 0;
$count_vencido = 0;

foreach ($contas as $conta) {
    if ($conta['status'] === 'pendente') {
        $total_pendente += $conta['valor'];
        $count_pendente++;
        if ($conta['data_vencimento'] < date('Y-m-d')) {
            $total_vencido += $conta['valor'];
            $count_vencido++;
        }
    } elseif ($conta['status'] === 'recebido') {
        $total_recebido += $conta['valor_recebido'] ?? $conta['valor'];
        $count_recebido++;
    }
}
?>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Pendentes -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Contas Pendentes</p>
                <p class="text-2xl font-bold text-orange-600"><?php echo $count_pendente; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($total_pendente, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-clock text-orange-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Vencidas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Contas Vencidas</p>
                <p class="text-2xl font-bold text-red-600"><?php echo $count_vencido; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($total_vencido, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Recebidas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Contas Recebidas</p>
                <p class="text-2xl font-bold text-green-600"><?php echo $count_recebido; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($total_recebido, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Contas -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-green-500 mr-2"></i>
                Contas a Receber (<?php echo count($contas); ?>)
            </h3>
            <div class="flex items-center space-x-2">
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
                <a href="relatorios.php?tipo=contas_receber&formato=excel" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    Excel
                </a>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($contas)): ?>
            <div class="text-center py-12">
                <i class="fas fa-hand-holding-usd text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma conta encontrada</h3>
                <p class="text-gray-500 mb-6">Não há contas a receber que correspondam aos filtros aplicados.</p>
                <a href="contas_receber.php?acao=nova" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Cadastrar Primeira Conta
                </a>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categoria</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vencimento</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($contas as $conta): ?>
                        <?php
                        $status_class = '';
                        $status_text = '';
                        $is_vencida = $conta['data_vencimento'] < date('Y-m-d') && $conta['status'] === 'pendente';
                        
                        if ($conta['status'] === 'recebido') {
                            $status_class = 'bg-green-100 text-green-800';
                            $status_text = 'Recebido';
                        } elseif ($is_vencida) {
                            $status_class = 'bg-red-100 text-red-800';
                            $status_text = 'Vencida';
                        } else {
                            $status_class = 'bg-orange-100 text-orange-800';
                            $status_text = 'Pendente';
                        }
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($conta['descricao']); ?>
                                </div>
                                <?php if ($conta['observacoes']): ?>
                                    <div class="text-sm text-gray-500">
                                        <?php echo htmlspecialchars(substr($conta['observacoes'], 0, 50)); ?>
                                        <?php echo strlen($conta['observacoes']) > 50 ? '...' : ''; ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($conta['cliente_nome']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($conta['categoria_nome'] ?? 'N/A'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y', strtotime($conta['data_vencimento'])); ?>
                                <?php if ($is_vencida): ?>
                                    <br><span class="text-xs text-red-600">
                                        (<?php echo abs((strtotime($conta['data_vencimento']) - time()) / (60 * 60 * 24)); ?> dias)
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="contas_receber.php?acao=visualizar&id=<?php echo $conta['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-900" title="Visualizar">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    <?php if ($conta['status'] === 'pendente'): ?>
                                        <a href="contas_receber.php?acao=receber&id=<?php echo $conta['id']; ?>" 
                                           class="text-green-600 hover:text-green-900" title="Receber">
                                            <i class="fas fa-dollar-sign"></i>
                                        </a>
                                        <a href="contas_receber.php?acao=editar&id=<?php echo $conta['id']; ?>" 
                                           class="text-indigo-600 hover:text-indigo-900" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <button onclick="confirmarExclusao(<?php echo $conta['id']; ?>, '<?php echo addslashes($conta['descricao']); ?>')" 
                                            class="text-red-600 hover:text-red-900" title="Excluir">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>
