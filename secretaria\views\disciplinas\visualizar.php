<div class="bg-white rounded-xl shadow-sm overflow-hidden">
    <div class="p-6">
        <!-- Cabeçalho com informações principais -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h2 class="text-xl font-bold text-gray-800">
                    <?php echo htmlspecialchars($disciplina['nome']); ?>
                    <?php if (!empty($disciplina['codigo'])): ?>
                    <span class="text-sm text-gray-500 ml-2">(Código: <?php echo htmlspecialchars($disciplina['codigo']); ?>)</span>
                    <?php endif; ?>
                </h2>
                <p class="text-sm text-gray-600 mt-1">
                    <?php if (!empty($disciplina['id_legado'])): ?>
                    ID Legado: <?php echo htmlspecialchars($disciplina['id_legado']); ?> |
                    <?php endif; ?>
                    Criada em: <?php echo date('d/m/Y', strtotime($disciplina['created_at'])); ?>
                    <?php if ($disciplina['created_at'] != $disciplina['updated_at']): ?>
                    | Atualizada em: <?php echo date('d/m/Y', strtotime($disciplina['updated_at'])); ?>
                    <?php endif; ?>
                </p>
            </div>

            <div class="mt-4 md:mt-0">
                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full
                    <?php echo $disciplina['status'] === 'ativo' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                    <?php echo $disciplina['status'] === 'ativo' ? 'Ativo' : 'Inativo'; ?>
                </span>
            </div>
        </div>

        <!-- Informações do Curso -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Informações do Curso</h3>
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-medium text-gray-900">
                    <?php if (isset($disciplina['curso_nome'])): ?>
                    <a href="cursos.php?action=visualizar&id=<?php echo $disciplina['curso_id']; ?>" class="hover:text-blue-600">
                        <?php echo htmlspecialchars($disciplina['curso_nome']); ?>
                    </a>
                    <?php else: ?>
                    <span class="text-gray-500">Curso não encontrado</span>
                    <?php endif; ?>
                </h4>
                <?php if (!empty($disciplina['periodo'])): ?>
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <i class="fas fa-calendar-alt mr-1.5 text-gray-400"></i>
                    Período: <?php echo htmlspecialchars($disciplina['periodo']); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Informações do Professor -->
        <?php if (!empty($disciplina['professor_nome'])): ?>
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Professor Responsável</h3>
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-medium text-gray-900">
                    <a href="professores.php?action=visualizar&id=<?php echo $disciplina['professor_padrao_id']; ?>" class="hover:text-blue-600">
                        <?php echo htmlspecialchars($disciplina['professor_nome']); ?>
                    </a>
                </h4>
            </div>
        </div>
        <?php endif; ?>

        <!-- Carga Horária -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Carga Horária</h3>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-2xl font-bold text-gray-900">
                    <?php echo !empty($disciplina['carga_horaria']) ? $disciplina['carga_horaria'] : '0'; ?> horas
                </p>
            </div>
        </div>

        <!-- Turmas Vinculadas -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">
                <i class="fas fa-users mr-2"></i>Turmas Vinculadas
                <?php
                // Evita duplicidade de turmas vinculadas por id
                $turmas_unicas = [];
                if (isset($turmas_vinculadas) && !empty($turmas_vinculadas)) {
                    foreach ($turmas_vinculadas as $turma) {
                        if (!isset($turmas_unicas[$turma['id']])) {
                            $turmas_unicas[$turma['id']] = $turma;
                        }
                    }
                }
                ?>
                <?php if (isset($turmas_unicas)): ?>
                <span class="text-sm font-normal text-gray-500">(<?php echo count($turmas_unicas); ?>)</span>
                <?php endif; ?>
            </h3>
            <div class="bg-gray-50 rounded-lg p-4">
                <?php if (isset($turmas_unicas) && !empty($turmas_unicas)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($turmas_unicas as $turma): ?>
                    <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 mb-1">
                                    <a href="turmas.php?action=visualizar&id=<?php echo $turma['id']; ?>" class="hover:text-blue-600">
                                        <?php echo htmlspecialchars($turma['nome']); ?>
                                    </a>
                                </h4>
                                <p class="text-sm text-gray-600 mb-2">
                                    <?php echo htmlspecialchars($turma['curso_nome']); ?>
                                </p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                        <?php
                                        switch ($turma['status']) {
                                            case 'em_andamento':
                                                echo 'bg-green-100 text-green-800';
                                                break;
                                            case 'planejada':
                                                echo 'bg-yellow-100 text-yellow-800';
                                                break;
                                            case 'concluida':
                                                echo 'bg-blue-100 text-blue-800';
                                                break;
                                            case 'cancelada':
                                                echo 'bg-red-100 text-red-800';
                                                break;
                                            default:
                                                echo 'bg-gray-100 text-gray-800';
                                        }
                                        ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $turma['status'])); ?>
                                    </span>
                                    <?php if (!empty($turma['turno'])): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        <?php echo ucfirst($turma['turno']); ?>
                                    </span>
                                    <?php endif; ?>
                                </div>
                                <?php if (!empty($turma['polo_nome'])): ?>
                                <p class="text-xs text-gray-500 mt-1">
                                    <i class="fas fa-map-marker-alt mr-1"></i><?php echo htmlspecialchars($turma['polo_nome']); ?>
                                </p>
                                <?php endif; ?>
                                <?php if (!empty($turma['data_inicio']) || !empty($turma['data_fim'])): ?>
                                <p class="text-xs text-gray-500 mt-1">
                                    <i class="fas fa-calendar mr-1"></i>
                                    <?php if (!empty($turma['data_inicio'])): ?>
                                        <?php echo date('d/m/Y', strtotime($turma['data_inicio'])); ?>
                                    <?php endif; ?>
                                    <?php if (!empty($turma['data_inicio']) && !empty($turma['data_fim'])): ?>
                                        até
                                    <?php endif; ?>
                                    <?php if (!empty($turma['data_fim'])): ?>
                                        <?php echo date('d/m/Y', strtotime($turma['data_fim'])); ?>
                                    <?php endif; ?>
                                </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-users text-gray-400 text-3xl mb-3"></i>
                    <p class="text-gray-500">Nenhuma turma vinculada a esta disciplina.</p>
                    <p class="text-sm text-gray-400 mt-1">As turmas aparecerão aqui quando forem associadas à disciplina.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Ementa -->
        <?php if (!empty($disciplina['ementa'])): ?>
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Ementa</h3>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-gray-700 whitespace-pre-line"><?php echo nl2br(htmlspecialchars($disciplina['ementa'])); ?></p>
            </div>
        </div>
        <?php endif; ?>

        <!-- Objetivos -->
        <?php if (!empty($disciplina['objetivos'])): ?>
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Objetivos</h3>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-gray-700 whitespace-pre-line"><?php echo nl2br(htmlspecialchars($disciplina['objetivos'])); ?></p>
            </div>
        </div>
        <?php endif; ?>

        <!-- Bibliografia -->
        <?php if (!empty($disciplina['bibliografia'])): ?>
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Bibliografia</h3>
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-gray-700 whitespace-pre-line"><?php echo nl2br(htmlspecialchars($disciplina['bibliografia'])); ?></p>
            </div>
        </div>
        <?php endif; ?>        <!-- Botões de Ação -->
        <div class="mt-8 flex flex-col sm:flex-row sm:justify-end space-y-3 sm:space-y-0 sm:space-x-3">
            <a href="disciplinas.php?action=listar" class="btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i> Voltar para a Lista
            </a>
            <a href="disciplinas.php?action=editar&id=<?php echo $disciplina['id']; ?>" class="btn-primary">
                <i class="fas fa-edit mr-2"></i> Editar Disciplina
            </a>
        </div>
    </div>
</div>
