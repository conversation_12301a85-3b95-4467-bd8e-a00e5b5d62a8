<!-- Configura<PERSON><PERSON><PERSON> Gerais -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Informações do Sistema -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
            Informações do Sistema
        </h3>
        
        <div class="space-y-4">
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Versão do Módulo:</span>
                <span class="text-sm text-gray-900 font-semibold">2.0</span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Última Atualização:</span>
                <span class="text-sm text-gray-900">11/07/2025</span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Status:</span>
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Ativo
                </span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Banco de Dados:</span>
                <span class="text-sm text-gray-900">MySQL</span>
            </div>
        </div>
    </div>

    <!-- Estatísticas Rápidas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-chart-bar text-green-500 mr-2"></i>
            Estatísticas do Sistema
        </h3>
        
        <?php
        // Buscar estatísticas básicas
        $stats = [
            'categorias' => $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras")['total'] ?? 0,
            'formas_pagamento' => 6, // Número fixo de formas de pagamento padrão
            'transacoes_mes' => $db->fetchOne("SELECT COUNT(*) as total FROM contas_pagar WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())")['total'] ?? 0,
            'contas_pendentes' => $db->fetchOne("SELECT COUNT(*) as total FROM contas_pagar WHERE status = 'pendente'")['total'] ?? 0
        ];
        ?>
        
        <div class="space-y-4">
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Categorias Cadastradas:</span>
                <span class="text-sm text-gray-900 font-semibold"><?php echo $stats['categorias']; ?></span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Formas de Pagamento:</span>
                <span class="text-sm text-gray-900 font-semibold"><?php echo $stats['formas_pagamento']; ?></span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Transações este Mês:</span>
                <span class="text-sm text-gray-900 font-semibold"><?php echo $stats['transacoes_mes']; ?></span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm font-medium text-gray-600">Contas Pendentes:</span>
                <span class="text-sm text-gray-900 font-semibold"><?php echo $stats['contas_pendentes']; ?></span>
            </div>
        </div>
    </div>
</div>

<!-- Configurações de Preferências -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-sliders-h text-purple-500 mr-2"></i>
        Preferências do Sistema
    </h3>
    
    <form method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <input type="hidden" name="form_tipo" value="preferencias">
        
        <!-- Moeda Padrão -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Moeda Padrão</label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="BRL" selected>Real Brasileiro (R$)</option>
                <option value="USD">Dólar Americano (US$)</option>
                <option value="EUR">Euro (€)</option>
            </select>
        </div>

        <!-- Formato de Data -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Formato de Data</label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="dd/mm/yyyy" selected>DD/MM/AAAA</option>
                <option value="mm/dd/yyyy">MM/DD/AAAA</option>
                <option value="yyyy-mm-dd">AAAA-MM-DD</option>
            </select>
        </div>

        <!-- Dia de Vencimento Padrão -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Dia de Vencimento Padrão (Mensalidades)</label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <?php for ($i = 1; $i <= 31; $i++): ?>
                    <option value="<?php echo $i; ?>" <?php echo $i === 10 ? 'selected' : ''; ?>>
                        Dia <?php echo $i; ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>

        <!-- Período de Relatórios Padrão -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Período Padrão para Relatórios</label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="mes_atual" selected>Mês Atual</option>
                <option value="ultimos_30_dias">Últimos 30 Dias</option>
                <option value="trimestre_atual">Trimestre Atual</option>
                <option value="ano_atual">Ano Atual</option>
            </select>
        </div>

        <!-- Botão Salvar -->
        <div class="md:col-span-2 flex justify-end pt-4 border-t border-gray-200">
            <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-save mr-2"></i>
                Salvar Preferências
            </button>
        </div>
    </form>
</div>

<!-- Ações do Sistema -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-tools text-orange-500 mr-2"></i>
        Ações do Sistema
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Backup -->
        <div class="border border-gray-200 rounded-lg p-4 text-center">
            <i class="fas fa-download text-blue-600 text-3xl mb-3"></i>
            <h4 class="text-md font-semibold text-gray-800 mb-2">Backup de Dados</h4>
            <p class="text-sm text-gray-600 mb-4">Gerar backup dos dados financeiros</p>
            <button onclick="alert('Funcionalidade em desenvolvimento')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                <i class="fas fa-download mr-2"></i>
                Gerar Backup
            </button>
        </div>

        <!-- Limpeza -->
        <div class="border border-gray-200 rounded-lg p-4 text-center">
            <i class="fas fa-broom text-yellow-600 text-3xl mb-3"></i>
            <h4 class="text-md font-semibold text-gray-800 mb-2">Limpeza de Dados</h4>
            <p class="text-sm text-gray-600 mb-4">Limpar dados antigos e temporários</p>
            <button onclick="alert('Funcionalidade em desenvolvimento')" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                <i class="fas fa-broom mr-2"></i>
                Executar Limpeza
            </button>
        </div>

        <!-- Auditoria -->
        <div class="border border-gray-200 rounded-lg p-4 text-center">
            <i class="fas fa-search text-green-600 text-3xl mb-3"></i>
            <h4 class="text-md font-semibold text-gray-800 mb-2">Auditoria</h4>
            <p class="text-sm text-gray-600 mb-4">Verificar integridade dos dados</p>
            <button onclick="alert('Funcionalidade em desenvolvimento')" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                <i class="fas fa-search mr-2"></i>
                Executar Auditoria
            </button>
        </div>
    </div>
</div>

<!-- Informações de Suporte -->
<div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-question-circle text-gray-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-gray-800">Suporte e Documentação</h3>
            <div class="mt-2 text-sm text-gray-600">
                <p class="mb-2">Para dúvidas sobre o módulo financeiro:</p>
                <ul class="list-disc list-inside space-y-1">
                    <li>Consulte a documentação completa no arquivo README.md</li>
                    <li>Verifique os logs do sistema em caso de erros</li>
                    <li>Entre em contato com o suporte técnico se necessário</li>
                    <li>Mantenha sempre backups atualizados dos dados</li>
                </ul>
            </div>
        </div>
    </div>
</div>
