<?php
/**
 * VIEW: Listagem de Contas a Pagar
 * Arquivo: views/contas_pagar/listar.php
 */
?>

<!-- Formulário de Filtros Avançados -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
            <i class="fas fa-filter text-gray-600 mr-2"></i>
            Filtros de Busca
        </h3>
    </div>
    <div class="p-6">
        <form id="form-filtros" method="GET" action="contas_pagar.php" class="space-y-4">
            <input type="hidden" name="acao" value="listar">
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Busca por Texto -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search mr-1"></i>
                        Buscar
                    </label>
                    <input type="text" name="busca" 
                           value="<?php echo htmlspecialchars($_GET['busca'] ?? ''); ?>"
                           placeholder="Descrição ou fornecedor..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                </div>

                <!-- Status -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-flag mr-1"></i>
                        Status
                    </label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 filtro-auto">
                        <option value="">Todos os status</option>
                        <option value="pendente" <?php echo ($_GET['status'] ?? '') === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                        <option value="pago" <?php echo ($_GET['status'] ?? '') === 'pago' ? 'selected' : ''; ?>>Pago</option>
                    </select>
                </div>

                <!-- Categoria -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tag mr-1"></i>
                        Categoria
                    </label>
                    <select name="categoria" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 filtro-auto">
                        <option value="">Todas as categorias</option>
                        <?php foreach ($categorias as $categoria): ?>
                            <option value="<?php echo $categoria['id']; ?>" 
                                    <?php echo ($_GET['categoria'] ?? '') == $categoria['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($categoria['nome']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Data Início -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar mr-1"></i>
                        Data Início
                    </label>
                    <input type="date" name="data_inicio" 
                           value="<?php echo htmlspecialchars($_GET['data_inicio'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 filtro-auto">
                </div>

                <!-- Data Fim -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar mr-1"></i>
                        Data Fim
                    </label>
                    <input type="date" name="data_fim" 
                           value="<?php echo htmlspecialchars($_GET['data_fim'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 filtro-auto">
                </div>

                <!-- Valor Mínimo -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign mr-1"></i>
                        Valor Mínimo
                    </label>
                    <input type="text" name="valor_min" 
                           value="<?php echo htmlspecialchars($_GET['valor_min'] ?? ''); ?>"
                           placeholder="0,00"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 input-moeda">
                </div>

                <!-- Valor Máximo -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign mr-1"></i>
                        Valor Máximo
                    </label>
                    <input type="text" name="valor_max" 
                           value="<?php echo htmlspecialchars($_GET['valor_max'] ?? ''); ?>"
                           placeholder="0,00"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 input-moeda">
                </div>

                <!-- Apenas Vencidas -->
                <div class="flex items-center">
                    <label class="flex items-center">
                        <input type="checkbox" name="vencidas" value="1" 
                               <?php echo isset($_GET['vencidas']) && $_GET['vencidas'] === '1' ? 'checked' : ''; ?>
                               class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 filtro-auto">
                        <span class="ml-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-1"></i>
                            Apenas vencidas
                        </span>
                    </label>
                </div>
            </div>

            <!-- Botões de Ação -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                <div class="flex space-x-3">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Filtrar
                    </button>
                    <button type="button" onclick="limparFiltros()" 
                            class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-times mr-2"></i>
                        Limpar
                    </button>
                </div>
                <div class="flex space-x-2">
                    <button type="button" onclick="exportarCSV()" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>
                        Exportar
                    </button>
                    <button type="button" onclick="gerarRelatorio('filtrado')" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-pdf mr-2"></i>
                        Relatório
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Paginação -->
<?php if ($total_paginas > 1): ?>
<div class="flex justify-center mt-8 mb-8">
    <nav class="inline-flex rounded-md shadow-sm" aria-label="Paginação">
        <?php
        $query_params = $_GET;
        $current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $current_page = max(1, $current_page);
        $per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
        $range = 2;
        $start = max(1, $current_page - $range);
        $end = min($total_paginas, $current_page + $range);
        
        function buildPageUrl($page, $per_page) {
            $params = $_GET;
            $params['page'] = $page;
            $params['per_page'] = $per_page;
            return '?' . http_build_query($params);
        }
        ?>
        <!-- Primeira página -->
        <a href="<?php echo buildPageUrl(1, $per_page); ?>" class="px-3 py-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 rounded-l-md <?php echo $current_page == 1 ? 'opacity-50 pointer-events-none' : ''; ?>" title="Primeira página">
            <i class="fas fa-angle-double-left"></i>
        </a>
        <!-- Página anterior -->
        <a href="<?php echo buildPageUrl(max(1, $current_page-1), $per_page); ?>" class="px-3 py-2 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50 <?php echo $current_page == 1 ? 'opacity-50 pointer-events-none' : ''; ?>" title="Página anterior">
            <i class="fas fa-angle-left"></i>
        </a>
        <!-- Números das páginas -->
        <?php for ($i = $start; $i <= $end; $i++): ?>
            <a href="<?php echo buildPageUrl($i, $per_page); ?>" class="px-3 py-2 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50 <?php echo $i == $current_page ? 'bg-red-600 text-white font-bold' : ''; ?>">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
        <!-- Próxima página -->
        <a href="<?php echo buildPageUrl(min($total_paginas, $current_page+1), $per_page); ?>" class="px-3 py-2 border-t border-b border-gray-300 bg-white text-gray-700 hover:bg-gray-50 <?php echo $current_page == $total_paginas ? 'opacity-50 pointer-events-none' : ''; ?>" title="Próxima página">
            <i class="fas fa-angle-right"></i>
        </a>
        <!-- Última página -->
        <a href="<?php echo buildPageUrl($total_paginas, $per_page); ?>" class="px-3 py-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 rounded-r-md <?php echo $current_page == $total_paginas ? 'opacity-50 pointer-events-none' : ''; ?>" title="Última página">
            <i class="fas fa-angle-double-right"></i>
        </a>
    </nav>
</div>
<?php endif; ?>

<!-- Resumo dos Filtros -->
<?php if (!empty($resumo_filtros)): ?>
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600"><?php echo number_format($resumo_filtros['total_contas']); ?></div>
                <div class="text-sm text-blue-600">Total</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600"><?php echo number_format($resumo_filtros['pendentes']); ?></div>
                <div class="text-sm text-yellow-600">Pendentes</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600"><?php echo number_format($resumo_filtros['pagas']); ?></div>
                <div class="text-sm text-green-600">Pagas</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">R$ <?php echo number_format($resumo_filtros['valor_total'], 2, ',', '.'); ?></div>
                <div class="text-sm text-gray-600">Valor Total</div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Ações em Lote -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold text-gray-900">
                Contas a Pagar 
                <?php if ($total_registros > 0): ?>
                    <span class="text-sm font-normal text-gray-500">
                        (<?php echo number_format($total_registros); ?> encontrada<?php echo $total_registros > 1 ? 's' : ''; ?>)
                    </span>
                <?php endif; ?>
            </h3>
            
            <!-- Botão de Pagamento em Lote -->
            <button id="btnPagamentoLote" onclick="abrirModalPagamentoLote()" 
                    class="hidden bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-credit-card mr-2"></i>
                Pagar Lote (<span id="contadorSelecionadas">0</span>)
            </button>
        </div>
        
        <!-- Controles de Visualização -->
        <div class="flex items-center space-x-4">
            <!-- Itens por página -->
            <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-600">Exibir:</label>
                <select name="per_page" onchange="this.form.submit()" form="form-filtros"
                        class="px-3 py-1 border border-gray-300 rounded text-sm">
                    <option value="20" <?php echo ($per_page == 20) ? 'selected' : ''; ?>>20</option>
                    <option value="50" <?php echo ($per_page == 50) ? 'selected' : ''; ?>>50</option>
                    <option value="100" <?php echo ($per_page == 100) ? 'selected' : ''; ?>>100</option>
                </select>
            </div>
            
            <!-- Botão de Imprimir -->
            <button onclick="imprimirLista()" 
                    class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded transition-colors">
                <i class="fas fa-print"></i>
            </button>
        </div>
    </div>
</div>

<!-- Tabela de Contas -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <?php if (!empty($contas)): ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" onchange="selecionarTodas(this)" 
                                   class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onclick="ordenarTabela('descricao')">
                            <div class="flex items-center">
                                Descrição
                                <i class="fas fa-sort ml-1"></i>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onclick="ordenarTabela('fornecedor_nome')">
                            <div class="flex items-center">
                                Fornecedor
                                <i class="fas fa-sort ml-1"></i>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onclick="ordenarTabela('categoria_nome')">
                            <div class="flex items-center">
                                Categoria
                                <i class="fas fa-sort ml-1"></i>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onclick="ordenarTabela('valor')">
                            <div class="flex items-center">
                                Valor
                                <i class="fas fa-sort ml-1"></i>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onclick="ordenarTabela('data_vencimento')">
                            <div class="flex items-center">
                                Vencimento
                                <i class="fas fa-sort ml-1"></i>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onclick="ordenarTabela('status')">
                            <div class="flex items-center">
                                Status
                                <i class="fas fa-sort ml-1"></i>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Ações
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($contas as $conta): ?>
                        <?php
                        $status_class = '';
                        $status_icon = '';
                        $status_text = '';
                        
                        switch($conta['status_visual']) {
                            case 'vencida':
                                $status_class = 'status-vencida';
                                $status_icon = 'fas fa-exclamation-triangle';
                                $status_text = 'Vencida';
                                break;
                            case 'vence_hoje':
                                $status_class = 'status-vence-hoje';
                                $status_icon = 'fas fa-clock';
                                $status_text = 'Vence Hoje';
                                break;
                            case 'vence_semana':
                                $status_class = 'status-vence-semana';
                                $status_icon = 'fas fa-calendar-week';
                                $status_text = 'Vence em Breve';
                                break;
                            case 'pago':
                                $status_class = 'status-pago';
                                $status_icon = 'fas fa-check-circle';
                                $status_text = 'Pago';
                                break;
                            default:
                                $status_class = 'status-pendente';
                                $status_icon = 'fas fa-clock';
                                $status_text = 'Pendente';
                        }
                        ?>
                        <tr class="table-row hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if ($conta['status'] === 'pendente'): ?>
                                    <input type="checkbox" name="contas_selecionadas[]" value="<?php echo $conta['id']; ?>"
                                           onchange="atualizarSelecao(this)"
                                           class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500">
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($conta['descricao']); ?>
                                </div>
                                <?php if ($conta['observacoes']): ?>
                                    <div class="text-sm text-gray-500 mt-1">
                                        <i class="fas fa-sticky-note mr-1"></i>
                                        <?php echo htmlspecialchars(substr($conta['observacoes'], 0, 50)) . (strlen($conta['observacoes']) > 50 ? '...' : ''); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?php echo htmlspecialchars($conta['fornecedor_nome'] ?: 'Não informado'); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if ($conta['categoria_nome']): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <?php echo htmlspecialchars($conta['categoria_nome']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-sm text-gray-400">Sem categoria</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-semibold text-gray-900">
                                    R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                                </div>
                                <?php if ($conta['status'] === 'pago' && $conta['valor_pago'] != $conta['valor']): ?>
                                    <div class="text-xs text-green-600">
                                        Pago: R$ <?php echo number_format($conta['valor_pago'], 2, ',', '.'); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?php echo date('d/m/Y', strtotime($conta['data_vencimento'])); ?>
                                </div>
                                <?php if ($conta['status'] === 'pago' && $conta['data_pagamento']): ?>
                                    <div class="text-xs text-green-600">
                                        Pago em: <?php echo date('d/m/Y', strtotime($conta['data_pagamento'])); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge <?php echo $status_class; ?>">
                                    <i class="<?php echo $status_icon; ?> mr-1"></i>
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <div class="flex items-center justify-center space-x-2">
                                    <!-- Visualizar -->
                                    <a href="contas_pagar.php?acao=visualizar&id=<?php echo $conta['id']; ?>" 
                                       class="text-gray-600 hover:text-gray-900 btn-action" 
                                       title="Visualizar">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    <?php if ($conta['status'] === 'pendente'): ?>
                                        <!-- Pagar -->
                                        <a href="contas_pagar.php?acao=pagar&id=<?php echo $conta['id']; ?>" 
                                           class="text-green-600 hover:text-green-900 btn-action" 
                                           title="Registrar Pagamento">
                                            <i class="fas fa-credit-card"></i>
                                        </a>
                                        
                                        <!-- Editar -->
                                        <a href="contas_pagar.php?acao=editar&id=<?php echo $conta['id']; ?>" 
                                           class="text-blue-600 hover:text-blue-900 btn-action" 
                                           title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <!-- Menu de Ações -->
                                    <div class="relative">
                                        <button onclick="toggleMenu('menu-<?php echo $conta['id']; ?>')" 
                                                class="text-gray-600 hover:text-gray-900 btn-action" 
                                                title="Mais ações">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div id="menu-<?php echo $conta['id']; ?>" 
                                             class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                            <div class="py-1">
                                                <button onclick="imprimirConta(<?php echo $conta['id']; ?>)" 
                                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-print mr-2"></i>
                                                    Imprimir
                                                </button>
                                                <?php if ($conta['status'] === 'pendente'): ?>
                                                    <button onclick="duplicarConta(<?php echo $conta['id']; ?>)" 
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-copy mr-2"></i>
                                                        Duplicar
                                                    </button>
                                                    <button onclick="agendarLembrete(<?php echo $conta['id']; ?>)" 
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        <i class="fas fa-bell mr-2"></i>
                                                        Lembrete
                                                    </button>
                                                    <div class="border-t border-gray-100"></div>
                                                    <button onclick="confirmarExclusao(<?php echo $conta['id']; ?>, '<?php echo addslashes($conta['descricao']); ?>')" 
                                                            class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                        <i class="fas fa-trash mr-2"></i>
                                                        Excluir
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Paginação -->
        <?php if ($total_paginas > 1): ?>
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Anterior
                            </a>
                        <?php endif; ?>
                        <?php if ($page < $total_paginas): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Próxima
                            </a>
                        <?php endif; ?>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Mostrando
                                <span class="font-medium"><?php echo ($page - 1) * $per_page + 1; ?></span>
                                até
                                <span class="font-medium"><?php echo min($page * $per_page, $total_registros); ?></span>
                                de
                                <span class="font-medium"><?php echo number_format($total_registros); ?></span>
                                resultados
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                <?php endif; ?>
                                
                                <?php
                                $start = max(1, $page - 2);
                                $end = min($total_paginas, $page + 2);
                                
                                if ($start > 1): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>" 
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        1
                                    </a>
                                    <?php if ($start > 2): ?>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                            ...
                                        </span>
                                    <?php endif; ?>
                                <?php endif; ?>
                                
                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                    <?php if ($i == $page): ?>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-red-500 bg-red-50 text-sm font-medium text-red-600">
                                            <?php echo $i; ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endfor; ?>
                                
                                <?php if ($end < $total_paginas): ?>
                                    <?php if ($end < $total_paginas - 1): ?>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                            ...
                                        </span>
                                    <?php endif; ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_paginas])); ?>" 
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        <?php echo $total_paginas; ?>
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($page < $total_paginas): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php endif; ?>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php else: ?>
        <!-- Estado Vazio -->
        <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 text-gray-400">
                <i class="fas fa-file-invoice text-6xl"></i>
            </div>
            <h3 class="mt-4 text-lg font-medium text-gray-900">Nenhuma conta encontrada</h3>
            <p class="mt-2 text-gray-500">
                <?php if (!empty($_GET) && array_filter($_GET)): ?>
                    Não foram encontradas contas com os filtros aplicados.
                <?php else: ?>
                    Comece cadastrando sua primeira conta a pagar.
                <?php endif; ?>
            </p>
            <div class="mt-6 space-x-3">
                <?php if (!empty($_GET) && array_filter($_GET)): ?>
                    <button onclick="limparFiltros()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-times mr-2"></i>
                        Limpar Filtros
                    </button>
                <?php endif; ?>
                <a href="contas_pagar.php?acao=nova" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                    <i class="fas fa-plus mr-2"></i>
                    Nova Conta a Pagar
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Toggle menu de ações
function toggleMenu(menuId) {
    // Fechar todos os menus
    document.querySelectorAll('[id^="menu-"]').forEach(menu => {
        if (menu.id !== menuId) {
            menu.classList.add('hidden');
        }
    });
    
    // Toggle do menu clicado
    const menu = document.getElementById(menuId);
    menu.classList.toggle('hidden');
}

// Fechar menus ao clicar fora
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick*="toggleMenu"]') && !event.target.closest('[id^="menu-"]')) {
        document.querySelectorAll('[id^="menu-"]').forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});
</script>