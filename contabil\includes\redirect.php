<?php
/**
 * Sistema de Redirecionamento Limpo - Módulo Financeiro
 * Resolve problemas de navegação e URLs
 */

// Função para redirecionamento seguro
function redirect($url, $permanent = false) {
    // Limpar qualquer output anterior
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Definir código de status
    $status_code = $permanent ? 301 : 302;
    
    // Verificar se é URL relativa ou absoluta
    if (!preg_match('/^https?:\/\//', $url)) {
        // URL relativa - construir URL completa
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $base_path = dirname($_SERVER['SCRIPT_NAME']);
        
        // Remover /financeiro do base_path se existir
        $base_path = str_replace('/financeiro', '', $base_path);
        
        // Construir URL completa
        if (strpos($url, '/') === 0) {
            // URL absoluta do site
            $url = $protocol . '://' . $host . $url;
        } else {
            // URL relativa
            $url = $protocol . '://' . $host . $base_path . '/financeiro/' . $url;
        }
    }
    
    // Enviar headers
    header("Location: $url", true, $status_code);
    exit();
}

// Função para URL base do módulo financeiro
function base_url($path = '') {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $base_path = dirname($_SERVER['SCRIPT_NAME']);
    
    // Remover /financeiro do base_path se existir
    $base_path = str_replace('/financeiro', '', $base_path);
    
    $base_url = $protocol . '://' . $host . $base_path . '/financeiro/';
    
    return $base_url . ltrim($path, '/');
}

// Função para asset URL (CSS, JS, imagens)
function asset_url($path) {
    return base_url($path);
}

// Função para verificar se usuário está logado
function check_auth() {
    session_start();
    
    // Verificar se existe sessão ativa
    if (!isset($_SESSION['user_id']) && !isset($_SESSION['usuario_id'])) {
        // Redirecionar para login
        redirect('../login.php');
    }
    
    return true;
}

// Função para logout
function logout() {
    session_start();
    session_destroy();
    redirect('../login.php');
}

// Função para mensagens flash
function set_flash($type, $message) {
    session_start();
    $_SESSION['flash'][$type] = $message;
}

function get_flash($type = null) {
    session_start();
    
    if ($type) {
        $message = $_SESSION['flash'][$type] ?? null;
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    
    $messages = $_SESSION['flash'] ?? [];
    unset($_SESSION['flash']);
    return $messages;
}

// Função para breadcrumb
function breadcrumb($items) {
    $html = '<nav class="flex mb-4" aria-label="Breadcrumb">';
    $html .= '<ol class="inline-flex items-center space-x-1 md:space-x-3">';
    
    foreach ($items as $index => $item) {
        $is_last = ($index === count($items) - 1);
        
        $html .= '<li class="inline-flex items-center">';
        
        if ($index > 0) {
            $html .= '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">';
            $html .= '<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>';
            $html .= '</svg>';
        }
        
        if ($is_last) {
            $html .= '<span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">' . htmlspecialchars($item['title']) . '</span>';
        } else {
            $url = isset($item['url']) ? $item['url'] : '#';
            $html .= '<a href="' . htmlspecialchars($url) . '" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">';
            
            if (isset($item['icon'])) {
                $html .= '<i class="' . htmlspecialchars($item['icon']) . ' mr-2"></i>';
            }
            
            $html .= htmlspecialchars($item['title']) . '</a>';
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ol>';
    $html .= '</nav>';
    
    return $html;
}

// Função para menu ativo
function is_active($page) {
    $current_page = basename($_SERVER['PHP_SELF']);
    return $current_page === $page ? 'active' : '';
}

// Função para formatar moeda
function format_currency($value) {
    return 'R$ ' . number_format($value, 2, ',', '.');
}

// Função para formatar data
function format_date($date, $format = 'd/m/Y') {
    if (empty($date) || $date === '0000-00-00') {
        return '-';
    }
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date($format, $timestamp);
}

// Função para debug (apenas em desenvolvimento)
function debug($data, $die = false) {
    if (defined('DEBUG') && DEBUG === true) {
        echo '<pre style="background: #f4f4f4; padding: 10px; border: 1px solid #ddd; margin: 10px 0;">';
        print_r($data);
        echo '</pre>';
        
        if ($die) {
            die();
        }
    }
}

// Constantes úteis
define('FINANCEIRO_PATH', __DIR__ . '/../');
define('INCLUDES_PATH', __DIR__ . '/');
define('ASSETS_PATH', FINANCEIRO_PATH . 'assets/');
define('LOGS_PATH', FINANCEIRO_PATH . 'logs/');

// Criar diretórios necessários se não existirem
if (!is_dir(LOGS_PATH)) {
    mkdir(LOGS_PATH, 0755, true);
}

if (!is_dir(ASSETS_PATH)) {
    mkdir(ASSETS_PATH, 0755, true);
}
?>
