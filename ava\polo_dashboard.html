<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel do Polo - Faciencia EAD</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --very-light-purple: #E6E6FA;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
            --text-muted: #6c757d;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
            --border-radius: 10px;
            --card-shadow: 0 6px 15px rgba(106, 90, 205, 0.1);
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        /* Sidebar Moderna */
        .sidebar {
            background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            position: relative;
            z-index: 10;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar-logo i {
            font-size: 2rem;
            margin-right: 10px;
        }

        .sidebar-logo h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section-header {
            font-size: 0.85rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.6);
            margin-left: 10px;
            margin-bottom: 15px;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding-left: 0;
            margin-bottom: 30px;
        }

        .sidebar-menu li {
            margin-bottom: 8px;
        }

        .sidebar-menu a {
            color: var(--white);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed) ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-menu a.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background-color: var(--white);
            border-radius: 0 2px 2px 0;
        }

        .sidebar-menu a i {
            margin-right: 15px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            transition: all var(--transition-speed) ease;
        }

        .sidebar-menu a:hover i {
            transform: scale(1.2);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
        }

        /* Conteúdo Principal */
        .main-content {
            background-color: var(--light-bg);
            padding: 30px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--very-light-purple);
        }

        .dashboard-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--secondary-purple);
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .polo-info {
            text-align: right;
        }

        .polo-info .polo-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--secondary-purple);
        }

        .polo-info .polo-role {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .user-avatar {
            position: relative;
            cursor: pointer;
        }

        .user-avatar img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--white);
        }

        /* Welcome Banner */
        .welcome-banner {
            background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .welcome-banner::before {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .welcome-banner::after {
            content: '';
            position: absolute;
            bottom: -60px;
            left: -60px;
            width: 180px;
            height: 180px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
        }

        .welcome-content {
            position: relative;
            z-index: 1;
        }

        .welcome-banner h2 {
            margin-bottom: 15px;
            font-weight: 700;
        }

        .welcome-banner p {
            margin-bottom: 20px;
            opacity: 0.9;
            max-width: 70%;
        }

        /* Plan and Limits Section */
        .plan-details {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 25px;
        }

        .plan-name {
            display: inline-block;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .limit-item {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            padding: 15px;
            flex: 1;
            min-width: 200px;
        }

        .limit-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .limit-title i {
            font-size: 1.1rem;
        }

        .limit-value {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .limit-progress {
            height: 6px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            margin-bottom: 5px;
            overflow: hidden;
        }

        .limit-progress-bar {
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 3px;
        }

        .limit-text {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        /* Cards e Elementos de UI */
        .dashboard-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 25px;
            margin-bottom: 30px;
            transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(106, 90, 205, 0.15);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-purple), var(--light-purple));
        }

        .card-header-custom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .card-header-custom h4 {
            font-weight: 600;
            color: var(--secondary-purple);
            margin: 0;
            font-size: 1.25rem;
        }

        .card-link {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--primary-purple);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all var(--transition-speed) ease;
        }

        .card-link:hover {
            color: var(--secondary-purple);
            transform: translateX(3px);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
            gap: 25px;
        }

        .stat-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(106, 90, 205, 0.15);
        }

        .stat-card.total-cursos {
            background: linear-gradient(135deg, rgba(106, 90, 205, 0.08), rgba(106, 90, 205, 0.01));
        }

        .stat-card.total-alunos {
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.08), rgba(23, 162, 184, 0.01));
        }

        .stat-card.novos-alunos {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.08), rgba(40, 167, 69, 0.01));
        }

        .stat-card.certificados {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.08), rgba(255, 193, 7, 0.01));
        }

        .stat-icon {
            width: 55px;
            height: 55px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-card.total-cursos .stat-icon {
            background: linear-gradient(135deg, var(--primary-purple), var(--light-purple));
        }

        .stat-card.total-alunos .stat-icon {
            background: linear-gradient(135deg, #17a2b8, #4dc0d1);
        }

        .stat-card.novos-alunos .stat-icon {
            background: linear-gradient(135deg, #28a745, #5dd879);
        }

        .stat-card.certificados .stat-icon {
            background: linear-gradient(135deg, #ffc107, #ffe066);
        }

        .stat-content {
            flex: 1;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
            line-height: 1.2;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-muted);
            margin-bottom: 0;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 0.85rem;
            margin-top: 5px;
        }

        .trend-up {
            color: var(--success-green);
        }

        .trend-down {
            color: var(--danger-red);
        }

        /* Course Cards */
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }

        .course-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed) ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(106, 90, 205, 0.15);
        }

        .course-image {
            height: 140px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .course-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
            display: flex;
            align-items: flex-end;
            padding: 15px;
        }

        .course-category {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: rgba(255, 255, 255, 0.9);
            color: var(--primary-purple);
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .course-title {
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        .course-content {
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .course-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .course-stat {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .course-stat i {
            color: var(--primary-purple);
            font-size: 1rem;
        }

        .course-description {
            font-size: 0.9rem;
            color: var(--text-muted);
            margin-bottom: 15px;
            flex: 1;
        }

        .course-actions {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        /* Tabelas Estilizadas */
        .custom-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom-table thead th {
            background-color: var(--very-light-purple);
            color: var(--secondary-purple);
            font-weight: 600;
            padding: 15px;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
        }

        .custom-table thead th:first-child {
            border-top-left-radius: 10px;
        }

        .custom-table thead th:last-child {
            border-top-right-radius: 10px;
        }

        .custom-table tbody tr {
            transition: all var(--transition-speed) ease;
        }

        .custom-table tbody tr:hover {
            background-color: rgba(106, 90, 205, 0.05);
            transform: scale(1.01);
        }

        .custom-table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-top: 1px solid #eee;
            font-size: 0.95rem;
        }

        .custom-table tbody tr:first-child td {
            border-top: none;
        }

        /* Status Badges */
        .badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .badge.bg-success {
            background-color: rgba(40, 167, 69, 0.15) !important;
            color: var(--success-green);
        }

        .badge.bg-warning {
            background-color: rgba(255, 193, 7, 0.15) !important;
            color: #d18700;
        }

        .badge.bg-danger {
            background-color: rgba(220, 53, 69, 0.15) !important;
            color: var(--danger-red);
        }

        .badge.bg-info {
            background-color: rgba(23, 162, 184, 0.15) !important;
            color: var(--info-blue);
        }

        .badge.bg-secondary {
            background-color: rgba(108, 117, 125, 0.15) !important;
            color: var(--text-muted);
        }

        /* Activity Feed */
        .activity-feed {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .activity-item {
            display: flex;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid var(--very-light-purple);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--very-light-purple);
            color: var(--primary-purple);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        /* Quick Access */
        .quick-access {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .quick-link {
            background-color: var(--white);
            border-radius: var(--border-radius);
            padding: 20px 15px;
            text-align: center;
            text-decoration: none;
            color: var(--text-dark);
            transition: all var(--transition-speed) ease;
            box-shadow: var(--card-shadow);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .quick-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(106, 90, 205, 0.15);
            color: var(--primary-purple);
        }

        .quick-link-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--very-light-purple);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: var(--primary-purple);
            transition: all var(--transition-speed) ease;
        }

        .quick-link:hover .quick-link-icon {
            background-color: var(--primary-purple);
            color: var(--white);
            transform: scale(1.1);
        }

        .quick-link-text {
            font-weight: 500;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Performance Card */
        .performance-stat {
            margin-bottom: 20px;
        }

        .performance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .performance-title {
            font-weight: 500;
            color: var(--text-dark);
        }

        .performance-value {
            font-weight: 700;
            color: var(--primary-purple);
        }

        .performance-progress {
            height: 8px;
            background-color: var(--very-light-purple);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .performance-bar {
            height: 100%;
            border-radius: 4px;
        }

        .performance-bar.bg-success {
            background-color: var(--success-green);
        }

        .performance-bar.bg-info {
            background-color: var(--info-blue);
        }

        .performance-bar.bg-warning {
            background-color: var(--warning-yellow);
        }

        .performance-text {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        /* Mobile Responsiveness */
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                height: 100vh;
                width: 280px;
                z-index: 1000;
                transition: left var(--transition-speed) ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 20px 15px;
            }

            .welcome-banner p {
                max-width: 100%;
            }
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .user-info {
                align-self: flex-end;
            }

            .courses-grid {
                grid-template-columns: 1fr;
            }

            .quick-access {
                grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .plan-details {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-grid">
        <!-- Sidebar Aprimorada -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-graduation-cap"></i>
                <h2>Faciencia</h2>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Principal</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_dashboard.html" class="active">
                            <i class="fas fa-tachometer-alt"></i> Painel Geral
                        </a>
                    </li>
                    <li>
                        <a href="polo_cursos.html">
                            <i class="fas fa-book-open"></i> Cursos
                        </a>
                    </li>
                    <li>
                        <a href="polo_aluno.html">
                            <i class="fas fa-users"></i> Alunos
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Gestão</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_matriculas.html">
                            <i class="fas fa-user-plus"></i> Matrículas
                        </a>
                    </li>
                    <li>
                        <a href="polo_certificados.html">
                            <i class="fas fa-certificate"></i> Certificados
                        </a>
                    </li>
                    <li>
                        <a href="polo_relatorio.html">
                            <i class="fas fa-chart-bar"></i> Relatórios
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Sistema</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_config.html">
                            <i class="fas fa-cogs"></i> Configurações
                        </a>
                    </li>
                    <li>
                        <a href="polo_suporte.html">
                            <i class="fas fa-headset"></i> Suporte
                        </a>
                    </li>
                    <li>
                        <a href="index.html" class="text-danger">
                            <i class="fas fa-sign-out-alt"></i> Sair
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <p>Faciencia EAD © 2024</p>
                <small>Versão 2.5.3</small>
            </div>
        </aside>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Cabeçalho do Painel -->
            <header class="dashboard-header">
                <h1>Painel do Polo</h1>
                <div class="user-info">
                    <div class="polo-info">
                        <div class="polo-name">Polo São Paulo</div>
                        <div class="polo-role">Administrador</div>
                    </div>
                    <div class="user-avatar">
                        <img src="/api/placeholder/50/50" alt="Foto de Perfil">
                        <span class="notification-badge">3</span>
                    </div>
                </div>
            </header>
<!-- Banner de Boas-vindas -->
            <section class="welcome-banner">
                <div class="welcome-content">
                    <h2>Bem-vindo, Polo São Paulo!</h2>
                    <p>Acompanhe o desempenho do seu polo, gerencie alunos e acesse cursos disponíveis em um só lugar.</p>
                    
                    <div class="plan-name">Plano Premium</div>
                    
                    <div class="plan-details">
                        <div class="limit-item">
                            <div class="limit-title">
                                <i class="fas fa-users"></i> Alunos
                            </div>
                            <div class="limit-value">317 / 500</div>
                            <div class="limit-progress">
                                <div class="limit-progress-bar" style="width: 63%;"></div>
                            </div>
                            <div class="limit-text">63% da capacidade utilizada</div>
                        </div>
                        
                        <div class="limit-item">
                            <div class="limit-title">
                                <i class="fas fa-book-open"></i> Cursos
                            </div>
                            <div class="limit-value">32 / 50</div>
                            <div class="limit-progress">
                                <div class="limit-progress-bar" style="width: 64%;"></div>
                            </div>
                            <div class="limit-text">64% da capacidade utilizada</div>
                        </div>
                        
                        <div class="limit-item">
                            <div class="limit-title">
                                <i class="fas fa-certificate"></i> Certificados
                            </div>
                            <div class="limit-value">198 / Ilimitado</div>
                            <div class="limit-progress">
                                <div class="limit-progress-bar" style="width: 30%;"></div>
                            </div>
                            <div class="limit-text">198 certificados emitidos</div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Acesso Rápido -->
            <section class="quick-access">
                <a href="polo_matriculas.html" class="quick-link">
                    <div class="quick-link-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <p class="quick-link-text">Nova Matrícula</p>
                </a>
                
                <a href="polo_cursos.html" class="quick-link">
                    <div class="quick-link-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <p class="quick-link-text">Cursos</p>
                </a>
                
                <a href="polo_certificados.html" class="quick-link">
                    <div class="quick-link-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <p class="quick-link-text">Certificados</p>
                </a>
                
                <a href="polo_relatorio.html" class="quick-link">
                    <div class="quick-link-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <p class="quick-link-text">Relatórios</p>
                </a>
                
                <a href="polo_suporte.html" class="quick-link">
                    <div class="quick-link-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <p class="quick-link-text">Suporte</p>
                </a>
            </section>
            
            <!-- Estatísticas -->
            <section class="dashboard-card">
                <div class="card-header-custom">
                    <h4>Estatísticas do Polo</h4>
                    <a href="polo_relatorio.html" class="card-link">
                        Ver relatório completo <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card total-cursos">
                        <div class="stat-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">32</div>
                            <div class="stat-label">Cursos Ativos</div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 12% este mês
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card total-alunos">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">317</div>
                            <div class="stat-label">Alunos Matriculados</div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 8% este mês
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card novos-alunos">
                        <div class="stat-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">42</div>
                            <div class="stat-label">Novos Alunos</div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 15% este mês
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card certificados">
                        <div class="stat-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">198</div>
                            <div class="stat-label">Certificados Emitidos</div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 24% este mês
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Cursos em Destaque -->
            <section class="dashboard-card">
                <div class="card-header-custom">
                    <h4>Cursos em Destaque</h4>
                    <a href="polo_cursos.html" class="card-link">
                        Ver todos os cursos <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                
                <div class="courses-grid">
                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h3 class="course-title">Administração Financeira</h3>
                            </div>
                            <div class="course-category">Negócios</div>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-users"></i> 48 alunos
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-clock"></i> 60h
                                </div>
                            </div>
                            <p class="course-description">Curso completo sobre administração financeira para empresas e gestão de recursos.</p>
                            <div class="course-actions">
                                <span class="badge bg-success">Matrículas Abertas</span>
                                <a href="polo_cursos_detalhes.html" class="card-link">Detalhes</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h3 class="course-title">Gestão de Pessoas</h3>
                            </div>
                            <div class="course-category">RH</div>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-users"></i> 36 alunos
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-clock"></i> 40h
                                </div>
                            </div>
                            <p class="course-description">Aprenda a gerenciar equipes, desenvolver talentos e melhorar o clima organizacional.</p>
                            <div class="course-actions">
                                <span class="badge bg-warning">Últimas Vagas</span>
                                <a href="polo_cursos_detalhes.html" class="card-link">Detalhes</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h3 class="course-title">Marketing Digital</h3>
                            </div>
                            <div class="course-category">Marketing</div>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-users"></i> 52 alunos
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-clock"></i> 45h
                                </div>
                            </div>
                            <p class="course-description">Estratégias e ferramentas para marketing digital, SEO e gestão de redes sociais.</p>
                            <div class="course-actions">
                                <span class="badge bg-success">Matrículas Abertas</span>
                                <a href="polo_cursos_detalhes.html" class="card-link">Detalhes</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Layout com 2 colunas para informações adicionais -->
            <div class="row">
                <!-- Coluna 1: Alunos Recentes -->
                <div class="col-lg-6">
                    <section class="dashboard-card">
                        <div class="card-header-custom">
                            <h4>Alunos Recentes</h4>
                            <a href="polo_aluno.html" class="card-link">
                                Ver todos <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table custom-table">
                                <thead>
                                    <tr>
                                        <th>Aluno</th>
                                        <th>Curso</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Ana Silva</td>
                                        <td>Marketing Digital</td>
                                        <td><span class="badge bg-success">Ativo</span></td>
                                    </tr>
                                    <tr>
                                        <td>Carlos Oliveira</td>
                                        <td>Gestão de Pessoas</td>
                                        <td><span class="badge bg-success">Ativo</span></td>
                                    </tr>
                                    <tr>
                                        <td>Beatriz Santos</td>
                                        <td>Administração Financeira</td>
                                        <td><span class="badge bg-warning">Pendente</span></td>
                                    </tr>
                                    <tr>
                                        <td>Pedro Costa</td>
                                        <td>Marketing Digital</td>
                                        <td><span class="badge bg-success">Ativo</span></td>
                                    </tr>
                                    <tr>
                                        <td>Juliana Martins</td>
                                        <td>Gestão de Pessoas</td>
                                        <td><span class="badge bg-info">Novo</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </section>
                </div>
                
                <!-- Coluna 2: Atividades Recentes e Desempenho -->
                <div class="col-lg-6">
                    <section class="dashboard-card">
                        <div class="card-header-custom">
                            <h4>Atividades Recentes</h4>
                            <a href="polo_relatorio.html" class="card-link">
                                Ver todas <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        
                        <ul class="activity-feed">
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Novo aluno matriculado em Marketing Digital</div>
                                    <div class="activity-time">Hoje, 14:30</div>
                                </div>
                            </li>
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-certificate"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Certificado emitido para Pedro Costa</div>
                                    <div class="activity-time">Hoje, 11:45</div>
                                </div>
                            </li>
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Novo curso disponível: Data Science</div>
                                    <div class="activity-time">Ontem, 16:20</div>
                                </div>
                            </li>
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Ana Silva concluiu o curso de Marketing Digital</div>
                                    <div class="activity-time">Ontem, 09:15</div>
                                </div>
                            </li>
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Novo comentário do professor no curso de Gestão</div>
                                    <div class="activity-time">14/03/2024, 13:30</div>
                                </div>
                            </li>
                        </ul>
                    </section>
                    
                    <!-- Desempenho do Polo -->
                    <section class="dashboard-card mt-4">
                        <div class="card-header-custom">
                            <h4>Desempenho do Polo</h4>
                        </div>
                        
                        <div class="performance-stat">
                            <div class="performance-header">
                                <div class="performance-title">Taxa de Conclusão</div>
                                <div class="performance-value">78%</div>
                            </div>
                            <div class="performance-progress">
                                <div class="performance-bar bg-success" style="width: 78%;"></div>
                            </div>
                            <div class="performance-text">Acima da média da rede (70%)</div>
                        </div>
                        
                        <div class="performance-stat">
                            <div class="performance-header">
                                <div class="performance-title">Satisfação dos Alunos</div>
                                <div class="performance-value">92%</div>
                            </div>
                            <div class="performance-progress">
                                <div class="performance-bar bg-success" style="width: 92%;"></div>
                            </div>
                            <div class="performance-text">Excelente! Mantenha o bom trabalho</div>
                        </div>
                        
                        <div class="performance-stat">
                            <div class="performance-header">
                                <div class="performance-title">Matrículas Mensais</div>
                                <div class="performance-value">85%</div>
                            </div>
                            <div class="performance-progress">
                                <div class="performance-bar bg-info" style="width: 85%;"></div>
                            </div>
                            <div class="performance-text">15% para atingir a meta mensal</div>
                        </div>
                        
                        <div class="performance-stat">
                            <div class="performance-header">
                                <div class="performance-title">Renovações</div>
                                <div class="performance-value">65%</div>
                            </div>
                            <div class="performance-progress">
                                <div class="performance-bar bg-warning" style="width: 65%;"></div>
                            </div>
                            <div class="performance-text">Abaixo da meta (75%)</div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Script para o menu responsivo -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Função para mostrar/ocultar sidebar em dispositivos móveis
            function toggleSidebar() {
                const sidebar = document.querySelector('.sidebar');
                sidebar.classList.toggle('show');
            }
            
            // Adicionar botão para mobile (menu hamburguer)
            const mainContent = document.querySelector('.main-content');
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.classList.add('mobile-menu-btn');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            mobileMenuBtn.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background-color: var(--primary-purple);
                color: white;
                border: none;
                border-radius: 5px;
                width: 40px;
                height: 40px;
                display: none;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                font-size: 1.2rem;
            `;
            
            document.body.appendChild(mobileMenuBtn);
            mobileMenuBtn.addEventListener('click', toggleSidebar);
            
            // Overlay para fechar o menu quando clicar fora
            const overlay = document.createElement('div');
            overlay.classList.add('sidebar-overlay');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                z-index: 999;
                display: none;
            `;
            
            document.body.appendChild(overlay);
            overlay.addEventListener('click', toggleSidebar);
            
            // Mostrar/ocultar elementos baseado no tamanho da tela
            function handleScreenResize() {
                if (window.innerWidth <= 992) {
                    mobileMenuBtn.style.display = 'flex';
                    
                    // Mostrar overlay apenas quando o menu estiver visível
                    if (document.querySelector('.sidebar').classList.contains('show')) {
                        overlay.style.display = 'block';
                    } else {
                        overlay.style.display = 'none';
                    }
                } else {
                    mobileMenuBtn.style.display = 'none';
                    overlay.style.display = 'none';
                }
            }
            
            // Adicionar event listener para redimensionamento
            window.addEventListener('resize', handleScreenResize);
            
            // Chamar a função uma vez para configurar o estado inicial
            handleScreenResize();
        });
    </script>
</body>
</html>