-- ============================================================================
-- MÓDULO FINANCEIRO - FASE 1: IMPLEMENTAÇÕES CRÍTICAS
-- ============================================================================
-- Data: 2025-01-17
-- Versão: 1.0
-- Descrição: Criação das tabelas para funcionalidades críticas faltantes
-- ============================================================================

-- Verificar se a tabela funcionarios existe, se não, criar
CREATE TABLE IF NOT EXISTS funcionarios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nome VARCHAR(255) NOT NULL,
    cpf VARCHAR(14) UNIQUE NOT NULL,
    cargo VARCHAR(100),
    salario_base DECIMAL(10,2),
    data_admissao DATE,
    data_demissao DATE NULL,
    status ENUM('ativo', 'inativo', 'demitido') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ============================================================================
-- 1. OBRIGAÇÕES TRABALHISTAS
-- ============================================================================

-- Controle de salários a pagar
CREATE TABLE IF NOT EXISTS financeiro_salarios_pagar (
    id INT PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT NOT NULL,
    mes_referencia DATE NOT NULL,
    salario_base DECIMAL(10,2) NOT NULL,
    horas_extras DECIMAL(10,2) DEFAULT 0,
    adicional_noturno DECIMAL(10,2) DEFAULT 0,
    comissoes DECIMAL(10,2) DEFAULT 0,
    vale_transporte DECIMAL(10,2) DEFAULT 0,
    vale_refeicao DECIMAL(10,2) DEFAULT 0,
    desconto_inss DECIMAL(10,2) DEFAULT 0,
    desconto_irrf DECIMAL(10,2) DEFAULT 0,
    desconto_vale_transporte DECIMAL(10,2) DEFAULT 0,
    desconto_vale_refeicao DECIMAL(10,2) DEFAULT 0,
    outros_descontos DECIMAL(10,2) DEFAULT 0,
    salario_liquido DECIMAL(10,2) NOT NULL,
    status ENUM('pendente', 'pago', 'cancelado') DEFAULT 'pendente',
    data_pagamento DATE NULL,
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    UNIQUE KEY unique_funcionario_mes (funcionario_id, mes_referencia)
);

-- Provisões trabalhistas
CREATE TABLE IF NOT EXISTS financeiro_provisoes_trabalhistas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT NOT NULL,
    tipo ENUM('ferias', '13_salario', 'fgts', 'inss', 'rescisao') NOT NULL,
    mes_referencia DATE NOT NULL,
    valor_provisionado DECIMAL(10,2) NOT NULL,
    valor_utilizado DECIMAL(10,2) DEFAULT 0,
    saldo_provisao DECIMAL(10,2) NOT NULL,
    status ENUM('ativo', 'utilizado', 'cancelado') DEFAULT 'ativo',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    INDEX idx_funcionario_tipo (funcionario_id, tipo),
    INDEX idx_mes_referencia (mes_referencia)
);

-- Obrigações trabalhistas a recolher
CREATE TABLE IF NOT EXISTS financeiro_obrigacoes_trabalhistas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo ENUM('fgts', 'inss_empresa', 'inss_funcionario', 'contribuicao_sindical') NOT NULL,
    mes_referencia DATE NOT NULL,
    valor_devido DECIMAL(10,2) NOT NULL,
    valor_pago DECIMAL(10,2) DEFAULT 0,
    data_vencimento DATE NOT NULL,
    data_pagamento DATE NULL,
    codigo_barras TEXT,
    linha_digitavel TEXT,
    status ENUM('pendente', 'pago', 'vencido', 'cancelado') DEFAULT 'pendente',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tipo_mes (tipo, mes_referencia),
    INDEX idx_vencimento (data_vencimento),
    INDEX idx_status (status)
);

-- ============================================================================
-- 2. OBRIGAÇÕES TRIBUTÁRIAS
-- ============================================================================

-- Controle de impostos a recolher
CREATE TABLE IF NOT EXISTS financeiro_impostos_recolher (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo_imposto ENUM('irpj', 'csll', 'pis', 'cofins', 'iss', 'icms', 'simples_nacional') NOT NULL,
    mes_referencia DATE NOT NULL,
    base_calculo DECIMAL(15,2) NOT NULL,
    aliquota DECIMAL(5,4) NOT NULL,
    valor_devido DECIMAL(10,2) NOT NULL,
    valor_pago DECIMAL(10,2) DEFAULT 0,
    data_vencimento DATE NOT NULL,
    data_pagamento DATE NULL,
    codigo_darf VARCHAR(50),
    numero_documento VARCHAR(50),
    status ENUM('pendente', 'pago', 'vencido', 'cancelado') DEFAULT 'pendente',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tipo_mes (tipo_imposto, mes_referencia),
    INDEX idx_vencimento (data_vencimento),
    INDEX idx_status (status)
);

-- Retenções na fonte
CREATE TABLE IF NOT EXISTS financeiro_retencoes_fonte (
    id INT PRIMARY KEY AUTO_INCREMENT,
    documento_origem_tipo ENUM('boleto', 'nota_fiscal', 'recibo') NOT NULL,
    documento_origem_id INT NOT NULL,
    tipo_retencao ENUM('irrf', 'pis', 'cofins', 'csll', 'iss', 'inss') NOT NULL,
    base_calculo DECIMAL(10,2) NOT NULL,
    aliquota DECIMAL(5,4) NOT NULL,
    valor_retido DECIMAL(10,2) NOT NULL,
    data_retencao DATE NOT NULL,
    mes_apuracao DATE NOT NULL,
    status ENUM('retido', 'recolhido', 'cancelado') DEFAULT 'retido',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_origem (documento_origem_tipo, documento_origem_id),
    INDEX idx_tipo_mes (tipo_retencao, mes_apuracao),
    INDEX idx_status (status)
);

-- ============================================================================
-- 3. PATRIMÔNIO LÍQUIDO
-- ============================================================================

-- Controle do patrimônio líquido
CREATE TABLE IF NOT EXISTS financeiro_patrimonio_liquido (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo ENUM('capital_social', 'reserva_legal', 'reserva_estatutaria', 'reserva_contingencia', 'lucros_acumulados', 'prejuizos_acumulados', 'ajustes_exercicios_anteriores') NOT NULL,
    descricao VARCHAR(255) NOT NULL,
    valor_inicial DECIMAL(15,2) DEFAULT 0,
    valor_atual DECIMAL(15,2) NOT NULL,
    data_constituicao DATE NOT NULL,
    exercicio_fiscal YEAR NOT NULL,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tipo_exercicio (tipo, exercicio_fiscal),
    INDEX idx_status (status)
);

-- Movimentações do patrimônio líquido
CREATE TABLE IF NOT EXISTS financeiro_movimentacoes_patrimonio (
    id INT PRIMARY KEY AUTO_INCREMENT,
    patrimonio_id INT NOT NULL,
    tipo_movimentacao ENUM('aumento', 'reducao', 'transferencia', 'ajuste') NOT NULL,
    valor DECIMAL(15,2) NOT NULL,
    data_movimentacao DATE NOT NULL,
    origem VARCHAR(255),
    justificativa TEXT NOT NULL,
    documento_suporte VARCHAR(255),
    aprovado_por VARCHAR(100),
    status ENUM('pendente', 'aprovado', 'rejeitado') DEFAULT 'pendente',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patrimonio_id) REFERENCES financeiro_patrimonio_liquido(id),
    INDEX idx_patrimonio_data (patrimonio_id, data_movimentacao),
    INDEX idx_status (status)
);

-- ============================================================================
-- 4. CONCILIAÇÃO BANCÁRIA
-- ============================================================================

-- Contas bancárias (expandir se necessário)
CREATE TABLE IF NOT EXISTS financeiro_contas_bancarias (
    id INT PRIMARY KEY AUTO_INCREMENT,
    banco_codigo VARCHAR(10) NOT NULL,
    banco_nome VARCHAR(100) NOT NULL,
    agencia VARCHAR(20) NOT NULL,
    conta VARCHAR(30) NOT NULL,
    tipo_conta ENUM('corrente', 'poupanca', 'aplicacao') DEFAULT 'corrente',
    saldo_inicial DECIMAL(15,2) DEFAULT 0,
    saldo_atual DECIMAL(15,2) DEFAULT 0,
    data_abertura DATE,
    status ENUM('ativa', 'inativa', 'encerrada') DEFAULT 'ativa',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_conta (banco_codigo, agencia, conta)
);

-- Extratos bancários importados
CREATE TABLE IF NOT EXISTS financeiro_extratos_bancarios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conta_bancaria_id INT NOT NULL,
    data_movimento DATE NOT NULL,
    data_valor DATE NOT NULL,
    historico TEXT NOT NULL,
    documento VARCHAR(50),
    valor DECIMAL(10,2) NOT NULL,
    tipo ENUM('debito', 'credito') NOT NULL,
    saldo_anterior DECIMAL(15,2),
    saldo_atual DECIMAL(15,2),
    conciliado BOOLEAN DEFAULT FALSE,
    lancamento_id INT NULL,
    data_conciliacao TIMESTAMP NULL,
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (conta_bancaria_id) REFERENCES financeiro_contas_bancarias(id),
    INDEX idx_conta_data (conta_bancaria_id, data_movimento),
    INDEX idx_conciliado (conciliado),
    INDEX idx_lancamento (lancamento_id)
);

-- ============================================================================
-- DADOS INICIAIS (OPCIONAL)
-- ============================================================================

-- Inserir conta bancária padrão se não existir
INSERT IGNORE INTO financeiro_contas_bancarias (banco_codigo, banco_nome, agencia, conta, tipo_conta, saldo_inicial, status) 
VALUES ('001', 'Banco do Brasil', '0001', '00000-0', 'corrente', 0, 'ativa');

-- Inserir patrimônio líquido inicial se não existir
INSERT IGNORE INTO financeiro_patrimonio_liquido (tipo, descricao, valor_inicial, valor_atual, data_constituicao, exercicio_fiscal) 
VALUES ('capital_social', 'Capital Social Inicial', 10000.00, 10000.00, CURDATE(), YEAR(CURDATE()));

-- ============================================================================
-- FIM DO SCRIPT
-- ============================================================================
