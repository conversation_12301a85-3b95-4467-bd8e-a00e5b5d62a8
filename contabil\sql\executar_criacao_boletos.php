<?php
/**
 * Script para executar a criação das tabelas de boletos
 * Execute este arquivo uma vez para criar as estruturas necessárias
 */

// Configurações de erro
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Conectar ao banco
require_once '../../secretaria/includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Criando tabelas para o módulo de boletos...</h2>\n";
    
    // Ler o arquivo SQL
    $sql_content = file_get_contents(__DIR__ . '/criar_tabelas_boletos.sql');
    
    // Dividir em comandos individuais
    $commands = explode(';', $sql_content);
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($commands as $command) {
        $command = trim($command);
        
        // Pular comandos vazios e comentários
        if (empty($command) || strpos($command, '--') === 0) {
            continue;
        }
        
        try {
            $db->query($command);
            $success_count++;
            echo "<p style='color: green;'>✅ Comando executado com sucesso</p>\n";
        } catch (Exception $e) {
            $error_count++;
            echo "<p style='color: red;'>❌ Erro: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            echo "<p style='color: gray;'>Comando: " . htmlspecialchars(substr($command, 0, 100)) . "...</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3>Resumo da Execução:</h3>\n";
    echo "<p><strong>Comandos executados com sucesso:</strong> $success_count</p>\n";
    echo "<p><strong>Comandos com erro:</strong> $error_count</p>\n";
    
    if ($error_count === 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 Todas as tabelas foram criadas com sucesso!</p>\n";
        echo "<p>Agora você pode acessar a página de boletos sem erros.</p>\n";
        echo "<p><a href='../boletos.php' style='background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Acessar Página de Boletos</a></p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ Algumas tabelas podem já existir ou houve erros menores.</p>\n";
        echo "<p>Verifique se a página de boletos está funcionando.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Erro fatal: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criação de Tabelas - Módulo Boletos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 10px;
        }
        .success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Configuração do Módulo de Boletos</h1>
        
        <div class="info">
            <h3>📋 O que este script faz:</h3>
            <ul>
                <li>Cria a tabela <code>polos</code> (se não existir)</li>
                <li>Cria a tabela <code>polos_boletos</code> (se não existir)</li>
                <li>Insere dados de exemplo para teste</li>
                <li>Configura índices e relacionamentos</li>
            </ul>
        </div>
        
        <div class="success">
            <h3>✅ Execução Concluída</h3>
            <p>O script foi executado. Verifique os resultados acima.</p>
            <p><strong>Próximos passos:</strong></p>
            <ol>
                <li>Acesse a página de boletos para verificar se está funcionando</li>
                <li>Se ainda houver erros, verifique a configuração do banco de dados</li>
                <li>Os dados de exemplo podem ser removidos após os testes</li>
            </ol>
        </div>
        
        <hr>
        <p><small>Sistema Reinandus - Módulo Financeiro</small></p>
    </div>
</body>
</html>
