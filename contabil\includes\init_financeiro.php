<?php
/**
 * INICIALIZAÇÃO AUTOMÁTICA DO MÓDULO FINANCEIRO
 * 
 * Este arquivo deve ser incluído em TODAS as páginas do módulo financeiro
 * para garantir sincronização automática e funcionamento correto.
 * 
 * COMO USAR:
 * Adicione esta linha no início de qualquer página do módulo financeiro:
 * require_once 'includes/init_financeiro.php';
 */

// Evitar execução múltipla
if (defined('INIT_FINANCEIRO_LOADED')) {
    return;
}
define('INIT_FINANCEIRO_LOADED', true);

// Verificar se o banco de dados está disponível
if (!isset($db) || !$db) {
    // Tentar carregar o banco se não estiver disponível
    if (file_exists('../secretaria/includes/Database.php')) {
        require_once '../secretaria/includes/Database.php';
        $db = Database::getInstance();
    } elseif (file_exists('../../secretaria/includes/Database.php')) {
        require_once '../../secretaria/includes/Database.php';
        $db = Database::getInstance();
    } else {
        error_log("ERRO: Não foi possível carregar o banco de dados no módulo financeiro");
        return;
    }
}

// Incluir configuração automática do módulo financeiro
$config_path = __DIR__ . '/financeiro_config.php';
if (file_exists($config_path)) {
    require_once $config_path;
} else {
    error_log("AVISO: Arquivo de configuração financeiro não encontrado: $config_path");
}

// Função para incluir automaticamente em páginas existentes
function incluirSincronizacaoAutomatica($arquivo_pagina) {
    $conteudo = file_get_contents($arquivo_pagina);
    
    // Verificar se já tem a inclusão
    if (strpos($conteudo, 'init_financeiro.php') !== false || 
        strpos($conteudo, 'financeiro_config.php') !== false) {
        return false; // Já incluído
    }
    
    // Procurar por padrões onde incluir
    $padroes = [
        '/(\$db = Database::getInstance\(\);)/i',
        '/(require_once.*Database\.php.*\n)/i',
        '/(\$db = new Database\(\);)/i'
    ];
    
    $inclusao = "\n// ============================================================================\n";
    $inclusao .= "// SINCRONIZAÇÃO AUTOMÁTICA - INCLUÍDO AUTOMATICAMENTE\n";
    $inclusao .= "// ============================================================================\n";
    $inclusao .= "require_once 'includes/init_financeiro.php';\n";
    
    foreach ($padroes as $padrao) {
        if (preg_match($padrao, $conteudo)) {
            $novo_conteudo = preg_replace($padrao, '$1' . $inclusao, $conteudo);
            if ($novo_conteudo !== $conteudo) {
                file_put_contents($arquivo_pagina, $novo_conteudo);
                return true;
            }
        }
    }
    
    return false;
}

// Função para aplicar sincronização em todas as páginas do módulo
function aplicarSincronizacaoTodasPaginas() {
    $diretorio_financeiro = dirname(__DIR__);
    $arquivos_php = glob($diretorio_financeiro . '/*.php');
    
    $aplicados = 0;
    foreach ($arquivos_php as $arquivo) {
        if (basename($arquivo) === 'init_financeiro.php') continue;
        
        if (incluirSincronizacaoAutomatica($arquivo)) {
            $aplicados++;
            error_log("Sincronização aplicada em: " . basename($arquivo));
        }
    }
    
    return $aplicados;
}

// Auto-aplicar sincronização se solicitado via GET
if (isset($_GET['auto_apply_sync']) && $_GET['auto_apply_sync'] === 'true') {
    $aplicados = aplicarSincronizacaoTodasPaginas();
    echo json_encode([
        'success' => true,
        'message' => "Sincronização aplicada em $aplicados arquivos",
        'aplicados' => $aplicados
    ]);
    exit;
}

// Verificar integridade do sistema
function verificarIntegridadeSistema() {
    global $db;
    
    if (!$db) return false;
    
    try {
        // Verificar tabelas essenciais
        $tabelas_essenciais = [
            'contas_pagar',
            'contas_receber',
            'contas_bancarias',
            'transacoes_financeiras'
        ];
        
        foreach ($tabelas_essenciais as $tabela) {
            $existe = $db->fetchOne("SHOW TABLES LIKE '$tabela'");
            if (!$existe) {
                error_log("ERRO: Tabela essencial '$tabela' não encontrada");
                return false;
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("ERRO ao verificar integridade: " . $e->getMessage());
        return false;
    }
}

// Função para mostrar status de sincronização
function mostrarStatusSincronizacao() {
    $status = getStatusSincronizacao();
    
    if (!$status['sincronizado']) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>⚠️ Sistema Sincronizando:</strong> Os dados estão sendo atualizados automaticamente...";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 8px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>✅ Sistema Sincronizado:</strong> Todos os dados estão atualizados.";
        echo "</div>";
    }
}

// Verificar integridade na inicialização
if (!verificarIntegridadeSistema()) {
    error_log("AVISO: Sistema financeiro com problemas de integridade");
}

// Registrar inicialização
if (function_exists('error_log')) {
    error_log("Módulo financeiro inicializado com sincronização automática");
}

// Definir constantes úteis
define('FINANCEIRO_VERSION', '3.0');
define('FINANCEIRO_SYNC_ENABLED', true);
define('FINANCEIRO_DEBUG', false);

// Função helper para debug
function debugFinanceiro($mensagem, $dados = null) {
    if (FINANCEIRO_DEBUG) {
        $log = "[FINANCEIRO DEBUG] $mensagem";
        if ($dados) {
            $log .= " - Dados: " . print_r($dados, true);
        }
        error_log($log);
    }
}

// Função para forçar sincronização manual (se necessário)
function forcarSincronizacaoManual() {
    global $db;
    
    if (!$db) return false;
    
    try {
        // Incluir classe de sincronização se disponível
        $sync_file = __DIR__ . '/../SincronizacaoFinanceira.php';
        if (file_exists($sync_file)) {
            require_once $sync_file;
            $sync = new SincronizacaoFinanceira($db);
            return $sync->sincronizacaoCompleta();
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Erro na sincronização manual: " . $e->getMessage());
        return false;
    }
}

// Hook para executar após carregamento da página
register_shutdown_function(function() {
    debugFinanceiro("Página do módulo financeiro finalizada");
});

// Verificar se é uma requisição AJAX e processar adequadamente
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    
    // Para requisições AJAX, garantir que a sincronização seja rápida
    if (function_exists('getStatusSincronizacao')) {
        $status = getStatusSincronizacao();
        if (!$status['sincronizado']) {
            // Forçar sincronização rápida em requisições AJAX
            forcarSincronizacaoManual();
        }
    }
}

// Adicionar cabeçalhos para indicar que o sistema está sincronizado
if (function_exists('getStatusSincronizacao')) {
    $status = getStatusSincronizacao();
    header('X-Financeiro-Sync-Status: ' . ($status['sincronizado'] ? 'OK' : 'SYNCING'));
    header('X-Financeiro-Version: ' . FINANCEIRO_VERSION);
}

debugFinanceiro("Inicialização do módulo financeiro concluída");
?>
