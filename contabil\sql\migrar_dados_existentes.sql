-- Migrar contas a pagar para lançamentos contábeis
INSERT INTO lancamentos_contabeis (numero_lancamento, data_lancamento, historico, valor_total, tipo_lancamento, origem, origem_id)
SELECT 
    CONCAT('CP-', id),
    data_vencimento,
    CONCAT('Conta a Pagar: ', descricao),
    valor,
    'automatico',
    'contas_pagar',
    id
FROM contas_pagar 
WHERE status = 'pago';

-- Migrar contas a receber para lançamentos contábeis  
INSERT INTO lancamentos_contabeis (numero_lancamento, data_lancamento, historico, valor_total, tipo_lancamento, origem, origem_id)
SELECT 
    CONCAT('CR-', id),
    data_recebimento,
    CONCAT('Conta a Receber: ', descricao),
    valor,
    'automatico', 
    'contas_receber',
    id
FROM contas_receber 
WHERE status = 'recebido';