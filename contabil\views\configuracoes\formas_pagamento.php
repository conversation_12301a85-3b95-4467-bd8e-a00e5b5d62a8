<!-- Informações sobre Formas de Pagamento -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Formas de Pagamento do Sistema</h3>
            <div class="mt-2 text-sm text-blue-700">
                <p>As formas de pagamento são pré-definidas pelo sistema e não podem ser editadas.
                Elas incluem as principais opções utilizadas em instituições de ensino.</p>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Formas de Pagamento -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-list text-green-500 mr-2"></i>
            Formas de Pagamento Cadastradas (<?php echo count($formas_pagamento); ?>)
        </h3>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($formas_pagamento)): ?>
            <div class="text-center py-12">
                <i class="fas fa-credit-card text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma forma de pagamento cadastrada</h3>
                <p class="text-gray-500 mb-6">Cadastre as formas de pagamento aceitas pela instituição.</p>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Origem</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($formas_pagamento as $forma): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($forma['nome']); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <?php echo htmlspecialchars($forma['descricao'] ?? ''); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $status_class = $forma['status'] === 'ativo' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                                $status_text = $forma['status'] === 'ativo' ? 'Ativo' : 'Inativo';
                                ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                Padrão do Sistema
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <?php
                                $tipo_class = 'bg-blue-100 text-blue-800';
                                $tipo_text = 'Digital';
                                if (in_array($forma['nome'], ['Dinheiro', 'Boleto Bancário'])) {
                                    $tipo_class = 'bg-green-100 text-green-800';
                                    $tipo_text = 'Tradicional';
                                } elseif (in_array($forma['nome'], ['Cartão de Débito', 'Cartão de Crédito'])) {
                                    $tipo_class = 'bg-purple-100 text-purple-800';
                                    $tipo_text = 'Cartão';
                                }
                                ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $tipo_class; ?>">
                                    <?php echo $tipo_text; ?>
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Informações sobre Formas de Pagamento -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Sobre as Formas de Pagamento</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Configure todas as formas de pagamento aceitas pela instituição</li>
                    <li>Essas formas aparecerão nos formulários de recebimento e pagamento</li>
                    <li>Mantenha apenas as formas realmente utilizadas como "Ativo"</li>
                    <li>A descrição pode incluir informações como taxas ou condições especiais</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Formas de Pagamento Sugeridas -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            Formas de Pagamento Sugeridas
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Digitais -->
            <div>
                <h4 class="text-md font-semibold text-blue-800 mb-3">
                    <i class="fas fa-mobile-alt text-blue-600 mr-2"></i>
                    Pagamentos Digitais
                </h4>
                <ul class="space-y-2 text-sm text-gray-700">
                    <li class="flex items-center"><i class="fas fa-qrcode text-blue-500 mr-2"></i> PIX</li>
                    <li class="flex items-center"><i class="fas fa-university text-blue-500 mr-2"></i> Transferência Bancária</li>
                    <li class="flex items-center"><i class="fas fa-credit-card text-blue-500 mr-2"></i> Cartão de Débito</li>
                    <li class="flex items-center"><i class="fas fa-credit-card text-blue-500 mr-2"></i> Cartão de Crédito</li>
                </ul>
            </div>

            <!-- Tradicionais -->
            <div>
                <h4 class="text-md font-semibold text-green-800 mb-3">
                    <i class="fas fa-money-bill text-green-600 mr-2"></i>
                    Pagamentos Tradicionais
                </h4>
                <ul class="space-y-2 text-sm text-gray-700">
                    <li class="flex items-center"><i class="fas fa-coins text-green-500 mr-2"></i> Dinheiro</li>
                    <li class="flex items-center"><i class="fas fa-money-check text-green-500 mr-2"></i> Cheque</li>
                    <li class="flex items-center"><i class="fas fa-barcode text-green-500 mr-2"></i> Boleto Bancário</li>
                    <li class="flex items-center"><i class="fas fa-file-invoice text-green-500 mr-2"></i> Carnê</li>
                </ul>
            </div>

            <!-- Especiais -->
            <div>
                <h4 class="text-md font-semibold text-purple-800 mb-3">
                    <i class="fas fa-star text-purple-600 mr-2"></i>
                    Formas Especiais
                </h4>
                <ul class="space-y-2 text-sm text-gray-700">
                    <li class="flex items-center"><i class="fas fa-file-contract text-purple-500 mr-2"></i> Desconto em Folha</li>
                    <li class="flex items-center"><i class="fas fa-handshake text-purple-500 mr-2"></i> Convênio Empresarial</li>
                    <li class="flex items-center"><i class="fas fa-graduation-cap text-purple-500 mr-2"></i> Bolsa de Estudos</li>
                    <li class="flex items-center"><i class="fas fa-gift text-purple-500 mr-2"></i> Cortesia</li>
                </ul>
            </div>
        </div>
    </div>
</div>
