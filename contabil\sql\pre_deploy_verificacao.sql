-- ============================================================================
-- PRÉ-DEPLOY VERIFICAÇÃO - SISTEMA DE ROXINHOS
-- ============================================================================
-- Execute este script ANTES do deploy principal para verificar o estado atual
-- do banco de produção e identificar o que precisa ser criado/atualizado
-- ============================================================================

SELECT 'VERIFICAÇÃO PRÉ-DEPLOY - Sistema de Roxinhos' as status;
SELECT '================================================' as separador;

-- ============================================================================
-- 1. VERIFICAR TABELAS EXISTENTES
-- ============================================================================

SELECT 'VERIFICANDO TABELAS EXISTENTES...' as status;

-- Verificar se as tabelas do sistema existem
SELECT 
    TABLE_NAME as tabela,
    TABLE_ROWS as registros_aprox,
    CREATE_TIME as data_criacao,
    TABLE_COMMENT as comentario
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE '%roxinho%'
ORDER BY TABLE_NAME;

-- ============================================================================
-- 2. VERIFICAR ESTRUTURA DA TABELA ROXINHOS ATUAL
-- ============================================================================

SELECT 'VERIFICANDO ESTRUTURA DA TABELA ROXINHOS...' as status;

-- Verificar colunas da tabela roxinhos
SELECT 
    COLUMN_NAME as coluna,
    DATA_TYPE as tipo,
    IS_NULLABLE as permite_null,
    COLUMN_DEFAULT as valor_padrao,
    COLUMN_COMMENT as comentario
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'roxinhos'
ORDER BY ORDINAL_POSITION;

-- ============================================================================
-- 3. VERIFICAR DADOS EXISTENTES
-- ============================================================================

SELECT 'VERIFICANDO DADOS EXISTENTES...' as status;

-- Contar registros na tabela roxinhos
SELECT COUNT(*) as total_roxinhos FROM roxinhos;

-- Mostrar alguns registros de exemplo (se existirem)
SELECT 
    id, 
    nome, 
    cpf, 
    email, 
    status,
    created_at
FROM roxinhos 
LIMIT 5;

-- ============================================================================
-- 4. VERIFICAR TABELA POLOS (DEPENDÊNCIA)
-- ============================================================================

SELECT 'VERIFICANDO TABELA POLOS (DEPENDÊNCIA)...' as status;

-- Verificar estrutura da tabela polos
SELECT 
    COLUMN_NAME as coluna,
    DATA_TYPE as tipo
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'polos'
AND COLUMN_NAME IN ('id', 'nome', 'cidade', 'cnpj', 'status')
ORDER BY ORDINAL_POSITION;

-- Contar polos ativos
SELECT 
    status,
    COUNT(*) as quantidade
FROM polos 
GROUP BY status;

-- ============================================================================
-- 5. VERIFICAR VIEWS EXISTENTES
-- ============================================================================

SELECT 'VERIFICANDO VIEWS EXISTENTES...' as status;

-- Verificar se as views do sistema existem
SELECT 
    TABLE_NAME as view_name,
    TABLE_COMMENT as comentario
FROM information_schema.VIEWS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE '%roxinho%';

-- ============================================================================
-- 6. VERIFICAR TRIGGERS EXISTENTES
-- ============================================================================

SELECT 'VERIFICANDO TRIGGERS EXISTENTES...' as status;

-- Verificar se os triggers do sistema existem
SELECT 
    TRIGGER_NAME as trigger_name,
    EVENT_MANIPULATION as evento,
    EVENT_OBJECT_TABLE as tabela,
    ACTION_TIMING as timing
FROM information_schema.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE() 
AND TRIGGER_NAME LIKE '%roxinho%';

-- ============================================================================
-- 7. VERIFICAR ÍNDICES
-- ============================================================================

SELECT 'VERIFICANDO ÍNDICES DA TABELA ROXINHOS...' as status;

-- Verificar índices da tabela roxinhos
SELECT 
    INDEX_NAME as indice,
    COLUMN_NAME as coluna,
    NON_UNIQUE as nao_unico
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'roxinhos'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- ============================================================================
-- 8. RESUMO DA VERIFICAÇÃO
-- ============================================================================

SELECT 'RESUMO DA VERIFICAÇÃO' as status;
SELECT '===================' as separador;

-- Resumo do que existe e o que precisa ser criado
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinhos') 
        THEN '✅ EXISTE' 
        ELSE '❌ NÃO EXISTE' 
    END as tabela_roxinhos,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinho_polos') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA CRIAR' 
    END as tabela_roxinho_polos,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinho_comissoes') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA CRIAR' 
    END as tabela_roxinho_comissoes,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinho_pagamentos') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA CRIAR' 
    END as tabela_roxinho_pagamentos,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinho_historico') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA CRIAR' 
    END as tabela_roxinho_historico;

-- Verificar se as colunas necessárias existem na tabela roxinhos
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinhos' AND COLUMN_NAME = 'taxa_padrao') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA ADICIONAR' 
    END as coluna_taxa_padrao,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinhos' AND COLUMN_NAME = 'banco') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA ADICIONAR' 
    END as coluna_banco,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinhos' AND COLUMN_NAME = 'pix') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA ADICIONAR' 
    END as coluna_pix,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'roxinhos' AND COLUMN_NAME = 'observacoes') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA ADICIONAR' 
    END as coluna_observacoes;

-- Verificar views
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vw_roxinho_comissoes_detalhadas') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA CRIAR' 
    END as view_comissoes_detalhadas,
    
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vw_roxinho_resumo') 
        THEN '✅ EXISTE' 
        ELSE '❌ PRECISA CRIAR' 
    END as view_resumo;

SELECT 'VERIFICAÇÃO CONCLUÍDA!' as status;
SELECT 'Execute o script deploy_producao_roxinhos.sql para aplicar as mudanças necessárias.' as proximos_passos;
