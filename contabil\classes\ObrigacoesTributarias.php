<?php
/**
 * CLASSE OBRIGACOESTRIBUTARIAS - VERSÃO CORRIGIDA
 * 
 * Gerencia o cálculo de impostos e obrigações tributárias.
 * 
 * Esta versão inclui o método getReceitasMes() que estava faltando,
 * corrigindo o erro fatal na página de gestão tributária.
 */

class ObrigacoesTributarias {
    private $db;
    private $regime;

    public function __construct($regime = 'simples') {
        $this->db = Database::getInstance();
        $this->regime = $regime;
    }

    /**
     * Busca o total de receitas efetivadas em um determinado mês/ano.
     * Este é o método que estava faltando e causava o erro fatal.
     *
     * @param int $mes Mês de referência.
     * @param int $ano Ano de referência.
     * @return float Total das receitas.
     */
    public function getReceitasMes($mes, $ano) {
        $sql = "SELECT COALESCE(SUM(valor), 0) as total 
                FROM transacoes_financeiras 
                WHERE tipo = 'receita' 
                  AND status = 'efetivada' 
                  AND MONTH(data_transacao) = ? 
                  AND YEAR(data_transacao) = ?";
        
        $resultado = $this->db->fetchOne($sql, [$mes, $ano]);
        
        return $resultado['total'] ?? 0.00;
    }

    /**
     * Calcula os impostos para um determinado mês/ano.
     *
     * @param int $mes Mês de referência.
     * @param int $ano Ano de referência.
     * @return array Detalhes dos impostos calculados.
     */
    public function calcularImpostosMes($mes, $ano) {
        // Esta é a linha que causava o erro. Agora o método existe.
        $receita_bruta = $this->getReceitasMes($mes, $ano);
        
        $total_impostos = 0;
        $detalhes = [];

        // Lógica de cálculo baseada no regime tributário
        switch ($this->regime) {
            case 'simples':
                // Exemplo: Alíquota de 6% sobre a receita bruta
                $aliquota = 0.06;
                $total_impostos = $receita_bruta * $aliquota;
                $detalhes['DAS'] = [
                    'base_calculo' => $receita_bruta,
                    'aliquota' => $aliquota * 100,
                    'valor' => $total_impostos
                ];
                break;
            
            // Adicionar outros regimes (lucro_presumido, lucro_real) aqui
            default:
                $total_impostos = 0;
                $detalhes['aviso'] = 'Regime tributário não configurado para cálculo.';
                break;
        }

        // Salvar o cálculo no banco de dados
        $this->salvarCalculo($mes, $ano, $receita_bruta, $total_impostos, $detalhes);

        return [
            'receita_bruta' => $receita_bruta,
            'total_impostos' => $total_impostos,
            'detalhes' => $detalhes
        ];
    }

    /**
     * Salva o resultado do cálculo de impostos no banco.
     */
    private function salvarCalculo($mes, $ano, $receita_bruta, $total_impostos, $detalhes) {
        $sql = "INSERT INTO impostos_calculados 
                (mes_referencia, ano_referencia, regime_tributario, receita_bruta, total_impostos, detalhes_calculo)
                VALUES (?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                receita_bruta = VALUES(receita_bruta),
                total_impostos = VALUES(total_impostos),
                detalhes_calculo = VALUES(detalhes_calculo),
                updated_at = NOW()";
        
        $this->db->query($sql, [
            $mes,
            $ano,
            $this->regime,
            $receita_bruta,
            $total_impostos,
            json_encode($detalhes)
        ]);
    }

    /**
     * Gera as guias de recolhimento baseadas no cálculo.
     */
    public function gerarGuiasRecolhimento($mes, $ano) {
        // Esta é uma implementação de exemplo
        $calculo = $this->db->fetchOne(
            "SELECT * FROM impostos_calculados WHERE mes_referencia = ? AND ano_referencia = ?",
            [$mes, $ano]
        );

        if (!$calculo) {
            throw new Exception("Cálculo de impostos não encontrado para o período.");
        }
    }
    
    public function gerarLancamentosImpostos($mes, $ano) {
        // Lógica para criar os lançamentos em contas a pagar
    }
}