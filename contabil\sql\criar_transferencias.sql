-- Tabela para registrar transferências entre contas e movimentações bancárias
CREATE TABLE IF NOT EXISTS `transferencias_bancarias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tipo_operacao` enum('transferencia','deposito','saque','pix_enviado','pix_recebido','ted_enviado','ted_recebido','doc_enviado','doc_recebido','tarifa','juros') NOT NULL,
  `conta_origem_id` int(11) DEFAULT NULL,
  `conta_destino_id` int(11) DEFAULT NULL,
  `valor` decimal(10,2) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `data_operacao` datetime NOT NULL,
  `numero_documento` varchar(100) DEFAULT NULL,
  `chave_pix` varchar(255) DEFAULT NULL,
  `banco_destino` varchar(100) DEFAULT NULL,
  `agencia_destino` varchar(20) DEFAULT NULL,
  `conta_destino_numero` varchar(30) DEFAULT NULL,
  `nome_favorecido` varchar(255) DEFAULT NULL,
  `cpf_cnpj_favorecido` varchar(20) DEFAULT NULL,
  `tarifa` decimal(10,2) DEFAULT 0.00,
  `categoria_id` int(11) DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `comprovante_arquivo` varchar(255) DEFAULT NULL,
  `status` enum('pendente','processada','cancelada','estornada') NOT NULL DEFAULT 'processada',
  `usuario_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_conta_origem` (`conta_origem_id`),
  KEY `idx_conta_destino` (`conta_destino_id`),
  KEY `idx_data_operacao` (`data_operacao`),
  KEY `idx_tipo_operacao` (`tipo_operacao`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_transferencia_conta_origem` FOREIGN KEY (`conta_origem_id`) REFERENCES `contas_bancarias` (`id`),
  CONSTRAINT `fk_transferencia_conta_destino` FOREIGN KEY (`conta_destino_id`) REFERENCES `contas_bancarias` (`id`),
  CONSTRAINT `fk_transferencia_categoria` FOREIGN KEY (`categoria_id`) REFERENCES `categorias_financeiras` (`id`),
  CONSTRAINT `fk_transferencia_usuario` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabela para controlar saldos atualizados das contas
CREATE TABLE IF NOT EXISTS `saldos_contas_bancarias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conta_bancaria_id` int(11) NOT NULL,
  `data_saldo` date NOT NULL,
  `saldo_inicial_dia` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_entradas` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_saidas` decimal(10,2) NOT NULL DEFAULT 0.00,
  `saldo_final_dia` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_conta_data` (`conta_bancaria_id`, `data_saldo`),
  CONSTRAINT `fk_saldo_conta_bancaria` FOREIGN KEY (`conta_bancaria_id`) REFERENCES `contas_bancarias` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Índices para performance
CREATE INDEX idx_transferencias_data_conta ON transferencias_bancarias(data_operacao, conta_origem_id);
CREATE INDEX idx_transferencias_data_destino ON transferencias_bancarias(data_operacao, conta_destino_id);
CREATE INDEX idx_saldos_data ON saldos_contas_bancarias(data_saldo);
