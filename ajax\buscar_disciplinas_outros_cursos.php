<?php
/**
 * ================================================================
 *                    SISTEMA FACIÊNCIA ERP
 * ================================================================
 * 
 * Script AJAX: Buscar Disciplinas de Outros Cursos
 * Descrição: Busca disciplinas para vincular a um curso específico
 * Versão: 1.0
 * Data: 2024-12-19
 * 
 * Funcionalidades:
 * - Busca disciplinas de outros cursos (exclui o curso atual)
 * - Suporte a paginação
 * - Busca por nome da disciplina
 * - Retorna informações do curso original
 * 
 * ================================================================
 */

// Carregamento do sistema base
require_once __DIR__ . '/../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica se o usuário tem permissão para acessar disciplinas
exigirPermissao('disciplinas');

// Instancia o banco de dados
$db = Database::getInstance();

// Define o tipo de retorno como JSON
header('Content-Type: application/json');

try {
    $query = $_GET['q'] ?? '';
    $curso_atual = $_GET['curso_atual'] ?? 0;
    $page = (int)($_GET['page'] ?? 1);
    $per_page = (int)($_GET['per_page'] ?? 20);
    $offset = ($page - 1) * $per_page;

    // Validação básica
    $query = trim($query);
    
    if (strlen($query) < 2) {
        echo json_encode([
            'success' => false,
            'message' => 'Digite pelo menos 2 caracteres para buscar',
            'disciplinas' => []
        ]);
        exit;
    }

    if (empty($curso_atual)) {
        echo json_encode([
            'success' => false,
            'message' => 'Curso atual é obrigatório',
            'disciplinas' => []
        ]);
        exit;
    }

    // Monta a consulta SQL
    $where = "d.status = 'ativo' AND d.curso_id != ?";
    $params = [$curso_atual];

    if (!empty($query)) {
        $where .= " AND d.nome LIKE ?";
        $searchTerm = '%' . $query . '%';
        $params[] = $searchTerm;
    }

    // Consulta principal com informações do curso
    $sql = "SELECT d.id, d.nome, d.carga_horaria, d.periodo, d.status,
                   c.nome as curso_nome, c.id as curso_id,
                   p.nome as professor_nome
            FROM disciplinas d 
            LEFT JOIN cursos c ON d.curso_id = c.id
            LEFT JOIN professores p ON d.professor_padrao_id = p.id
            WHERE {$where}
            ORDER BY d.nome ASC 
            LIMIT {$offset}, {$per_page}";

    $disciplinas = $db->fetchAll($sql, $params);

    // Conta total para paginação
    $sql_count = "SELECT COUNT(*) as total 
                  FROM disciplinas d 
                  WHERE {$where}";
    $total_result = $db->fetchOne($sql_count, $params);
    $total = $total_result['total'] ?? 0;

    // Calcula informações de paginação
    $total_pages = $total > 0 ? ceil($total / $per_page) : 1;
    $has_next = $page < $total_pages;
    $has_prev = $page > 1;

    // Formata os dados das disciplinas
    $disciplinas_formatadas = [];
    foreach ($disciplinas as $disciplina) {
        $disciplinas_formatadas[] = [
            'id' => $disciplina['id'],
            'nome' => $disciplina['nome'],
            'carga_horaria' => $disciplina['carga_horaria'] ?? 0,
            'periodo' => $disciplina['periodo'] ?? '',
            'status' => $disciplina['status'],
            'curso_id' => $disciplina['curso_id'],
            'curso_nome' => $disciplina['curso_nome'] ?? 'Curso não encontrado',
            'professor_nome' => $disciplina['professor_nome'] ?? '',
            'texto_completo' => $disciplina['nome'] . ' (' . ($disciplina['curso_nome'] ?? 'Sem curso') . ')'
        ];
    }

    echo json_encode([
        'success' => true,
        'disciplinas' => $disciplinas_formatadas,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $per_page,
            'total' => $total,
            'total_pages' => $total_pages,
            'has_next' => $has_next,
            'has_prev' => $has_prev
        ],
        'search' => [
            'query' => $query,
            'found' => count($disciplinas_formatadas),
            'curso_atual' => $curso_atual
        ]
    ]);

} catch (Exception $e) {
    error_log('Erro na busca de disciplinas de outros cursos: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Erro interno do servidor',
        'disciplinas' => []
    ]);
}
?>
