<?php
/**
 * Formulário para alocação de custos
 */

// Buscar centros de custos ativos
try {
    $centros_disponiveis = $db->fetchAll("
        SELECT * FROM financeiro_centro_custos WHERE status = 'ativo' ORDER BY codigo
    ") ?: [];
} catch (Exception $e) {
    $centros_disponiveis = [];
}
?>

<div class="max-w-4xl mx-auto">
    <!-- Header do Formulário -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-share-alt text-blue-500 mr-2"></i>
                    Alocar Custo
                </h2>
                <a href="?acao=dashboard" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar ao Dashboard
                </a>
            </div>
        </div>

        <?php if (empty($centros_disponiveis)): ?>
            <!-- Aviso se não há centros -->
            <div class="p-6">
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Nenhum Centro de Custos Disponível</h3>
                            <p class="text-sm text-yellow-700 mt-1">
                                Você precisa criar pelo menos um centro de custos antes de alocar custos.
                                <a href="?acao=novo_centro" class="font-medium underline">Criar centro de custos</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Formulário -->
            <form method="POST" class="p-6">
                <input type="hidden" name="acao" value="alocar_custo">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Centro de Custos -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Centro de Custos <span class="text-red-500">*</span>
                        </label>
                        <select name="centro_custo_id" required 
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Selecione o centro de custos</option>
                            <?php foreach ($centros_disponiveis as $centro): ?>
                                <option value="<?php echo $centro['id']; ?>">
                                    <?php echo htmlspecialchars($centro['codigo'] . ' - ' . $centro['nome']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Tipo de Custo -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Tipo de Custo <span class="text-red-500">*</span>
                        </label>
                        <select name="tipo_custo" required onchange="atualizarExemplosCusto()"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Selecione o tipo</option>
                            <option value="pessoal">Pessoal</option>
                            <option value="material">Material</option>
                            <option value="servicos">Serviços</option>
                            <option value="equipamentos">Equipamentos</option>
                            <option value="infraestrutura">Infraestrutura</option>
                            <option value="marketing">Marketing</option>
                            <option value="administrativo">Administrativo</option>
                            <option value="outros">Outros</option>
                        </select>
                    </div>

                    <!-- Descrição -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Descrição <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="descricao" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Descrição detalhada do custo">
                    </div>

                    <!-- Valor -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Valor <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-2 text-gray-500">R$</span>
                            <input type="text" name="valor" required 
                                   class="w-full border border-gray-300 rounded-lg pl-10 pr-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="0,00" onkeyup="formatarMoeda(this)">
                        </div>
                    </div>

                    <!-- Data de Competência -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Data de Competência <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="data_competencia" required 
                               value="<?php echo date('Y-m-d'); ?>"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Documento de Origem -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Documento de Origem
                        </label>
                        <input type="text" name="documento_origem" 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Ex: NF 12345, Recibo 001, Contrato 2025">
                    </div>
                </div>

                <!-- Observações -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Observações
                    </label>
                    <textarea name="observacoes" rows="3" 
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Observações adicionais sobre este custo"></textarea>
                </div>

                <!-- Exemplos por Tipo de Custo -->
                <div class="mt-6 p-4 bg-blue-50 rounded-lg" id="exemplos_custo">
                    <h4 class="text-md font-semibold text-blue-800 mb-2">
                        <i class="fas fa-lightbulb mr-2"></i>
                        Exemplos de Custos
                    </h4>
                    <div id="lista_exemplos_custo" class="text-sm text-blue-700">
                        <p>Selecione um tipo de custo para ver exemplos específicos.</p>
                    </div>
                </div>

                <!-- Botões -->
                <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                    <a href="?acao=dashboard" class="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancelar
                    </a>
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Alocar Custo
                    </button>
                </div>
            </form>
        <?php endif; ?>
    </div>
</div>

<script>
// Exemplos por tipo de custo
const exemplosCusto = {
    pessoal: {
        titulo: "Exemplos de Custos de Pessoal",
        lista: [
            "Salários de professores",
            "Salários administrativos",
            "Encargos sociais",
            "Benefícios (vale alimentação, plano de saúde)",
            "Horas extras",
            "Férias e 13º salário",
            "FGTS e contribuições",
            "Treinamentos e capacitação"
        ]
    },
    material: {
        titulo: "Exemplos de Custos de Material",
        lista: [
            "Material didático",
            "Livros e apostilas",
            "Material de escritório",
            "Material de limpeza",
            "Reagentes de laboratório",
            "Uniformes",
            "Material de manutenção",
            "Suprimentos de informática"
        ]
    },
    servicos: {
        titulo: "Exemplos de Custos de Serviços",
        lista: [
            "Serviços de limpeza",
            "Segurança patrimonial",
            "Manutenção predial",
            "Consultoria especializada",
            "Serviços de TI",
            "Telefonia e internet",
            "Correios e malote",
            "Serviços bancários"
        ]
    },
    equipamentos: {
        titulo: "Exemplos de Custos de Equipamentos",
        lista: [
            "Computadores e notebooks",
            "Projetores e equipamentos audiovisuais",
            "Equipamentos de laboratório",
            "Móveis e mobiliário",
            "Equipamentos de segurança",
            "Ar condicionado",
            "Impressoras e copiadoras",
            "Equipamentos esportivos"
        ]
    },
    infraestrutura: {
        titulo: "Exemplos de Custos de Infraestrutura",
        lista: [
            "Energia elétrica",
            "Água e esgoto",
            "Gás",
            "Aluguel de imóveis",
            "Condomínio",
            "IPTU",
            "Seguros",
            "Reformas e melhorias"
        ]
    },
    marketing: {
        titulo: "Exemplos de Custos de Marketing",
        lista: [
            "Publicidade e propaganda",
            "Material promocional",
            "Eventos e feiras",
            "Marketing digital",
            "Produção de conteúdo",
            "Assessoria de imprensa",
            "Pesquisas de mercado",
            "Relacionamento com alunos"
        ]
    },
    administrativo: {
        titulo: "Exemplos de Custos Administrativos",
        lista: [
            "Honorários contábeis",
            "Honorários advocatícios",
            "Taxas e emolumentos",
            "Licenças de software",
            "Assinaturas e anuidades",
            "Viagens e hospedagem",
            "Representação",
            "Despesas bancárias"
        ]
    },
    outros: {
        titulo: "Exemplos de Outros Custos",
        lista: [
            "Multas e penalidades",
            "Perdas e danos",
            "Doações",
            "Despesas eventuais",
            "Custos não classificados",
            "Provisões",
            "Ajustes contábeis",
            "Custos extraordinários"
        ]
    }
};

function atualizarExemplosCusto() {
    const tipo = document.querySelector('select[name="tipo_custo"]').value;
    const listaExemplos = document.getElementById('lista_exemplos_custo');
    
    if (tipo && exemplosCusto[tipo]) {
        const exemplo = exemplosCusto[tipo];
        let html = `
            <h5 class="font-semibold mb-2">${exemplo.titulo}</h5>
            <ul class="list-disc list-inside space-y-1">
        `;
        
        exemplo.lista.forEach(item => {
            html += `<li>${item}</li>`;
        });
        
        html += '</ul>';
        
        listaExemplos.innerHTML = html;
    } else {
        listaExemplos.innerHTML = '<p>Selecione um tipo de custo para ver exemplos específicos.</p>';
    }
}

function formatarMoeda(input) {
    let value = input.value.replace(/\D/g, '');
    value = (value / 100).toFixed(2) + '';
    value = value.replace(".", ",");
    value = value.replace(/(\d)(\d{3})(\d{3}),/g, "$1.$2.$3,");
    value = value.replace(/(\d)(\d{3}),/g, "$1.$2,");
    input.value = value;
}
</script>
