<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-barcode text-blue-500 mr-2"></i>
                Detalhes do Boleto
            </h3>
            <div class="flex items-center space-x-2">
                <a href="boletos.php" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar
                </a>

                <?php if (!empty($boleto['asaas_id']) && !empty($boleto['url_boleto'])): ?>
                    <!-- Link direto do boleto no Asaas -->
                    <a href="<?php echo htmlspecialchars($boleto['url_boleto']); ?>"
                       target="_blank" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-pdf mr-2"></i>
                        Boleto Asaas (PDF)
                    </a>

                    <!-- Backup: link via sistema -->
                    <a href="boletos.php?acao=pdf_asaas&id=<?php echo $boleto['id']; ?>"
                       target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Abrir no Asaas
                    </a>
                <?php elseif (!empty($boleto['asaas_id'])): ?>
                    <!-- Só tem ID do Asaas, sem URL direta -->
                    <a href="boletos.php?acao=pdf_asaas&id=<?php echo $boleto['id']; ?>"
                       target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Abrir no Asaas
                    </a>
                <?php endif; ?>

                <button onclick="window.print()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir Detalhes
                </button>
            </div>
        </div>
    </div>

    <!-- Informações do Boleto -->
    <div class="p-6">
        <?php if (!empty($boleto['asaas_id'])): ?>
            <!-- Link direto do boleto Asaas -->
            <div class="mb-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-pdf text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-green-800">Boleto Oficial do Asaas</h3>
                            <p class="text-sm text-green-700 mt-1">
                                Boleto gerado e disponível no Asaas. Clique para abrir o PDF oficial.
                            </p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3">
                        <?php if (!empty($boleto['url_boleto'])): ?>
                            <!-- Link direto para o PDF -->
                            <a href="<?php echo htmlspecialchars($boleto['url_boleto']); ?>"
                               target="_blank" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                Baixar Boleto PDF
                            </a>
                        <?php endif; ?>

                        <!-- Link via sistema -->
                        <a href="boletos.php?acao=pdf_asaas&id=<?php echo $boleto['id']; ?>"
                           target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            Abrir no Asaas
                        </a>
                    </div>
                </div>

                <?php if (!empty($boleto['url_boleto'])): ?>
                    <div class="mt-4 pt-4 border-t border-green-200">
                        <p class="text-xs text-green-600">
                            <i class="fas fa-link mr-1"></i>
                            <strong>Link direto:</strong>
                            <a href="<?php echo htmlspecialchars($boleto['url_boleto']); ?>" target="_blank" class="underline">
                                <?php echo htmlspecialchars($boleto['url_boleto']); ?>
                            </a>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Boleto não enviado para Asaas -->
            <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Boleto Não Enviado para Asaas</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>Este boleto ainda não foi enviado para o Asaas. Envie para gerar o boleto oficial.</p>
                            <div class="mt-3">
                                <a href="boletos.php?acao=enviar_asaas&id=<?php echo $boleto['id']; ?>"
                                   class="inline-flex items-center px-3 py-1 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-white hover:bg-yellow-50">
                                    <i class="fas fa-cloud-upload-alt mr-2"></i>
                                    Enviar para Asaas
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Informações básicas do boleto -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Status -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <?php if ($boleto['status'] === 'pago'): ?>
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        <?php elseif ($boleto['status'] === 'pendente'): ?>
                            <i class="fas fa-clock text-yellow-500 text-xl"></i>
                        <?php else: ?>
                            <i class="fas fa-times-circle text-red-500 text-xl"></i>
                        <?php endif; ?>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">Status</p>
                        <p class="text-lg font-semibold text-gray-900 capitalize"><?php echo $boleto['status']; ?></p>
                    </div>
                </div>
            </div>

            <!-- Valor -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign text-green-500 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">Valor</p>
                        <p class="text-lg font-semibold text-gray-900">R$ <?php echo number_format($boleto['valor'], 2, ',', '.'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Vencimento -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar text-blue-500 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">Vencimento</p>
                        <p class="text-lg font-semibold text-gray-900"><?php echo date('d/m/Y', strtotime($boleto['data_vencimento'])); ?></p>
                    </div>
                </div>
            </div>

            <!-- Asaas Status -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <?php if (!empty($boleto['asaas_id'])): ?>
                            <i class="fas fa-cloud-check text-green-500 text-xl"></i>
                        <?php else: ?>
                            <i class="fas fa-cloud text-gray-400 text-xl"></i>
                        <?php endif; ?>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">Asaas</p>
                        <p class="text-lg font-semibold text-gray-900">
                            <?php echo !empty($boleto['asaas_id']) ? 'Enviado' : 'Não Enviado'; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detalhes completos -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Dados do Boleto -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Dados do Boleto
                </h4>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Número do Boleto</label>
                        <p class="text-sm text-gray-900 font-mono"><?php echo htmlspecialchars($boleto['numero_boleto']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Nosso Número</label>
                        <p class="text-sm text-gray-900 font-mono"><?php echo htmlspecialchars($boleto['nosso_numero']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Valor</label>
                        <p class="text-lg text-gray-900 font-bold">R$ <?php echo number_format($boleto['valor'], 2, ',', '.'); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Data de Vencimento</label>
                        <p class="text-sm text-gray-900"><?php echo date('d/m/Y', strtotime($boleto['data_vencimento'])); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Data de Emissão</label>
                        <p class="text-sm text-gray-900"><?php echo date('d/m/Y', strtotime($boleto['data_emissao'])); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Status</label>
                        <?php
                        $status_config = [
                            'pendente' => ['class' => 'bg-orange-100 text-orange-800', 'text' => 'Pendente'],
                            'pago' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Pago'],
                            'vencido' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Vencido'],
                            'cancelado' => ['class' => 'bg-gray-100 text-gray-800', 'text' => 'Cancelado']
                        ];
                        $status = $status_config[$boleto['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $boleto['status']];
                        ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status['class']; ?>">
                            <?php echo $status['text']; ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Dados do Pagador -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Dados do Pagador
                </h4>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Nome</label>
                        <p class="text-sm text-gray-900"><?php echo htmlspecialchars($boleto['nome_pagador']); ?></p>
                    </div>
                    <?php if ($boleto['cpf_pagador']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">CPF</label>
                            <p class="text-sm text-gray-900 font-mono"><?php echo htmlspecialchars($boleto['cpf_pagador']); ?></p>
                        </div>
                    <?php endif; ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Tipo</label>
                        <p class="text-sm text-gray-900"><?php echo $boleto['tipo_entidade'] === 'aluno' ? 'Aluno' : 'Empresa/Pessoa Física'; ?></p>
                    </div>
                    <?php if ($boleto['mensalidade_descricao']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Referente a</label>
                            <p class="text-sm text-blue-600"><?php echo htmlspecialchars($boleto['mensalidade_descricao']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Dados Bancários -->
        <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 class="text-md font-semibold text-gray-800 mb-3">Dados Bancários</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <label class="block text-xs font-medium text-gray-600">Banco</label>
                    <p class="text-gray-900 font-mono"><?php echo htmlspecialchars($boleto['banco']); ?></p>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-600">Agência</label>
                    <p class="text-gray-900 font-mono"><?php echo htmlspecialchars($boleto['agencia']); ?></p>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-600">Conta</label>
                    <p class="text-gray-900 font-mono"><?php echo htmlspecialchars($boleto['conta']); ?></p>
                </div>
            </div>
        </div>

        <!-- Código de Barras e Linha Digitável -->
        <div class="mt-6 space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Linha Digitável</label>
                <div class="p-3 bg-gray-100 border border-gray-300 rounded-lg">
                    <p class="text-lg font-mono text-center text-gray-900 tracking-wider">
                        <?php echo htmlspecialchars($boleto['linha_digitavel']); ?>
                    </p>
                </div>
                <button onclick="copiarLinhaDigitavel()" class="mt-2 text-sm text-blue-600 hover:text-blue-800">
                    <i class="fas fa-copy mr-1"></i>
                    Copiar linha digitável
                </button>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Código de Barras</label>
                <div class="p-3 bg-gray-100 border border-gray-300 rounded-lg">
                    <p class="text-sm font-mono text-center text-gray-900 tracking-wider break-all">
                        <?php echo htmlspecialchars($boleto['codigo_barras']); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Observações -->
        <?php if ($boleto['observacoes']): ?>
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
                <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p class="text-sm text-gray-900"><?php echo nl2br(htmlspecialchars($boleto['observacoes'])); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Histórico de Pagamento -->
        <?php if ($boleto['status'] === 'pago' && $boleto['data_pagamento']): ?>
            <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 class="text-md font-semibold text-green-800 mb-2">
                    <i class="fas fa-check-circle mr-2"></i>
                    Pagamento Confirmado
                </h4>
                <p class="text-sm text-green-700">
                    Pago em: <?php echo date('d/m/Y', strtotime($boleto['data_pagamento'])); ?>
                </p>
            </div>
        <?php endif; ?>

        <!-- Ações -->
        <?php if ($boleto['status'] === 'pendente'): ?>
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <h4 class="text-md font-semibold text-gray-800">Ações</h4>
                    <div class="flex items-center space-x-2">
                        <button onclick="marcarComoPago(<?php echo $boleto['id']; ?>)" 
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-check mr-2"></i>
                            Marcar como Pago
                        </button>
                        <button onclick="cancelarBoleto(<?php echo $boleto['id']; ?>, '<?php echo addslashes($boleto['numero_boleto']); ?>')" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-times mr-2"></i>
                            Cancelar Boleto
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function copiarLinhaDigitavel() {
    const linha = '<?php echo $boleto['linha_digitavel']; ?>';
    navigator.clipboard.writeText(linha).then(function() {
        alert('Linha digitável copiada para a área de transferência!');
    });
}

function marcarComoPago(id) {
    const dataAtual = new Date().toISOString().split('T')[0];
    const data = prompt('Data do pagamento (AAAA-MM-DD):', dataAtual);
    
    if (data) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `boletos.php?acao=marcar_pago&id=${id}`;
        form.innerHTML = `<input type="hidden" name="data_pagamento" value="${data}">`;
        document.body.appendChild(form);
        form.submit();
    }
}

function cancelarBoleto(id, numero) {
    if (confirm(`Tem certeza que deseja cancelar o boleto ${numero}?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `boletos.php?acao=cancelar&id=${id}`;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .bg-white {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
</style>
