<?php
/**
 * ENDPOINT AJAX OTIMIZADO PARA BUSCA DE ALUNOS
 * 
 * Endpoint separado para melhor performance e cache
 */

require_once '../../secretaria/includes/init.php';
require_once '../../secretaria/includes/Database.php';

// Headers para AJAX (antes de qualquer output)
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

// Verificar se usuário está logado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit;
}

try {
    $db = Database::getInstance();
    $acao = $_GET['acao'] ?? '';

    switch ($acao) {
        case 'buscar_alunos':
            buscarAlunos($db);
            break;

        case 'contar_alunos':
            contarAlunos($db);
            break;

        case 'buscar_aluno_por_id':
            buscarAlunoPorId($db);
            break;

        default:
            throw new Exception('Ação não encontrada: ' . $acao);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

function buscarAlunos($db) {
    try {
        $termo = $_GET['termo'] ?? '';
        $polo_id = $_GET['polo_id'] ?? '';
        $curso_id = $_GET['curso_id'] ?? '';
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        if (strlen($termo) < 2) {
            echo json_encode([
                'alunos' => [],
                'total' => 0,
                'page' => $page,
                'has_more' => false
            ]);
            return;
        }
    
    // Construir WHERE clause
    $where = ["m.status = 'ativo'"];
    $params = [];
    
    // Busca por nome ou CPF
    $termo_clean = preg_replace('/[^0-9a-zA-ZÀ-ÿ\s]/', '', $termo);
    if (is_numeric($termo_clean)) {
        // Se for numérico, buscar por CPF
        $where[] = "a.cpf LIKE ?";
        $params[] = "%$termo_clean%";
    } else {
        // Se for texto, buscar por nome
        $where[] = "a.nome LIKE ?";
        $params[] = "%$termo_clean%";
    }
    
    if ($polo_id) {
        $where[] = "m.polo_id = ?";
        $params[] = $polo_id;
    }
    
    if ($curso_id) {
        $where[] = "m.curso_id = ?";
        $params[] = $curso_id;
    }
    
    $where_sql = implode(' AND ', $where);
    
    // Contar total de matrículas (não alunos únicos)
    $total = $db->fetchOne("
        SELECT COUNT(*) as total
        FROM alunos a
        JOIN matriculas m ON a.id = m.aluno_id
        JOIN cursos c ON m.curso_id = c.id
        LEFT JOIN polos p ON m.polo_id = p.id
        WHERE $where_sql
    ", $params)['total'];
    
    // Buscar matrículas específicas (não apenas alunos)
    $alunos = $db->fetchAll("
        SELECT
            a.id as aluno_id,
            m.id as matricula_id,
            a.nome,
            a.cpf,
            c.id as curso_id,
            c.nome as curso_nome,
            p.id as polo_id,
            p.nome as polo_nome,
            a.email,
            a.telefone,
            m.data_inicio,
            m.status as status_matricula
        FROM alunos a
        JOIN matriculas m ON a.id = m.aluno_id
        JOIN cursos c ON m.curso_id = c.id
        LEFT JOIN polos p ON m.polo_id = p.id
        WHERE $where_sql
        ORDER BY a.nome, c.nome
        LIMIT $limit OFFSET $offset
    ", $params);
    
    // Formatar CPF para exibição
    foreach ($alunos as &$aluno) {
        $aluno['cpf_formatado'] = formatarCPF($aluno['cpf']);
    }
    
        echo json_encode([
            'alunos' => $alunos,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'has_more' => ($offset + $limit) < $total
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'error' => 'Erro na busca de alunos: ' . $e->getMessage(),
            'alunos' => [],
            'total' => 0
        ]);
    }
}

function contarAlunos($db) {
    $polo_id = $_GET['polo_id'] ?? '';
    $curso_id = $_GET['curso_id'] ?? '';
    $mes = $_GET['mes'] ?? '';
    $ano = $_GET['ano'] ?? '';
    
    $where = ["m.status = 'ativo'"];
    $params = [];
    
    if ($polo_id) {
        $where[] = "m.polo_id = ?";
        $params[] = $polo_id;
    }
    
    if ($curso_id) {
        $where[] = "m.curso_id = ?";
        $params[] = $curso_id;
    }
    
    $where_sql = implode(' AND ', $where);
    
    // Contar alunos ativos
    $total = $db->fetchOne("
        SELECT COUNT(DISTINCT a.id) as total
        FROM alunos a
        JOIN matriculas m ON a.id = m.aluno_id
        WHERE $where_sql
    ", $params)['total'];
    
    // Verificar mensalidades já geradas
    $ja_geradas = 0;
    if ($mes && $ano) {
        $where_mensalidades = $where;
        $params_mensalidades = $params;
        $where_mensalidades[] = "mens.mes = ? AND mens.ano = ?";
        $params_mensalidades[] = $mes;
        $params_mensalidades[] = $ano;
        
        $where_mens_sql = str_replace('m.status', 'mat.status', implode(' AND ', $where_mensalidades));
        
        $ja_geradas = $db->fetchOne("
            SELECT COUNT(DISTINCT mens.aluno_id) as total
            FROM mensalidades mens
            JOIN matriculas mat ON mens.aluno_id = mat.aluno_id
            WHERE $where_mens_sql
        ", $params_mensalidades)['total'];
    }
    
    // Calcular valor estimado
    $valor_estimado = 0;
    if ($total > 0) {
        $valor_medio = $db->fetchOne("
            SELECT AVG(COALESCE(p.valor_mensalidade, c.valor_mensalidade, 500.00)) as valor_medio
            FROM alunos a
            JOIN matriculas m ON a.id = m.aluno_id
            JOIN cursos c ON m.curso_id = c.id
            LEFT JOIN polos p ON m.polo_id = p.id
            WHERE $where_sql
        ", $params)['valor_medio'] ?? 500;
        
        $valor_estimado = ($total - $ja_geradas) * $valor_medio;
    }
    
    echo json_encode([
        'total_alunos' => $total,
        'ja_geradas' => $ja_geradas,
        'novas' => max(0, $total - $ja_geradas),
        'valor_estimado' => $valor_estimado,
        'valor_medio' => $valor_medio ?? 0
    ]);
}

function buscarAlunoPorId($db) {
    $aluno_id = $_GET['aluno_id'] ?? '';
    
    if (!$aluno_id) {
        throw new Exception('ID do aluno não informado');
    }
    
    $aluno = $db->fetchOne("
        SELECT DISTINCT 
            a.id, 
            a.nome, 
            a.cpf, 
            a.email,
            a.telefone,
            c.nome as curso_nome, 
            p.nome as polo_nome,
            m.data_inicio,
            m.status as status_matricula
        FROM alunos a
        JOIN matriculas m ON a.id = m.aluno_id
        JOIN cursos c ON m.curso_id = c.id
        LEFT JOIN polos p ON m.polo_id = p.id
        WHERE a.id = ? AND m.status = 'ativo'
        ORDER BY m.data_inicio DESC
        LIMIT 1
    ", [$aluno_id]);
    
    if (!$aluno) {
        throw new Exception('Aluno não encontrado ou sem matrícula ativa');
    }
    
    $aluno['cpf_formatado'] = formatarCPF($aluno['cpf']);
    
    echo json_encode($aluno);
}

function formatarCPF($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    if (strlen($cpf) === 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}
?>
