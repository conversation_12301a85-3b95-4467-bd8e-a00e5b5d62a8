<?php
require_once '../includes/config.php';

class PatrimonioLiquido {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Calcular patrimônio líquido atual
     */
    public function calcularPatrimonioAtual() {
        $ativos = $this->calcularAtivos();
        $passivos = $this->calcularPassivos();
        
        return [
            'ativos' => $ativos,
            'passivos' => $passivos,
            'patrimonio_liquido' => $ativos['total'] - $passivos['total'],
            'data_calculo' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Calcular ativos
     */
    private function calcularAtivos() {
        // Caixa e bancos
        $caixa_bancos = $this->db->fetch("
            SELECT COALESCE(SUM(saldo_atual), 0) as total
            FROM contas_bancarias 
            WHERE status = 'ativo'
        ")['total'];
        
        // Contas a receber
        $contas_receber = $this->db->fetch("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM contas_receber 
            WHERE status = 'pendente'
        ")['total'];
        
        // Investimentos
        $investimentos = $this->db->fetch("
            SELECT COALESCE(SUM(valor_atual), 0) as total
            FROM investimentos 
            WHERE status = 'ativo'
        ")['total'] ?? 0;
        
        // Imobilizado
        $imobilizado = $this->db->fetch("
            SELECT COALESCE(SUM(valor_atual), 0) as total
            FROM patrimonio_imobilizado 
            WHERE status = 'ativo'
        ")['total'] ?? 0;
        
        // Estoque
        $estoque = $this->db->fetch("
            SELECT COALESCE(SUM(quantidade * valor_unitario), 0) as total
            FROM estoque 
            WHERE quantidade > 0
        ")['total'] ?? 0;
        
        return [
            'caixa_bancos' => $caixa_bancos,
            'contas_receber' => $contas_receber,
            'investimentos' => $investimentos,
            'imobilizado' => $imobilizado,
            'estoque' => $estoque,
            'total' => $caixa_bancos + $contas_receber + $investimentos + $imobilizado + $estoque
        ];
    }
    
    /**
     * Calcular passivos
     */
    private function calcularPassivos() {
        // Contas a pagar
        $contas_pagar = $this->db->fetch("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM contas_pagar 
            WHERE status = 'pendente'
        ")['total'];
        
        // Empréstimos
        $emprestimos = $this->db->fetch("
            SELECT COALESCE(SUM(saldo_devedor), 0) as total
            FROM emprestimos 
            WHERE status = 'ativo'
        ")['total'] ?? 0;
        
        // Financiamentos
        $financiamentos = $this->db->fetch("
            SELECT COALESCE(SUM(saldo_devedor), 0) as total
            FROM financiamentos 
            WHERE status = 'ativo'
        ")['total'] ?? 0;
        
        // Impostos a pagar
        $impostos = $this->db->fetch("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM impostos_pagar 
            WHERE status = 'pendente'
        ")['total'] ?? 0;
        
        return [
            'contas_pagar' => $contas_pagar,
            'emprestimos' => $emprestimos,
            'financiamentos' => $financiamentos,
            'impostos' => $impostos,
            'total' => $contas_pagar + $emprestimos + $financiamentos + $impostos
        ];
    }
    
    /**
     * Evolução do patrimônio líquido
     */
    public function evolucaoPatrimonio($periodo_inicio, $periodo_fim) {
        $evolucao = [];
        $data_atual = new DateTime($periodo_inicio);
        $data_fim = new DateTime($periodo_fim);
        
        while ($data_atual <= $data_fim) {
            $data_str = $data_atual->format('Y-m-d');
            
            $patrimonio = $this->calcularPatrimonioNaData($data_str);
            $evolucao[] = [
                'data' => $data_str,
                'patrimonio_liquido' => $patrimonio,
                'data_formatada' => $data_atual->format('d/m/Y')
            ];
            
            $data_atual->add(new DateInterval('P1M')); // Adicionar 1 mês
        }
        
        return $evolucao;
    }
    
    /**
     * Calcular patrimônio em data específica
     */
    private function calcularPatrimonioNaData($data) {
        // Saldo em contas bancárias na data
        $saldo_contas = $this->db->fetch("
            SELECT COALESCE(SUM(
                cb.saldo_inicial + 
                COALESCE(movimentacoes.creditos, 0) - 
                COALESCE(movimentacoes.debitos, 0)
            ), 0) as total
            FROM contas_bancarias cb
            LEFT JOIN (
                SELECT 
                    conta_bancaria_id,
                    SUM(CASE WHEN tipo = 'receita' THEN valor ELSE 0 END) as creditos,
                    SUM(CASE WHEN tipo = 'despesa' THEN valor ELSE 0 END) as debitos
                FROM transacoes_financeiras 
                WHERE data_transacao <= ? AND status = 'efetivada'
                GROUP BY conta_bancaria_id
            ) movimentacoes ON cb.id = movimentacoes.conta_bancaria_id
            WHERE cb.status = 'ativo'
        ", [$data])['total'];
        
        // Contas a receber até a data
        $contas_receber = $this->db->fetch("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM contas_receber 
            WHERE data_vencimento <= ? AND status = 'pendente'
        ", [$data])['total'];
        
        // Contas a pagar até a data
        $contas_pagar = $this->db->fetch("
            SELECT COALESCE(SUM(valor), 0) as total
            FROM contas_pagar 
            WHERE data_vencimento <= ? AND status = 'pendente'
        ", [$data])['total'];
        
        return $saldo_contas + $contas_receber - $contas_pagar;
    }
    
    /**
     * Análise de liquidez
     */
    public function analiseLiquidez() {
        $ativos = $this->calcularAtivos();
        $passivos = $this->calcularPassivos();
        
        // Liquidez corrente
        $ativo_circulante = $ativos['caixa_bancos'] + $ativos['contas_receber'];
        $passivo_circulante = $passivos['contas_pagar'] + $passivos['impostos'];
        
        $liquidez_corrente = $passivo_circulante > 0 ? $ativo_circulante / $passivo_circulante : 0;
        
        // Liquidez seca (sem estoque)
        $liquidez_seca = $passivo_circulante > 0 ? 
            ($ativo_circulante - $ativos['estoque']) / $passivo_circulante : 0;
        
        // Liquidez imediata (apenas caixa)
        $liquidez_imediata = $passivo_circulante > 0 ? 
            $ativos['caixa_bancos'] / $passivo_circulante : 0;
        
        return [
            'liquidez_corrente' => $liquidez_corrente,
            'liquidez_seca' => $liquidez_seca,
            'liquidez_imediata' => $liquidez_imediata,
            'ativo_circulante' => $ativo_circulante,
            'passivo_circulante' => $passivo_circulante,
            'interpretacao' => $this->interpretarLiquidez($liquidez_corrente)
        ];
    }
    
    /**
     * Interpretar índices de liquidez
     */
    private function interpretarLiquidez($liquidez_corrente) {
        if ($liquidez_corrente >= 2.0) {
            return ['status' => 'excelente', 'cor' => 'green', 'texto' => 'Liquidez excelente'];
        } elseif ($liquidez_corrente >= 1.5) {
            return ['status' => 'boa', 'cor' => 'blue', 'texto' => 'Boa liquidez'];
        } elseif ($liquidez_corrente >= 1.0) {
            return ['status' => 'adequada', 'cor' => 'orange', 'texto' => 'Liquidez adequada'];
        } else {
            return ['status' => 'baixa', 'cor' => 'red', 'texto' => 'Liquidez baixa - Atenção!'];
        }
    }
    
    /**
     * Salvar snapshot do patrimônio
     */
    public function salvarSnapshot() {
        $patrimonio = $this->calcularPatrimonioAtual();
        
        $this->db->execute("
            INSERT INTO patrimonio_snapshots 
            (data_snapshot, total_ativos, total_passivos, patrimonio_liquido, 
             detalhes_ativos, detalhes_passivos, data_criacao)
            VALUES (NOW(), ?, ?, ?, ?, ?, NOW())
        ", [
            $patrimonio['ativos']['total'],
            $patrimonio['passivos']['total'],
            $patrimonio['patrimonio_liquido'],
            json_encode($patrimonio['ativos']),
            json_encode($patrimonio['passivos'])
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Histórico de snapshots
     */
    public function historicoSnapshots($limite = 12) {
        return $this->db->fetchAll("
            SELECT * FROM patrimonio_snapshots 
            ORDER BY data_snapshot DESC 
            LIMIT ?
        ", [$limite]);
    }
}
?>