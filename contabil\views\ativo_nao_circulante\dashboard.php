<?php
/**
 * Dashboard do Ativo Não Circulante
 */
?>

<!-- Dashboard Principal -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total de Ativos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-building text-indigo-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total de Ativos</p>
                <p class="text-2xl font-semibold text-gray-900"><?php echo $total_geral['total_ativos']; ?></p>
                <p class="text-sm text-gray-500">cadastrados</p>
            </div>
        </div>
    </div>

    <!-- Valor de Aquisição -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-blue-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Valor de Aquisição</p>
                <p class="text-2xl font-semibold text-blue-600">
                    R$ <?php echo number_format($total_geral['valor_aquisicao_total'], 2, ',', '.'); ?>
                </p>
                <p class="text-sm text-gray-500">valor histórico</p>
            </div>
        </div>
    </div>

    <!-- Depreciação Acumulada -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-chart-line-down text-red-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Depreciação Acumulada</p>
                <p class="text-2xl font-semibold text-red-600">
                    R$ <?php echo number_format($total_geral['depreciacao_total'], 2, ',', '.'); ?>
                </p>
                <p class="text-sm text-gray-500">depreciado</p>
            </div>
        </div>
    </div>

    <!-- Valor Líquido -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-chart-line text-green-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Valor Líquido</p>
                <p class="text-2xl font-semibold text-green-600">
                    R$ <?php echo number_format($total_geral['valor_liquido_total'], 2, ',', '.'); ?>
                </p>
                <p class="text-sm text-gray-500">valor atual</p>
            </div>
        </div>
    </div>
</div>

<!-- Menu de Ações -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Imobilizado -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-desktop text-blue-500 mr-2"></i>
                Imobilizado
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Móveis, equipamentos, veículos e instalações</p>
        <div class="space-y-2">
            <a href="?acao=listar&tipo=imobilizado" class="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-list mr-2"></i>
                Ver Imobilizado
            </a>
            <a href="?acao=novo&tipo=imobilizado" class="block w-full bg-indigo-600 text-white text-center py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Novo Bem
            </a>
        </div>
    </div>

    <!-- Intangível -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-lightbulb text-purple-500 mr-2"></i>
                Intangível
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Software, marcas, patentes e direitos</p>
        <div class="space-y-2">
            <a href="?acao=listar&tipo=intangivel" class="block w-full bg-purple-600 text-white text-center py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-list mr-2"></i>
                Ver Intangível
            </a>
            <a href="?acao=novo&tipo=intangivel" class="block w-full bg-violet-600 text-white text-center py-2 px-4 rounded-lg hover:bg-violet-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Novo Intangível
            </a>
        </div>
    </div>

    <!-- Investimentos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-chart-pie text-green-500 mr-2"></i>
                Investimentos
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Investimentos de longo prazo e participações</p>
        <div class="space-y-2">
            <a href="?acao=listar&tipo=investimento" class="block w-full bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-list mr-2"></i>
                Ver Investimentos
            </a>
            <a href="?acao=novo&tipo=investimento" class="block w-full bg-emerald-600 text-white text-center py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Novo Investimento
            </a>
        </div>
    </div>

    <!-- Relatórios -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-chart-bar text-orange-500 mr-2"></i>
                Relatórios
            </h3>
        </div>
        <p class="text-gray-600 mb-4">Relatórios de patrimônio e depreciação</p>
        <div class="space-y-2">
            <button onclick="alert('Relatório em desenvolvimento')" class="block w-full bg-orange-600 text-white text-center py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors">
                <i class="fas fa-file-pdf mr-2"></i>
                Relatório Patrimônio
            </button>
            <button onclick="alert('Relatório em desenvolvimento')" class="block w-full bg-amber-600 text-white text-center py-2 px-4 rounded-lg hover:bg-amber-700 transition-colors">
                <i class="fas fa-chart-line mr-2"></i>
                Depreciação
            </button>
        </div>
    </div>
</div>

<!-- Resumo por Tipo -->
<?php if (!empty($resumo_tipos)): ?>
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Resumo por Tipo -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-chart-pie text-indigo-500 mr-2"></i>
                Resumo por Tipo de Ativo
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php foreach ($resumo_tipos as $tipo): ?>
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900">
                                <?php echo ucfirst(str_replace('_', ' ', $tipo['tipo_ativo'])); ?>
                            </h4>
                            <p class="text-sm text-gray-500"><?php echo $tipo['quantidade']; ?> itens</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">
                                R$ <?php echo number_format($tipo['valor_liquido_total'], 2, ',', '.'); ?>
                            </p>
                            <p class="text-sm text-gray-500">
                                Depreciado: R$ <?php echo number_format($tipo['depreciacao_total'], 2, ',', '.'); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Resumo por Categoria -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-tags text-blue-500 mr-2"></i>
                Resumo por Categoria
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php foreach ($resumo_categorias as $categoria): ?>
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900">
                                <?php echo ucfirst(str_replace('_', ' ', $categoria['categoria'])); ?>
                            </h4>
                            <p class="text-sm text-gray-500"><?php echo $categoria['quantidade']; ?> itens</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">
                                R$ <?php echo number_format($categoria['valor_liquido_total'], 2, ',', '.'); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Ativos com Maior Depreciação -->
<?php if (!empty($ativos_depreciacao)): ?>
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
            Ativos com Maior Depreciação
        </h3>
        <p class="text-sm text-gray-600 mt-1">Ativos que mais perderam valor percentualmente</p>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categoria</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor Aquisição</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Depreciação</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor Líquido</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% Depreciado</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($ativos_depreciacao as $ativo): ?>
                    <?php $percentual_depreciado = ($ativo['valor_aquisicao'] > 0) ? ($ativo['depreciacao_acumulada'] / $ativo['valor_aquisicao']) * 100 : 0; ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($ativo['descricao']); ?></div>
                            <?php if ($ativo['numero_patrimonio']): ?>
                                <div class="text-sm text-gray-500">Patrimônio: <?php echo htmlspecialchars($ativo['numero_patrimonio']); ?></div>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo ucfirst(str_replace('_', ' ', $ativo['categoria'])); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            R$ <?php echo number_format($ativo['valor_aquisicao'], 2, ',', '.'); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                            R$ <?php echo number_format($ativo['depreciacao_acumulada'], 2, ',', '.'); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            R$ <?php echo number_format($ativo['valor_liquido'], 2, ',', '.'); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-red-500 h-2 rounded-full" style="width: <?php echo min(100, $percentual_depreciado); ?>%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900">
                                    <?php echo number_format($percentual_depreciado, 1, ',', '.'); ?>%
                                </span>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>
