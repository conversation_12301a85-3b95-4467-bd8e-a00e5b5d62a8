<?php
/**
 * Função auxiliar para escapar valores que podem ser null
 */
function safe_html($value, $default = '') {
    return htmlspecialchars($value ?? $default);
}
?>

<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        Filtros
    </h3>
    
    <form id="form-filtros" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <input type="hidden" name="acao" value="listar">
        
        <!-- Status -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select name="status" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Todos</option>
                <option value="pendente" <?php echo ($_GET['status'] ?? '') === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                <option value="pago" <?php echo ($_GET['status'] ?? '') === 'pago' ? 'selected' : ''; ?>>Pago</option>
                <option value="vencido" <?php echo ($_GET['status'] ?? '') === 'vencido' ? 'selected' : ''; ?>>Vencido</option>
                <option value="cancelado" <?php echo ($_GET['status'] ?? '') === 'cancelado' ? 'selected' : ''; ?>>Cancelado</option>
            </select>
        </div>

        <!-- Tipo -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
            <select name="tipo_entidade" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Todos</option>
                <option value="aluno" <?php echo ($_GET['tipo_entidade'] ?? '') === 'aluno' ? 'selected' : ''; ?>>Aluno</option>
                <option value="empresa" <?php echo ($_GET['tipo_entidade'] ?? '') === 'empresa' ? 'selected' : ''; ?>>Empresa</option>
            </select>
        </div>

        <!-- Data Início -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
            <input type="date" name="data_inicio" value="<?php echo htmlspecialchars($_GET['data_inicio'] ?? ''); ?>" 
                   class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        </div>

        <!-- Data Fim -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
            <input type="date" name="data_fim" value="<?php echo htmlspecialchars($_GET['data_fim'] ?? ''); ?>" 
                   class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        </div>

        <!-- Busca -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
            <div class="flex">
                <input type="text" name="busca" value="<?php echo htmlspecialchars($_GET['busca'] ?? ''); ?>" 
                       placeholder="Número ou nome..." 
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-r-lg transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Resumo -->
<?php
$total_boletos = count($boletos);
$total_pendentes = 0;
$total_pagos = 0;
$total_vencidos = 0;
$valor_total = 0;
$valor_pago = 0;

foreach ($boletos as $boleto) {
    $valor_total += $boleto['valor'];
    
    if ($boleto['status'] === 'pendente') {
        $total_pendentes++;
        if ($boleto['data_vencimento'] < date('Y-m-d')) {
            $total_vencidos++;
        }
    } elseif ($boleto['status'] === 'pago') {
        $total_pagos++;
        $valor_pago += $boleto['valor'];
    }
}
?>

<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Boletos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Boletos</p>
                <p class="text-2xl font-bold text-blue-600"><?php echo $total_boletos; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($valor_total, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-barcode text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Pendentes -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Pendentes</p>
                <p class="text-2xl font-bold text-orange-600"><?php echo $total_pendentes; ?></p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-clock text-orange-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Vencidos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Vencidos</p>
                <p class="text-2xl font-bold text-red-600"><?php echo $total_vencidos; ?></p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Pagos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Pagos</p>
                <p class="text-2xl font-bold text-green-600"><?php echo $total_pagos; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($valor_pago, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Boletos -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-blue-500 mr-2"></i>
                Boletos Bancários (<?php echo count($boletos); ?>)
            </h3>
            <div class="flex items-center space-x-2">
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
                <a href="relatorios.php?tipo=boletos&formato=excel" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    Excel
                </a>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($boletos)): ?>
            <div class="text-center py-12">
                <i class="fas fa-barcode text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum boleto encontrado</h3>
                <p class="text-gray-500 mb-6">Não há boletos que correspondam aos filtros aplicados.</p>
                <a href="boletos.php?acao=gerar" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Gerar Primeiro Boleto
                </a>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Número</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pagador</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vencimento</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($boletos as $boleto): ?>
                        <?php
                        $is_vencido = $boleto['data_vencimento'] < date('Y-m-d') && $boleto['status'] === 'pendente';
                        
                        $status_config = [
                            'pendente' => ['class' => 'bg-orange-100 text-orange-800', 'text' => 'Pendente'],
                            'pago' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Pago'],
                            'vencido' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Vencido'],
                            'cancelado' => ['class' => 'bg-gray-100 text-gray-800', 'text' => 'Cancelado']
                        ];
                        
                        $status_atual = $is_vencido ? 'vencido' : $boleto['status'];
                        $status = $status_config[$status_atual] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $boleto['status']];
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo safe_html($boleto['numero_boleto']); ?>
                                </div>
                                <div class="text-sm text-gray-500">
                                    Nosso Nº: <?php echo safe_html($boleto['nosso_numero']); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo safe_html($boleto['nome_pagador']); ?>
                                </div>
                                <?php if (!empty($boleto['cpf_pagador'])): ?>
                                    <div class="text-sm text-gray-500">
                                        CPF: <?php echo safe_html($boleto['cpf_pagador']); ?>
                                    </div>
                                <?php endif; ?>
                                <?php if (!empty($boleto['mensalidade_descricao'])): ?>
                                    <div class="text-xs text-blue-600">
                                        <?php echo safe_html($boleto['mensalidade_descricao']); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                R$ <?php echo number_format($boleto['valor'], 2, ',', '.'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y', strtotime($boleto['data_vencimento'])); ?>
                                <?php if ($is_vencido): ?>
                                    <br><span class="text-xs text-red-600">
                                        (<?php echo abs((strtotime($boleto['data_vencimento']) - time()) / (60 * 60 * 24)); ?> dias)
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status['class']; ?>">
                                    <?php echo $status['text']; ?>
                                </span>
                                <?php if ($boleto['data_pagamento'] ?? null): ?>
                                    <div class="text-xs text-gray-500 mt-1">
                                        Pago em: <?php echo date('d/m/Y', strtotime($boleto['data_pagamento'])); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <!-- Visualizar detalhes locais -->
                                    <a href="boletos.php?acao=visualizar&id=<?php echo $boleto['id']; ?>"
                                       class="text-blue-600 hover:text-blue-900" title="Visualizar Detalhes">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    <?php if (!empty($boleto['asaas_id'])): ?>
                                        <!-- Boleto já está no Asaas -->

                                        <!-- Abrir boleto do Asaas (link direto) -->
                                        <?php if (!empty($boleto['url_boleto'])): ?>
                                            <!-- URL do boleto já salva - link direto -->
                                            <a href="<?php echo htmlspecialchars($boleto['url_boleto']); ?>"
                                               target="_blank" class="text-red-600 hover:text-red-900" title="Abrir Boleto no Asaas (Link Direto)">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        <?php else: ?>
                                            <!-- URL não salva - buscar via ação -->
                                            <a href="boletos.php?acao=pdf_asaas&id=<?php echo $boleto['id']; ?>"
                                               target="_blank" class="text-orange-600 hover:text-orange-900" title="Buscar e Abrir Boleto no Asaas">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        <?php endif; ?>

                                        <!-- Sincronizar com Asaas -->
                                        <button onclick="sincronizarAsaas(<?php echo $boleto['id']; ?>)"
                                                class="text-purple-600 hover:text-purple-900" title="Sincronizar com Asaas">
                                            <i class="fas fa-sync"></i>
                                        </button>

                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                            <i class="fas fa-cloud mr-1"></i> Asaas
                                            <?php if (!empty($boleto['url_boleto'])): ?>
                                                <i class="fas fa-link ml-1" title="URL do boleto disponível"></i>
                                            <?php else: ?>
                                                <i class="fas fa-exclamation-triangle ml-1 text-yellow-600" title="URL do boleto não encontrada"></i>
                                            <?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <!-- Boleto local - pode enviar para Asaas -->
                                        <button onclick="enviarParaAsaas(<?php echo $boleto['id']; ?>)"
                                                class="text-indigo-600 hover:text-indigo-900" title="Enviar para Asaas">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </button>

                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                            <i class="fas fa-desktop mr-1"></i> Local
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($boleto['status'] === 'pendente'): ?>
                                        <button onclick="marcarComoPago(<?php echo $boleto['id']; ?>)"
                                                class="text-green-600 hover:text-green-900" title="Marcar como Pago">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button onclick="cancelarBoletoMelhorado(<?php echo $boleto['id']; ?>, '<?php echo addslashes(safe_html($boleto['numero_boleto'])); ?>', <?php echo !empty($boleto['asaas_id']) ? 'true' : 'false'; ?>)"
                                                class="text-red-600 hover:text-red-900" title="Cancelar Boleto">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<script>
function imprimirBoleto(id) {
    // Abrir janela para impressão do boleto
    window.open(`boletos.php?acao=imprimir&id=${id}`, '_blank', 'width=800,height=600');
}

function enviarParaAsaas(id) {
    if (!confirm('Deseja enviar este boleto para o Asaas?')) {
        return;
    }

    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch('boletos.php?acao=enviar_asaas', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `boleto_id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.sucesso) {
            alert(data.mensagem);
            location.reload();
        } else {
            alert('Erro: ' + data.erro);
        }
    })
    .catch(error => {
        alert('Erro na requisição: ' + error);
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

function sincronizarAsaas(id) {
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch('boletos.php?acao=sincronizar_asaas', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `boleto_id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.sucesso) {
            alert(data.mensagem);
            location.reload();
        } else {
            alert('Erro: ' + data.erro);
        }
    })
    .catch(error => {
        alert('Erro na requisição: ' + error);
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

function marcarComoPago(id) {
    const dataAtual = new Date().toISOString().split('T')[0];
    const dataPagamento = prompt('Data do pagamento (YYYY-MM-DD):', dataAtual);

    if (!dataPagamento) return;

    const valorPago = prompt('Valor pago (opcional):');

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `boletos.php?acao=marcar_pago&id=${id}`;

    const inputData = document.createElement('input');
    inputData.type = 'hidden';
    inputData.name = 'data_pagamento';
    inputData.value = dataPagamento;
    form.appendChild(inputData);

    if (valorPago) {
        const inputValor = document.createElement('input');
        inputValor.type = 'hidden';
        inputValor.name = 'valor_pago';
        inputValor.value = valorPago;
        form.appendChild(inputValor);
    }

    document.body.appendChild(form);
    form.submit();
}

function cancelarBoleto(id, numero) {
    if (!confirm(`Deseja cancelar o boleto ${numero}?`)) {
        return;
    }

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `boletos.php?acao=cancelar&id=${id}`;

    document.body.appendChild(form);
    form.submit();
}

function cancelarBoletoMelhorado(id, numero, temAsaas) {
    let mensagem = `Tem certeza que deseja cancelar o boleto ${numero}?`;

    if (temAsaas) {
        mensagem += '\n\n⚠️ Este boleto também será cancelado no Asaas automaticamente.';
    }

    if (!confirm(mensagem)) {
        return;
    }

    // Mostrar loading
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    // Criar formulário
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `boletos.php?acao=cancelar&id=${id}`;
    document.body.appendChild(form);
    form.submit();
}

// Auto-submit quando mudar filtros
document.addEventListener('DOMContentLoaded', function() {
    const filtros = document.querySelectorAll('.filtro-auto');
    filtros.forEach(filtro => {
        filtro.addEventListener('change', function() {
            document.getElementById('form-filtros').submit();
        });
    });
});
</script>
