<?php
/**
 * SISTEMA DE SINCRONIZAÇÃO AUTOMÁTICA
 * 
 * Este arquivo é incluído automaticamente em todas as páginas do módulo financeiro
 * e garante que os dados estejam sempre sincronizados sem intervenção manual.
 */

// Evitar execução múltipla
if (defined('AUTO_SYNC_LOADED')) {
    return;
}
define('AUTO_SYNC_LOADED', true);

class AutoSync {
    private $db;
    private $cache_time = 300; // 5 minutos
    private $force_sync = false;
    
    public function __construct($database) {
        $this->db = $database;
        $this->initializeAutoSync();
    }
    
    /**
     * Inicializa o sistema de sincronização automática
     */
    private function initializeAutoSync() {
        // Verificar se precisa sincronizar
        if ($this->needsSync()) {
            $this->performAutoSync();
        }
        
        // Registrar hooks para operações
        $this->registerHooks();
    }
    
    /**
     * Verifica se precisa sincronizar
     */
    private function needsSync() {
        try {
            // Verificar última sincronização
            $last_sync = $this->getLastSyncTime();
            $current_time = time();
            
            // Forçar sincronização se:
            // 1. Nunca foi sincronizado
            // 2. Passou do tempo de cache
            // 3. Há operações pendentes
            if (!$last_sync || 
                ($current_time - $last_sync) > $this->cache_time ||
                $this->hasPendingOperations()) {
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Erro ao verificar necessidade de sync: " . $e->getMessage());
            return true; // Em caso de erro, sincronizar por segurança
        }
    }
    
    /**
     * Executa sincronização automática
     */
    private function performAutoSync() {
        try {
            // Sincronizar contas a pagar
            $this->syncContasPagar();
            
            // Sincronizar contas a receber
            $this->syncContasReceber();
            
            // Sincronizar mensalidades (se existir)
            $this->syncMensalidades();
            
            // Sincronizar boletos (se existir)
            $this->syncBoletos();
            
            // Recalcular saldos bancários
            $this->recalcularSaldosBancarios();
            
            // Atualizar timestamp da última sincronização
            $this->updateLastSyncTime();
            
        } catch (Exception $e) {
            error_log("Erro na sincronização automática: " . $e->getMessage());
        }
    }
    
    /**
     * Sincroniza contas a pagar
     */
    private function syncContasPagar() {
        // Buscar contas pagas sem transação financeira
        $contas_dessinc = $this->db->fetchAll("
            SELECT cp.* FROM contas_pagar cp
            WHERE cp.status = 'pago'
            AND cp.data_pagamento IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM transacoes_financeiras tf
                WHERE tf.referencia_tipo = 'conta_pagar'
                AND tf.referencia_id = cp.id
            )
            LIMIT 50
        ");

        foreach ($contas_dessinc as $conta) {
            $this->createTransacaoFinanceira(
                'despesa',
                $conta['descricao'],
                $conta['valor_total'] ?: $conta['valor'], // Usar valor_total que inclui juros/multas
                $conta['data_pagamento'],
                $conta['conta_bancaria_id'] ?: 1,
                'conta_pagar',
                $conta['id']
            );

            $this->updateSaldoBancario(
                $conta['conta_bancaria_id'] ?: 1,
                -($conta['valor_total'] ?: $conta['valor'])
            );
        }
    }
    
    /**
     * Sincroniza contas a receber
     */
    private function syncContasReceber() {
        // Buscar contas recebidas sem transação financeira
        $contas_dessinc = $this->db->fetchAll("
            SELECT cr.* FROM contas_receber cr
            WHERE cr.status = 'recebido'
            AND cr.data_recebimento IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM transacoes_financeiras tf
                WHERE tf.referencia_tipo = 'conta_receber'
                AND tf.referencia_id = cr.id
            )
            LIMIT 50
        ");

        foreach ($contas_dessinc as $conta) {
            $this->createTransacaoFinanceira(
                'receita',
                $conta['descricao'],
                $conta['valor_total'] ?: $conta['valor'], // Usar valor_total que inclui juros/descontos
                $conta['data_recebimento'],
                $conta['conta_bancaria_id'] ?: 1,
                'conta_receber',
                $conta['id']
            );

            $this->updateSaldoBancario(
                $conta['conta_bancaria_id'] ?: 1,
                $conta['valor_total'] ?: $conta['valor']
            );
        }
    }
    
    /**
     * Sincroniza mensalidades
     */
    private function syncMensalidades() {
        // Verificar se tabela existe
        $tabela_existe = $this->db->fetchOne("SHOW TABLES LIKE 'mensalidades_alunos'");
        if (!$tabela_existe) return;
        
        // Buscar mensalidades pagas sem conta a receber
        $mensalidades_dessinc = $this->db->fetchAll("
            SELECT ma.* FROM mensalidades_alunos ma
            WHERE ma.status = 'pago' 
            AND ma.data_pagamento IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM contas_receber cr 
                WHERE cr.descricao LIKE CONCAT('%Mensalidade%', ma.id, '%')
            )
            LIMIT 30
        ");
        
        foreach ($mensalidades_dessinc as $mensalidade) {
            // Criar conta a receber
            $this->db->query("
                INSERT INTO contas_receber 
                (descricao, valor, data_vencimento, data_recebimento, status, 
                 cliente_nome, categoria_id, created_at)
                VALUES (?, ?, ?, ?, 'recebido', 'Aluno', 1, NOW())
            ", [
                "Mensalidade - {$mensalidade['descricao']} (ID: {$mensalidade['id']})",
                $mensalidade['valor'],
                $mensalidade['data_vencimento'],
                $mensalidade['data_pagamento']
            ]);
            
            $conta_receber_id = $this->db->lastInsertId();
            
            // Criar transação financeira
            $this->createTransacaoFinanceira(
                'receita',
                "Mensalidade - {$mensalidade['descricao']}",
                $mensalidade['valor_pago'] ?: $mensalidade['valor'],
                $mensalidade['data_pagamento'],
                1,
                'conta_receber',
                $conta_receber_id
            );
            
            $this->updateSaldoBancario(1, $mensalidade['valor_pago'] ?: $mensalidade['valor']);
        }
    }
    
    /**
     * Sincroniza boletos
     */
    private function syncBoletos() {
        // Verificar se tabela existe
        $tabela_existe = $this->db->fetchOne("SHOW TABLES LIKE 'polos_boletos'");
        if (!$tabela_existe) return;
        
        // Buscar boletos pagos sem conta a receber
        $boletos_dessinc = $this->db->fetchAll("
            SELECT pb.* FROM polos_boletos pb
            WHERE pb.status = 'pago' 
            AND pb.data_pagamento IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM contas_receber cr 
                WHERE cr.descricao LIKE CONCAT('%', pb.numero_boleto, '%')
            )
            LIMIT 30
        ");
        
        foreach ($boletos_dessinc as $boleto) {
            // Criar conta a receber
            $this->db->query("
                INSERT INTO contas_receber
                (descricao, valor, data_vencimento, data_recebimento, status,
                 cliente_nome, categoria_id, conta_bancaria_id, usuario_id, created_at)
                VALUES (?, ?, ?, ?, 'recebido', ?, 1, 1, 1, NOW())
            ", [
                "Boleto {$boleto['numero_boleto']} - {$boleto['descricao']}",
                $boleto['valor_pago'] ?: $boleto['valor'],
                $boleto['data_vencimento'],
                $boleto['data_pagamento'],
                $boleto['nome_pagador'] ?: 'Cliente'
            ]);

            $conta_receber_id = $this->db->lastInsertId();

            // Criar transação financeira
            $this->createTransacaoFinanceira(
                'receita',
                "Recebimento Boleto {$boleto['numero_boleto']}",
                $boleto['valor_pago'] ?: $boleto['valor'],
                $boleto['data_pagamento'],
                1,
                'conta_receber',
                $conta_receber_id
            );

            $this->updateSaldoBancario(1, $boleto['valor_pago'] ?: $boleto['valor']);
        }
    }
    
    /**
     * Cria transação financeira
     */
    private function createTransacaoFinanceira($tipo, $descricao, $valor, $data, $conta_bancaria_id, $ref_tipo, $ref_id) {
        try {
            // Verificar se já existe para evitar duplicatas
            $existe = $this->db->fetchOne("
                SELECT id FROM transacoes_financeiras
                WHERE referencia_tipo = ? AND referencia_id = ?
            ", [$ref_tipo, $ref_id]);

            if (!$existe) {
                $this->db->query("
                    INSERT INTO transacoes_financeiras
                    (tipo, descricao, valor, data_transacao, conta_bancaria_id,
                     categoria_id, referencia_tipo, referencia_id, usuario_id, status, created_at)
                    VALUES (?, ?, ?, ?, ?, 1, ?, ?, 1, 'efetivada', NOW())
                ", [$tipo, $descricao, $valor, $data, $conta_bancaria_id, $ref_tipo, $ref_id]);
            }

        } catch (Exception $e) {
            error_log("Erro ao criar transação financeira: " . $e->getMessage());
        }
    }
    
    /**
     * Atualiza saldo bancário
     */
    private function updateSaldoBancario($conta_id, $valor) {
        try {
            $this->db->query("
                UPDATE contas_bancarias 
                SET saldo_atual = saldo_atual + ?,
                    data_ultima_sincronizacao = NOW()
                WHERE id = ?
            ", [$valor, $conta_id]);
            
        } catch (Exception $e) {
            error_log("Erro ao atualizar saldo bancário: " . $e->getMessage());
        }
    }
    
    /**
     * Recalcula saldos bancários
     */
    private function recalcularSaldosBancarios() {
        try {
            $contas = $this->db->fetchAll("SELECT id, saldo_inicial FROM contas_bancarias WHERE status = 'ativo' OR status IS NULL");
            
            foreach ($contas as $conta) {
                $saldo_transacoes = $this->db->fetchOne("
                    SELECT COALESCE(
                        SUM(CASE WHEN tipo = 'receita' THEN valor ELSE -valor END), 0
                    ) as saldo
                    FROM transacoes_financeiras 
                    WHERE conta_bancaria_id = ? AND status = 'efetivada'
                ", [$conta['id']]);
                
                $saldo_final = ($conta['saldo_inicial'] ?: 0) + $saldo_transacoes['saldo'];
                
                $this->db->query("
                    UPDATE contas_bancarias 
                    SET saldo_atual = ?,
                        data_ultima_sincronizacao = NOW()
                    WHERE id = ?
                ", [$saldo_final, $conta['id']]);
            }
            
        } catch (Exception $e) {
            error_log("Erro ao recalcular saldos bancários: " . $e->getMessage());
        }
    }
    
    /**
     * Registra hooks para operações
     */
    private function registerHooks() {
        // Hook para quando uma página é carregada
        register_shutdown_function([$this, 'onPageShutdown']);
        
        // Hook para operações POST
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->onPostOperation();
        }
    }
    
    /**
     * Executado quando a página termina de carregar
     */
    public function onPageShutdown() {
        // Verificar se houve mudanças que precisam de sincronização
        if ($this->hasRecentChanges()) {
            $this->performQuickSync();
        }
    }
    
    /**
     * Executado em operações POST
     */
    private function onPostOperation() {
        // Forçar sincronização após operações POST
        $this->force_sync = true;
    }
    
    /**
     * Verifica se há operações pendentes
     */
    private function hasPendingOperations() {
        try {
            // Verificar contas pagas sem transação
            $pending_pagar = $this->db->fetchOne("
                SELECT COUNT(*) as total FROM contas_pagar cp
                WHERE cp.status = 'pago' 
                AND cp.data_pagamento IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM transacoes_financeiras tf 
                    WHERE tf.referencia_tipo = 'conta_pagar' 
                    AND tf.referencia_id = cp.id
                )
            ");
            
            // Verificar contas recebidas sem transação
            $pending_receber = $this->db->fetchOne("
                SELECT COUNT(*) as total FROM contas_receber cr
                WHERE cr.status = 'recebido' 
                AND cr.data_recebimento IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM transacoes_financeiras tf 
                    WHERE tf.referencia_tipo = 'conta_receber' 
                    AND tf.referencia_id = cr.id
                )
            ");
            
            return ($pending_pagar['total'] > 0 || $pending_receber['total'] > 0);
            
        } catch (Exception $e) {
            return true; // Em caso de erro, assumir que há pendências
        }
    }
    
    /**
     * Verifica se há mudanças recentes
     */
    private function hasRecentChanges() {
        try {
            $recent_changes = $this->db->fetchOne("
                SELECT COUNT(*) as total FROM (
                    SELECT updated_at FROM contas_pagar WHERE updated_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                    UNION ALL
                    SELECT updated_at FROM contas_receber WHERE updated_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                ) as changes
            ");
            
            return $recent_changes['total'] > 0;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Sincronização rápida
     */
    private function performQuickSync() {
        try {
            // Sincronizar apenas registros mais recentes
            $this->syncContasPagar();
            $this->syncContasReceber();
            
        } catch (Exception $e) {
            error_log("Erro na sincronização rápida: " . $e->getMessage());
        }
    }
    
    /**
     * Obtém timestamp da última sincronização
     */
    private function getLastSyncTime() {
        try {
            $result = $this->db->fetchOne("
                SELECT UNIX_TIMESTAMP(MAX(data_ultima_sincronizacao)) as last_sync 
                FROM contas_bancarias 
                WHERE data_ultima_sincronizacao IS NOT NULL
            ");
            
            return $result['last_sync'] ?: 0;
            
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * Atualiza timestamp da última sincronização
     */
    private function updateLastSyncTime() {
        try {
            $this->db->query("
                UPDATE contas_bancarias 
                SET data_ultima_sincronizacao = NOW() 
                WHERE id = 1
            ");
            
        } catch (Exception $e) {
            error_log("Erro ao atualizar timestamp de sincronização: " . $e->getMessage());
        }
    }
}

// Inicializar sincronização automática se o banco estiver disponível
if (isset($db) && $db) {
    try {
        $auto_sync = new AutoSync($db);
    } catch (Exception $e) {
        error_log("Erro ao inicializar sincronização automática: " . $e->getMessage());
    }
}
?>
