<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-plus text-blue-500 mr-2"></i>
                Gerar Novo Boleto
            </h3>
            <a href="boletos.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <form method="POST" class="p-6" onsubmit="return validarFormulario()">
        <input type="hidden" name="acao" value="gerar">

        <!-- Tipo de Boleto -->
        <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Tipo de Boleto</h4>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_entidade" value="polo" class="mr-3"
                           onchange="document.getElementById('campo-polo').style.display='block'; document.getElementById('campo-aluno').style.display='none'; document.getElementById('campo-mensalidade').style.display='none'; document.getElementById('campos-avulso').style.display='none';" checked>
                    <div>
                        <div class="font-medium text-gray-900">Boleto para Polo</div>
                        <div class="text-sm text-gray-500">Gerar boleto para polo de ensino</div>
                    </div>
                </label>

                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_entidade" value="aluno" class="mr-3"
                           onchange="document.getElementById('campo-polo').style.display='none'; document.getElementById('campo-aluno').style.display='block'; document.getElementById('campo-mensalidade').style.display='block'; document.getElementById('campos-avulso').style.display='none';">
                    <div>
                        <div class="font-medium text-gray-900">Boleto para Aluno</div>
                        <div class="text-sm text-gray-500">Gerar boleto para mensalidade ou taxa de aluno</div>
                    </div>
                </label>

                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_entidade" value="avulso" class="mr-3"
                           onchange="document.getElementById('campo-polo').style.display='none'; document.getElementById('campo-aluno').style.display='none'; document.getElementById('campo-mensalidade').style.display='none'; document.getElementById('campos-avulso').style.display='block';">
                    <div>
                        <div class="font-medium text-gray-900">Boleto Avulso</div>
                        <div class="text-sm text-gray-500">Gerar boleto para pessoa física ou jurídica</div>
                    </div>
                </label>
            </div>

            <!-- Botões de teste direto -->
            <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p class="text-sm text-yellow-800 mb-2">Teste direto (se os radio buttons não funcionarem):</p>
                <button type="button" onclick="document.getElementById('campo-polo').style.display='block'; document.getElementById('campo-aluno').style.display='none'; document.getElementById('campo-mensalidade').style.display='none'; document.getElementById('campos-avulso').style.display='none';" class="bg-blue-500 text-white px-3 py-1 rounded mr-2">Polo</button>
                <button type="button" onclick="document.getElementById('campo-polo').style.display='none'; document.getElementById('campo-aluno').style.display='block'; document.getElementById('campo-mensalidade').style.display='block'; document.getElementById('campos-avulso').style.display='none';" class="bg-green-500 text-white px-3 py-1 rounded mr-2">Aluno</button>
                <button type="button" onclick="document.getElementById('campo-polo').style.display='none'; document.getElementById('campo-aluno').style.display='none'; document.getElementById('campo-mensalidade').style.display='none'; document.getElementById('campos-avulso').style.display='block';" class="bg-red-500 text-white px-3 py-1 rounded">Avulso</button>
            </div>
        </div>

        <!-- Seleção de Polo (visível quando tipo = polo) -->
        <div id="campo-polo" class="mb-6">
                <label for="polo_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Polo <span class="text-red-500">*</span>
                </label>
                <select name="polo_id" id="polo_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="preencherDadosPolo()">
                    <option value="">Selecione um polo...</option>
                    <?php if (isset($polos)): ?>
                        <?php foreach ($polos as $polo): ?>
                            <option value="<?php echo $polo['id']; ?>"
                                    data-nome="<?php echo htmlspecialchars($polo['nome']); ?>"
                                    data-email="<?php echo htmlspecialchars($polo['email'] ?? ''); ?>"
                                    data-telefone="<?php echo htmlspecialchars($polo['telefone'] ?? ''); ?>"
                                    data-cnpj="<?php echo htmlspecialchars($polo['cnpj'] ?? ''); ?>"
                                    data-responsavel="<?php echo htmlspecialchars($polo['responsavel'] ?? ''); ?>">
                                <?php echo htmlspecialchars($polo['nome']); ?>
                                <?php if ($polo['responsavel']): ?>
                                    - <?php echo htmlspecialchars($polo['responsavel']); ?>
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
        </div>

        <!-- Seleção de Aluno (visível quando tipo = aluno) -->
        <div id="campo-aluno" class="mb-6" style="display: none;">
                <label for="entidade_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Aluno <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="text" id="busca_aluno" placeholder="Digite o nome do aluno para buscar..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           onkeyup="buscarAlunos(this.value)" autocomplete="off">
                    <div id="lista_alunos" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                        <!-- Resultados da busca aparecerão aqui -->
                    </div>
                    <input type="hidden" id="entidade_id" name="entidade_id" required>
                </div>
                <p class="text-xs text-gray-500 mt-1">Digite pelo menos 3 caracteres para buscar</p>
        </div>

        <!-- Mensalidade (visível quando tipo = aluno) -->
        <div id="campo-mensalidade" class="mb-6" style="display: none;">
                <label for="mensalidade_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Mensalidade (Opcional)
                </label>
                <select id="mensalidade_id" name="mensalidade_id"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        onchange="preencherValorMensalidade()">
                    <option value="">Selecione uma mensalidade pendente</option>
                </select>
                <p class="text-xs text-gray-500 mt-1">Se não selecionar, será um boleto avulso para o aluno</p>
        </div>

        <!-- Campos para Boleto Avulso (visível quando tipo = avulso) -->
        <div id="campos-avulso" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg" style="display: none;">
            <h4 class="text-md font-semibold text-green-800 mb-4">
                <i class="fas fa-file-invoice-dollar text-green-600 mr-2"></i>
                Boleto Avulso - Preenchimento Simples
            </h4>
            <p class="text-sm text-green-700 mb-4">
                Preencha apenas os dados básicos. Os campos abaixo serão automaticamente copiados para os dados do cliente.
            </p>
        </div>

        <!-- Dados do Cliente (sempre visível) -->
        <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 class="text-md font-semibold text-gray-800 mb-4">
                <i class="fas fa-user text-blue-500 mr-2"></i>
                <span id="titulo-dados-cliente">Dados do Cliente</span>
            </h4>
            <p class="text-sm text-gray-600 mb-4" id="descricao-dados-cliente">
                Estes dados serão usados para criar o cliente no Asaas e gerar o boleto.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="cliente_nome" class="block text-sm font-medium text-gray-700 mb-2">
                        Nome Completo <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="cliente_nome" name="cliente_nome" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Nome completo do cliente">
                </div>
                <div>
                    <label for="cliente_cpf_cnpj" class="block text-sm font-medium text-gray-700 mb-2">
                        CPF/CNPJ <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="cliente_cpf_cnpj" name="cliente_cpf_cnpj" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="000.000.000-00" onkeyup="formatarCpfCnpj(this)">
                </div>
                <div>
                    <label for="cliente_email" class="block text-sm font-medium text-gray-700 mb-2">
                        E-mail <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="cliente_email" name="cliente_email" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="<EMAIL>">
                </div>
                <div>
                    <label for="cliente_telefone" class="block text-sm font-medium text-gray-700 mb-2">
                        Telefone <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="cliente_telefone" name="cliente_telefone" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="(00) 00000-0000" onkeyup="formatarTelefone(this)">
                </div>
            </div>
        </div>

        <!-- Dados do Boleto -->
        <div class="mt-6">
            <h4 class="text-md font-semibold text-gray-800 mb-4">
                <i class="fas fa-file-invoice text-green-500 mr-2"></i>
                Dados do Boleto
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

            <!-- Valor -->
            <div>
                <label for="valor" class="block text-sm font-medium text-gray-700 mb-2">
                    Valor <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-2 text-gray-500">R$</span>
                    <input type="text" id="valor" name="valor" required
                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0,00"
                           onkeyup="formatarMoeda(this)">
                </div>
            </div>

            <!-- Data de Vencimento -->
            <div>
                <label for="data_vencimento" class="block text-sm font-medium text-gray-700 mb-2">
                    Data de Vencimento <span class="text-red-500">*</span>
                </label>
                <input type="date" id="data_vencimento" name="data_vencimento" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Descrição -->
            <div class="md:col-span-2">
                <label for="descricao" class="block text-sm font-medium text-gray-700 mb-2">
                    Descrição <span class="text-red-500">*</span>
                </label>
                <input type="text" id="descricao" name="descricao" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Descrição do boleto (ex: Mensalidade Janeiro 2025)">
            </div>

            <!-- Observações -->
            <div class="md:col-span-2">
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações
                </label>
                <textarea id="observacoes" name="observacoes" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Informações adicionais sobre o boleto..."></textarea>
            </div>

            <!-- Opção Asaas -->
            <div class="md:col-span-2">
                <div class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg">
                    <input type="checkbox" id="gerar_asaas" name="gerar_asaas" value="1" class="mr-3" checked>
                    <div>
                        <label for="gerar_asaas" class="font-medium text-green-800 cursor-pointer">
                            Gerar boleto via Asaas
                        </label>
                        <p class="text-sm text-green-600">
                            Recomendado: O boleto será criado automaticamente no Asaas e ficará disponível para download
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações Bancárias -->
        <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 class="text-md font-semibold text-blue-800 mb-2">Informações Bancárias</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
                <div>
                    <span class="font-medium">Banco:</span> Itaú (341)
                </div>
                <div>
                    <span class="font-medium">Agência:</span> 1234
                </div>
                <div>
                    <span class="font-medium">Conta:</span> 12345-6
                </div>
            </div>
            <p class="text-xs text-blue-600 mt-2">
                <i class="fas fa-info-circle mr-1"></i>
                Estas informações são configuradas nas configurações do sistema
            </p>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="boletos.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <div class="flex items-center space-x-4">
                <button type="button" onclick="limparFormulario()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-eraser mr-2"></i>
                    Limpar
                </button>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-barcode mr-2"></i>
                    Gerar Boleto
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Informações sobre Boletos -->
<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Informações Importantes</h3>
            <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>O boleto será gerado com as informações bancárias configuradas no sistema</li>
                    <li>Para boletos de mensalidade, o pagamento será automaticamente vinculado</li>
                    <li>Boletos vencidos podem ser pagos normalmente</li>
                    <li>O código de barras e linha digitável são gerados automaticamente</li>
                    <li>Após a geração, o boleto pode ser impresso ou enviado por email</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
console.log('🚀 [DEBUG] JavaScript iniciado - Página de geração de boletos');

// Dados das mensalidades para JavaScript
const mensalidadesData = <?php echo json_encode($mensalidades); ?>;
console.log('📊 [DEBUG] Dados de mensalidades carregados:', mensalidadesData);

let timeoutBusca = null;
console.log('⏰ [DEBUG] Variável timeoutBusca inicializada');

// Inicialização da página
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 [DEBUG] DOM carregado - Inicializando página');

    // Pré-preencher data de vencimento com 30 dias a partir de hoje
    const dataVencimento = document.getElementById('data_vencimento');
    if (dataVencimento && !dataVencimento.value) {
        const hoje = new Date();
        hoje.setDate(hoje.getDate() + 30); // 30 dias a partir de hoje
        const dataFormatada = hoje.toISOString().split('T')[0]; // YYYY-MM-DD
        dataVencimento.value = dataFormatada;
        console.log('📅 [DEBUG] Data de vencimento pré-preenchida:', dataFormatada);
    }

    // Definir valor mínimo para data de vencimento (hoje)
    if (dataVencimento) {
        const hoje = new Date().toISOString().split('T')[0];
        dataVencimento.min = hoje;
    }
});

// Função para alternar tipos de boleto
function alterarTipoSimples(tipo) {
    console.log('🔄 [DEBUG] Alterando tipo para:', tipo);

    // Buscar elementos
    var polo = document.getElementById('campo-polo');
    var aluno = document.getElementById('campo-aluno');
    var mensalidade = document.getElementById('campo-mensalidade');
    var avulso = document.getElementById('campos-avulso');

    // Ocultar todos
    if (polo) polo.style.display = 'none';
    if (aluno) aluno.style.display = 'none';
    if (mensalidade) mensalidade.style.display = 'none';
    if (avulso) avulso.style.display = 'none';

    // Atualizar títulos e descrições
    const tituloCliente = document.getElementById('titulo-dados-cliente');
    const descricaoCliente = document.getElementById('descricao-dados-cliente');

    // Mostrar conforme tipo e atualizar interface
    if (tipo === 'polo') {
        if (polo) polo.style.display = 'block';
        if (tituloCliente) tituloCliente.textContent = 'Dados do Polo (Cliente)';
        if (descricaoCliente) descricaoCliente.textContent = 'Selecione um polo acima. Os dados serão preenchidos automaticamente.';

    } else if (tipo === 'aluno') {
        if (aluno) aluno.style.display = 'block';
        if (mensalidade) mensalidade.style.display = 'block';
        if (tituloCliente) tituloCliente.textContent = 'Dados do Aluno (Cliente)';
        if (descricaoCliente) descricaoCliente.textContent = 'Busque um aluno acima. Os dados serão preenchidos automaticamente.';

    } else if (tipo === 'avulso') {
        if (avulso) avulso.style.display = 'block';
        if (tituloCliente) tituloCliente.textContent = 'Dados do Cliente';
        if (descricaoCliente) descricaoCliente.textContent = 'Para boleto avulso, preencha os dados do cliente que irá pagar o boleto.';

        // Limpar campos para permitir preenchimento manual
        limparCamposCliente();

        // Focar no primeiro campo
        setTimeout(() => {
            const primeiroCampo = document.getElementById('cliente_nome');
            if (primeiroCampo) primeiroCampo.focus();
        }, 100);
    }

    // Limpar outros campos quando mudar tipo
    limparCamposEspecificos(tipo);
}

// Função para limpar campos específicos baseado no tipo
function limparCamposEspecificos(tipoAtual) {
    // Limpar busca de alunos se não for tipo aluno
    if (tipoAtual !== 'aluno') {
        const buscaAluno = document.getElementById('busca_aluno');
        if (buscaAluno) buscaAluno.value = '';
        const listaAlunos = document.getElementById('lista_alunos');
        if (listaAlunos) listaAlunos.classList.add('hidden');
        const mensalidadeSelect = document.getElementById('mensalidade_id');
        if (mensalidadeSelect) mensalidadeSelect.innerHTML = '<option value="">Selecione uma mensalidade pendente</option>';
    }

    // Limpar seleção de polo se não for tipo polo
    if (tipoAtual !== 'polo') {
        const poloSelect = document.getElementById('polo_id');
        if (poloSelect) poloSelect.value = '';
    }

    // Para boleto avulso, limpar campos do cliente para permitir preenchimento manual
    if (tipoAtual === 'avulso') {
        limparCamposCliente();
    }
}

function preencherDadosPolo() {
    const poloSelect = document.getElementById('polo_id');
    const selectedOption = poloSelect.options[poloSelect.selectedIndex];

    if (selectedOption.value) {
        // Preencher dados do cliente com dados do polo
        document.getElementById('cliente_nome').value = selectedOption.dataset.responsavel || selectedOption.dataset.nome;
        document.getElementById('cliente_cpf_cnpj').value = selectedOption.dataset.cnpj || '';
        document.getElementById('cliente_email').value = selectedOption.dataset.email || '';
        document.getElementById('cliente_telefone').value = selectedOption.dataset.telefone || '';

        // Preencher descrição padrão
        document.getElementById('descricao').value = `Boleto - ${selectedOption.dataset.nome}`;
    } else {
        limparCamposCliente();
    }
}

function preencherDadosAluno(aluno) {
    // Preencher dados do cliente com dados do aluno
    document.getElementById('cliente_nome').value = aluno.nome || '';
    document.getElementById('cliente_cpf_cnpj').value = aluno.cpf || '';
    document.getElementById('cliente_email').value = aluno.email || '';
    document.getElementById('cliente_telefone').value = aluno.telefone || '';

    // Preencher descrição padrão
    document.getElementById('descricao').value = `Boleto - ${aluno.nome}`;
}

function limparCamposCliente() {
    console.log('🧹 [DEBUG] Limpando campos do cliente');

    const campos = ['cliente_nome', 'cliente_cpf_cnpj', 'cliente_email', 'cliente_telefone'];

    campos.forEach(campoId => {
        const campo = document.getElementById(campoId);
        if (campo) {
            campo.value = '';
        } else {
            console.warn(`⚠️ [DEBUG] Campo ${campoId} não encontrado`);
        }
    });

    // Limpar também a descrição se estiver preenchida automaticamente
    const descricao = document.getElementById('descricao');
    if (descricao && descricao.value.startsWith('Boleto - ')) {
        descricao.value = '';
    }
}

function sincronizarCamposAvulso() {
    // Sincronizar nome
    const nomePagador = document.getElementById('nome_pagador');
    const clienteNome = document.getElementById('cliente_nome');

    if (nomePagador && clienteNome) {
        nomePagador.addEventListener('input', function() {
            clienteNome.value = this.value;
        });
    }

    // Sincronizar CPF/CNPJ
    const cpfPagador = document.getElementById('cpf_pagador');
    const clienteCpf = document.getElementById('cliente_cpf_cnpj');

    if (cpfPagador && clienteCpf) {
        cpfPagador.addEventListener('input', function() {
            clienteCpf.value = this.value;
        });
    }

    // Sincronizar email
    const emailPagador = document.getElementById('email_pagador');
    const clienteEmail = document.getElementById('cliente_email');

    if (emailPagador && clienteEmail) {
        emailPagador.addEventListener('input', function() {
            clienteEmail.value = this.value;
        });
    }

    // Sincronizar telefone
    const telefonePagador = document.getElementById('telefone_pagador');
    const clienteTelefone = document.getElementById('cliente_telefone');

    if (telefonePagador && clienteTelefone) {
        telefonePagador.addEventListener('input', function() {
            clienteTelefone.value = this.value;
        });
    }
}

function carregarMensalidades() {
    const alunoId = document.getElementById('entidade_id').value;
    const mensalidadeSelect = document.getElementById('mensalidade_id');
    
    // Limpar opções
    mensalidadeSelect.innerHTML = '<option value="">Selecione uma mensalidade pendente</option>';
    
    if (alunoId) {
        // Filtrar mensalidades do aluno selecionado
        const mensalidadesAluno = mensalidadesData.filter(m => m.aluno_nome === document.querySelector(`#entidade_id option[value="${alunoId}"]`).dataset.nome);
        
        mensalidadesAluno.forEach(mensalidade => {
            const option = document.createElement('option');
            option.value = mensalidade.id;
            option.textContent = `${mensalidade.descricao} - R$ ${parseFloat(mensalidade.valor).toFixed(2).replace('.', ',')}`;
            option.dataset.valor = mensalidade.valor;
            mensalidadeSelect.appendChild(option);
        });
    }
}

function preencherValorMensalidade() {
    const mensalidadeSelect = document.getElementById('mensalidade_id');
    const valorInput = document.getElementById('valor');

    if (mensalidadeSelect.value) {
        const selectedOption = mensalidadeSelect.options[mensalidadeSelect.selectedIndex];
        const valor = parseFloat(selectedOption.dataset.valor);
        valorInput.value = valor.toFixed(2).replace('.', ',');
    }
}

function buscarAlunos(termo) {
    console.log('🔍 [DEBUG] Função buscarAlunos chamada com termo:', termo);

    // Verificar se elementos existem
    const listaAlunos = document.getElementById('lista_alunos');
    if (!listaAlunos) {
        console.error('❌ [ERROR] Elemento lista_alunos não encontrado!');
        alert('Erro: Elemento lista_alunos não encontrado na página!');
        return;
    }
    console.log('✅ [DEBUG] Elemento lista_alunos encontrado');

    clearTimeout(timeoutBusca);

    if (termo.length < 3) {
        listaAlunos.classList.add('hidden');
        console.log('⚠️ [DEBUG] Termo muito curto, ocultando lista');
        return;
    }

    console.log('🚀 [DEBUG] Iniciando timeout para busca...');
    timeoutBusca = setTimeout(() => {
        console.log('⏰ [DEBUG] Timeout executado, iniciando busca AJAX...');
        const url = `boletos.php?acao=buscar_alunos&termo=${encodeURIComponent(termo)}`;
        console.log('🌐 [DEBUG] URL da busca:', url);

        // Fazer busca via AJAX
        fetch(url)
            .then(response => {
                console.log('📡 [DEBUG] Resposta recebida - Status:', response.status);
                console.log('📡 [DEBUG] Resposta OK:', response.ok);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(alunos => {
                console.log('📊 [DEBUG] Dados recebidos:', alunos);
                console.log('📊 [DEBUG] Tipo dos dados:', typeof alunos);
                console.log('📊 [DEBUG] É array:', Array.isArray(alunos));

                listaAlunos.innerHTML = '';

                if (alunos.error) {
                    console.log('❌ [DEBUG] Erro retornado pelo servidor:', alunos.error);
                    listaAlunos.innerHTML = `<div class="p-3 text-red-500 text-sm">Erro: ${alunos.error}</div>`;
                } else if (alunos.length === 0) {
                    console.log('⚠️ [DEBUG] Nenhum aluno encontrado');
                    listaAlunos.innerHTML = '<div class="p-3 text-gray-500 text-sm">Nenhum aluno encontrado</div>';
                } else {
                    console.log(`✅ [DEBUG] ${alunos.length} alunos encontrados, criando elementos...`);
                    alunos.forEach((aluno, index) => {
                        console.log(`👤 [DEBUG] Processando aluno ${index + 1}:`, aluno);
                        const div = document.createElement('div');
                        div.className = 'p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100';
                        div.innerHTML = `<strong>${aluno.nome}</strong><br><small class="text-gray-500">${aluno.cpf}</small>`;
                        div.onclick = () => selecionarAluno(aluno.id, aluno.nome);
                        listaAlunos.appendChild(div);
                    });
                    console.log('✅ [DEBUG] Todos os elementos criados');
                }

                listaAlunos.classList.remove('hidden');
                console.log('👁️ [DEBUG] Lista exibida (classe hidden removida)');
            })
            .catch(error => {
                console.error('💥 [ERROR] Erro na busca:', error);
                console.error('💥 [ERROR] Stack trace:', error.stack);
                listaAlunos.innerHTML = `<div class="p-3 text-red-500 text-sm">Erro na busca: ${error.message}</div>`;
                listaAlunos.classList.remove('hidden');
            });
    }, 300);

    console.log('⏳ [DEBUG] Timeout configurado para 300ms');
}

function selecionarAluno(id, nome) {
    console.log('👤 [DEBUG] Selecionando aluno:', { id, nome });

    // Verificar se elementos existem
    const buscaAluno = document.getElementById('busca_aluno');
    const entidadeId = document.getElementById('entidade_id');
    const listaAlunos = document.getElementById('lista_alunos');

    if (!buscaAluno) {
        console.error('❌ [ERROR] Elemento busca_aluno não encontrado!');
        return;
    }
    if (!entidadeId) {
        console.error('❌ [ERROR] Elemento entidade_id não encontrado!');
        return;
    }
    if (!listaAlunos) {
        console.error('❌ [ERROR] Elemento lista_alunos não encontrado!');
        return;
    }

    buscaAluno.value = nome;
    entidadeId.value = id;
    listaAlunos.classList.add('hidden');

    console.log('✅ [DEBUG] Aluno selecionado com sucesso');
    console.log('✅ [DEBUG] Campo busca_aluno preenchido com:', nome);
    console.log('✅ [DEBUG] Campo entidade_id preenchido com:', id);
    console.log('✅ [DEBUG] Lista ocultada');

    carregarMensalidades();

    // Simular dados do aluno para preenchimento
    const alunoSimulado = {
        nome: nome,
        cpf: '000.000.000-00', // Em produção, buscar do banco
        email: '<EMAIL>', // Em produção, buscar do banco
        telefone: '(11) 99999-9999' // Em produção, buscar do banco
    };

    preencherDadosAluno(alunoSimulado);
}

function formatarMoeda(input) {
    let value = input.value.replace(/\D/g, '');
    value = (value / 100).toFixed(2);
    value = value.replace('.', ',');
    value = value.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    input.value = value;
}

function formatarCpfCnpj(input) {
    let value = input.value.replace(/\D/g, '');

    if (value.length <= 11) {
        // CPF: 000.000.000-00
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
    } else {
        // CNPJ: 00.000.000/0000-00
        value = value.replace(/^(\d{2})(\d)/, '$1.$2');
        value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
        value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
        value = value.replace(/(\d{4})(\d)/, '$1-$2');
    }

    input.value = value;
}

function formatarTelefone(input) {
    let value = input.value.replace(/\D/g, '');

    if (value.length <= 10) {
        // Telefone fixo: (00) 0000-0000
        value = value.replace(/^(\d{2})(\d)/, '($1) $2');
        value = value.replace(/(\d{4})(\d)/, '$1-$2');
    } else {
        // Celular: (00) 00000-0000
        value = value.replace(/^(\d{2})(\d)/, '($1) $2');
        value = value.replace(/(\d{5})(\d)/, '$1-$2');
    }

    input.value = value;
}

function limparFormulario() {
    if (confirm('Tem certeza que deseja limpar todos os campos?')) {
        document.querySelector('input[name="tipo_entidade"][value="aluno"]').checked = true;
        alterarTipo('aluno');
        document.getElementById('entidade_id').value = '';
        document.getElementById('mensalidade_id').innerHTML = '<option value="">Selecione uma mensalidade pendente</option>';
        document.getElementById('valor').value = '';
        document.getElementById('data_vencimento').value = '';
        document.getElementById('observacoes').value = '';
    }
}

// Fechar lista ao clicar fora
document.addEventListener('click', function(event) {
    const listaAlunos = document.getElementById('lista_alunos');
    const buscaAluno = document.getElementById('busca_aluno');

    if (listaAlunos && buscaAluno && !listaAlunos.contains(event.target) && event.target !== buscaAluno) {
        listaAlunos.classList.add('hidden');
    }
});

// Validação do formulário antes do envio
function validarFormulario() {
    console.log('Validando formulário...');

    const tipoEntidade = document.querySelector('input[name="tipo_entidade"]:checked');
    if (!tipoEntidade) {
        alert('Selecione o tipo de boleto');
        return false;
    }

    const valor = document.getElementById('valor').value;
    if (!valor || parseFloat(valor.replace(',', '.')) <= 0) {
        alert('Informe um valor válido maior que zero');
        document.getElementById('valor').focus();
        return false;
    }

    const dataVencimento = document.getElementById('data_vencimento').value;
    if (!dataVencimento) {
        alert('Informe a data de vencimento');
        document.getElementById('data_vencimento').focus();
        return false;
    }

    // Validações específicas por tipo
    if (tipoEntidade.value === 'aluno') {
        const entidadeId = document.getElementById('entidade_id').value;
        if (!entidadeId) {
            alert('Selecione um aluno');
            document.getElementById('busca_aluno').focus();
            return false;
        }
    } else if (tipoEntidade.value === 'polo') {
        const poloId = document.getElementById('polo_id').value;
        if (!poloId) {
            alert('Selecione um polo');
            document.getElementById('polo_id').focus();
            return false;
        }
    } else if (tipoEntidade.value === 'avulso') {
        const nomePagador = document.getElementById('nome_pagador').value;
        if (!nomePagador) {
            alert('Informe o nome do pagador');
            document.getElementById('nome_pagador').focus();
            return false;
        }
    }

    console.log('Formulário válido, enviando...');
    return true;
}

// Definir data de vencimento padrão (30 dias) e inicializar tipo
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 [DEBUG] DOM carregado, inicializando página...');

    // Verificar se elementos críticos existem
    const elementos = {
        'busca_aluno': document.getElementById('busca_aluno'),
        'lista_alunos': document.getElementById('lista_alunos'),
        'entidade_id': document.getElementById('entidade_id'),
        'data_vencimento': document.getElementById('data_vencimento'),
        'campo-aluno': document.getElementById('campo-aluno'),
        'campo-polo': document.getElementById('campo-polo')
    };

    console.log('🔍 [DEBUG] Verificando elementos da página:');
    for (const [nome, elemento] of Object.entries(elementos)) {
        if (elemento) {
            console.log(`✅ [DEBUG] ${nome}: encontrado`);
        } else {
            console.error(`❌ [ERROR] ${nome}: NÃO encontrado!`);
        }
    }

    // Configurar data de vencimento padrão
    if (elementos['data_vencimento']) {
        const hoje = new Date();
        hoje.setDate(hoje.getDate() + 30);
        elementos['data_vencimento'].value = hoje.toISOString().split('T')[0];
        console.log('📅 [DEBUG] Data de vencimento configurada:', elementos['data_vencimento'].value);
    }

    // Inicializar com o tipo polo
    console.log('🏢 [DEBUG] Inicializando com tipo polo...');
    alterarTipoSimples('polo');

    // Verificar se a função buscarAlunos está disponível
    if (typeof buscarAlunos === 'function') {
        console.log('✅ [DEBUG] Função buscarAlunos está disponível');
    } else {
        console.error('❌ [ERROR] Função buscarAlunos NÃO está disponível!');
    }

    console.log('🚀 [DEBUG] Página inicializada com sucesso');
    console.log('💡 [DEBUG] Agora você pode testar a busca de alunos');
});
</script>
