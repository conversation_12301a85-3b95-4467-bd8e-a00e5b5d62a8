<?php
/**
 * SISTEMA DE RASTREAMENTO DO MÓDULO FINANCEIRO
 * 
 * Este arquivo contém funções específicas para rastrear todas as ações
 * dos usuários no módulo financeiro com detalhes completos.
 */

// Database já carregada pelo init.php do financeiro

/**
 * Registra uma ação específica do módulo financeiro
 * 
 * @param string $acao Ação realizada (ex: 'visualizar_listagem', 'criar_conta', 'editar_conta')
 * @param string $descricao Descrição detalhada da ação
 * @param int|null $objetoId ID do objeto relacionado (ex: ID da conta)
 * @param string|null $objetoTipo Tipo do objeto (ex: 'conta_receber', 'categoria')
 * @param array|null $dadosAntigos Dados antes da alteração
 * @param array|null $dadosNovos Dados após a alteração
 * @param array $detalhesExtras Informações extras específicas da ação
 */
function registrarAcaoFinanceiro($acao, $descricao, $objetoId = null, $objetoTipo = null, $dadosAntigos = null, $dadosNovos = null, $detalhesExtras = []) {
    try {
        $db = Database::getInstance();
        
        // Coleta informações do contexto
        $usuarioId = $_SESSION['user_id'] ?? null;
        $ipOrigem = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        $paginaAtual = $_SERVER['REQUEST_URI'] ?? null;
        $metodoHttp = $_SERVER['REQUEST_METHOD'] ?? null;
        
        // Prepara dados extras com informações do contexto
        $dadosExtras = array_merge([
            'pagina' => $paginaAtual,
            'metodo_http' => $metodoHttp,
            'user_agent' => $userAgent,
            'timestamp_detalhado' => microtime(true),
            'sessao_id' => session_id(),
            'referer' => $_SERVER['HTTP_REFERER'] ?? null
        ], $detalhesExtras);
        
        // Detecta dispositivo básico
        $dispositivo = 'Desktop';
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            $dispositivo = 'Mobile';
        } elseif (preg_match('/Tablet/', $userAgent)) {
            $dispositivo = 'Tablet';
        }
        
        // Insere o log no banco
        $sql = "INSERT INTO logs_sistema (
            usuario_id, modulo, acao, descricao, ip_origem, dispositivo,
            objeto_id, objeto_tipo, dados_antigos, dados_novos, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $usuarioId,
            'financeiro',
            $acao,
            $descricao,
            $ipOrigem,
            $dispositivo,
            $objetoId,
            $objetoTipo,
            $dadosAntigos ? json_encode($dadosAntigos, JSON_UNESCAPED_UNICODE) : null,
            $dadosNovos ? json_encode(array_merge($dadosNovos, $dadosExtras), JSON_UNESCAPED_UNICODE) : json_encode($dadosExtras, JSON_UNESCAPED_UNICODE)
        ];
        
        $db->query($sql, $params);
        
        return true;
        
    } catch (Exception $e) {
        // Log de erro silencioso para não interromper o fluxo
        error_log("Erro ao registrar ação financeiro: " . $e->getMessage());
        return false;
    }
}

/**
 * Registra acesso a uma página do módulo financeiro
 */
function registrarAcessoPagina($pagina, $parametros = []) {
    $descricao = "Acesso à página: $pagina";
    
    $detalhes = [
        'tipo_acesso' => 'visualizacao_pagina',
        'parametros_get' => $_GET,
        'parametros_post' => array_keys($_POST), // Apenas as chaves por segurança
        'filtros_aplicados' => $parametros
    ];
    
    registrarAcaoFinanceiro('acesso_pagina', $descricao, null, 'pagina', null, null, $detalhes);
}

/**
 * Registra uso de filtros na listagem
 */
function registrarUsoFiltros($filtros) {
    $descricao = "Aplicação de filtros na listagem";
    
    $detalhes = [
        'tipo_acesso' => 'uso_filtros',
        'filtros_aplicados' => $filtros,
        'quantidade_filtros' => count(array_filter($filtros))
    ];
    
    registrarAcaoFinanceiro('uso_filtros', $descricao, null, 'filtro', null, null, $detalhes);
}

/**
 * Registra busca realizada
 */
function registrarBusca($termoBusca, $resultados = 0) {
    $descricao = "Busca realizada: '$termoBusca'";
    
    $detalhes = [
        'tipo_acesso' => 'busca',
        'termo_busca' => $termoBusca,
        'quantidade_resultados' => $resultados,
        'tamanho_termo' => strlen($termoBusca)
    ];
    
    registrarAcaoFinanceiro('busca', $descricao, null, 'busca', null, null, $detalhes);
}

/**
 * Registra operação CRUD (Create, Read, Update, Delete)
 */
function registrarOperacaoCRUD($operacao, $tipo, $objetoId, $dadosAntigos = null, $dadosNovos = null) {
    $acoes = [
        'create' => 'Criação',
        'read' => 'Visualização',
        'update' => 'Atualização',
        'delete' => 'Exclusão'
    ];
    
    $acao = strtolower($operacao);
    $descricaoOperacao = $acoes[$acao] ?? 'Operação';
    $descricao = "$descricaoOperacao de $tipo";
    
    if ($objetoId) {
        $descricao .= " (ID: $objetoId)";
    }
    
    $detalhes = [
        'tipo_acesso' => 'operacao_crud',
        'operacao' => $operacao,
        'tipo_objeto' => $tipo
    ];
    
    // Adiciona informações específicas baseadas na operação
    if ($acao === 'create' && $dadosNovos) {
        $detalhes['campos_preenchidos'] = array_keys($dadosNovos);
        $detalhes['quantidade_campos'] = count($dadosNovos);
    } elseif ($acao === 'update' && $dadosAntigos && $dadosNovos) {
        $camposAlterados = [];
        foreach ($dadosNovos as $campo => $valor) {
            if (isset($dadosAntigos[$campo]) && $dadosAntigos[$campo] != $valor) {
                $camposAlterados[] = $campo;
            }
        }
        $detalhes['campos_alterados'] = $camposAlterados;
        $detalhes['quantidade_alteracoes'] = count($camposAlterados);
    }
    
    registrarAcaoFinanceiro("crud_$acao", $descricao, $objetoId, $tipo, $dadosAntigos, $dadosNovos, $detalhes);
}

/**
 * Registra exportação de dados
 */
function registrarExportacao($tipo, $formato, $filtros = [], $quantidadeRegistros = 0) {
    $descricao = "Exportação de $tipo em formato $formato";
    
    $detalhes = [
        'tipo_acesso' => 'exportacao',
        'tipo_dados' => $tipo,
        'formato' => $formato,
        'filtros_aplicados' => $filtros,
        'quantidade_registros' => $quantidadeRegistros
    ];
    
    registrarAcaoFinanceiro('exportacao', $descricao, null, 'exportacao', null, null, $detalhes);
}

/**
 * Registra impressão de relatórios
 */
function registrarImpressao($tipo, $parametros = []) {
    $descricao = "Impressão de $tipo";
    
    $detalhes = [
        'tipo_acesso' => 'impressao',
        'tipo_relatorio' => $tipo,
        'parametros' => $parametros
    ];
    
    registrarAcaoFinanceiro('impressao', $descricao, null, 'relatorio', null, null, $detalhes);
}

/**
 * Registra erro ou tentativa de acesso não autorizado
 */
function registrarErroAcesso($tipo, $detalhes = []) {
    $descricao = "Erro de acesso: $tipo";
    
    $detalhesCompletos = array_merge([
        'tipo_acesso' => 'erro',
        'tipo_erro' => $tipo
    ], $detalhes);
    
    registrarAcaoFinanceiro('erro_acesso', $descricao, null, 'erro', null, null, $detalhesCompletos);
}

/**
 * Registra tempo de permanência em uma página
 */
function registrarTempoPermanencia($pagina, $tempoSegundos) {
    $descricao = "Permanência na página $pagina por " . number_format($tempoSegundos, 2) . " segundos";
    
    $detalhes = [
        'tipo_acesso' => 'tempo_permanencia',
        'pagina' => $pagina,
        'tempo_segundos' => $tempoSegundos,
        'tempo_formatado' => gmdate("H:i:s", $tempoSegundos)
    ];
    
    registrarAcaoFinanceiro('tempo_permanencia', $descricao, null, 'permanencia', null, null, $detalhes);
}

/**
 * Registra download de arquivos
 */
function registrarDownload($arquivo, $tipo = 'documento') {
    $descricao = "Download do arquivo: $arquivo";
    
    $detalhes = [
        'tipo_acesso' => 'download',
        'nome_arquivo' => $arquivo,
        'tipo_arquivo' => $tipo,
        'tamanho_arquivo' => file_exists($arquivo) ? filesize($arquivo) : null
    ];
    
    registrarAcaoFinanceiro('download', $descricao, null, 'arquivo', null, null, $detalhes);
}

/**
 * Registra configuração de preferências do usuário
 */
function registrarConfiguracaoPreferencia($configuracao, $valorAntigo, $valorNovo) {
    $descricao = "Alteração de configuração: $configuracao";
    
    $detalhes = [
        'tipo_acesso' => 'configuracao',
        'configuracao' => $configuracao,
        'valor_anterior' => $valorAntigo,
        'valor_novo' => $valorNovo
    ];
    
    registrarAcaoFinanceiro('configuracao', $descricao, null, 'preferencia', 
        ['configuracao' => $configuracao, 'valor' => $valorAntigo],
        ['configuracao' => $configuracao, 'valor' => $valorNovo],
        $detalhes
    );
}
?>
