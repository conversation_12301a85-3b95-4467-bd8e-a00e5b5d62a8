<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administração de Alunos - Faciencia EAD</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
            background-color: var(--light-bg);
        }

        /* Sidebar Moderna */
        .sidebar {
            background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .sidebar-logo img {
            max-width: 50px;
            margin-right: 10px;
        }

        .sidebar-menu {
            list-style: none;
            flex-grow: 1;
            padding-left: 0;
        }

        .sidebar-menu li {
            margin-bottom: 10px;
        }

        .sidebar-menu a {
            color: var(--white);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.2);
        }

        .sidebar-menu a i {
            margin-right: 15px;
            font-size: 1.2rem;
            width: 30px;
            text-align: center;
        }

        .sidebar-section {
            margin-bottom: 20px;
        }

        .sidebar-section-header {
            font-size: 0.9rem;
            text-transform: uppercase;
            color: rgba(255,255,255,0.6);
            margin-left: 15px;
            margin-bottom: 10px;
        }

        /* Conteúdo Principal */
        .main-content {
            background-color: var(--white);
            padding: 30px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-purple);
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-info img {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid var(--primary-purple);
        }

        .admin-card {
            background: var(--white);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(106, 90, 205, 0.1);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .admin-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-purple), var(--light-purple));
        }

        .notification-icon {
            position: relative;
            margin-right: 20px;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
        }

        .summary-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 1.5rem;
        }

        .bg-purple {
            background-color: var(--primary-purple);
        }

        .bg-blue {
            background-color: var(--info-blue);
        }

        .bg-green {
            background-color: var(--success-green);
        }

        .bg-yellow {
            background-color: var(--warning-yellow);
        }

        .bg-red {
            background-color: var(--danger-red);
        }

        .summary-text h4 {
            font-size: 1.4rem;
            margin-bottom: 5px;
        }

        .summary-text p {
            font-size: 0.9rem;
            color: #777;
            margin-bottom: 0;
        }

        /* Elementos específicos para gerenciamento de alunos */
        .aluno-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .search-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .search-box {
            position: relative;
            min-width: 300px;
        }

        .search-box input {
            padding-left: 40px;
            border-radius: 20px;
            border: 1px solid #ddd;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .filter-dropdown .btn {
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .table-view-header {
            background-color: var(--light-bg);
            font-weight: 600;
            border: none;
        }

        .aluno-table tr:hover {
            background-color: rgba(106, 90, 205, 0.05);
        }

        .table-responsive {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .status-pill {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .aluno-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
        }

        .aluno-name-info {
            display: flex;
            align-items: center;
        }

        .progress-thin {
            height: 6px;
            border-radius: 3px;
        }

        .modal-header {
            background-color: var(--primary-purple);
            color: white;
        }

        .modal-subheader {
            background-color: var(--light-bg);
            padding: 10px 20px;
            margin: -16px -16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ddd;
        }

        .nav-tabs-custom {
            margin-bottom: 20px;
            border-bottom: 2px solid var(--light-bg);
        }

        .nav-tabs-custom .nav-link {
            border: none;
            color: var(--text-dark);
            font-weight: 500;
            padding: 10px 15px;
            margin-right: 5px;
            border-radius: 0;
        }

        .nav-tabs-custom .nav-link.active {
            color: var(--primary-purple);
            border-bottom: 2px solid var(--primary-purple);
            background-color: transparent;
        }

        .student-card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .student-card-header {
            padding: 15px;
            background-color: var(--light-bg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .student-card-body {
            padding: 20px;
        }

        .student-info-item {
            margin-bottom: 15px;
            display: flex;
        }

        .student-info-label {
            width: 120px;
            color: #777;
            font-weight: 500;
        }

        .student-info-value {
            flex-grow: 1;
        }

        .student-courses {
            margin-top: 20px;
        }

        .course-item {
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            background-color: var(--light-bg);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .course-info {
            display: flex;
            align-items: center;
        }

        .course-icon {
            width: 30px;
            height: 30px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: white;
            font-size: 0.9rem;
        }

        .pagination {
            justify-content: center;
        }

        /* Estilo para cards de alunos */
        .aluno-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .aluno-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .aluno-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .aluno-header-info {
            background-color: var(--light-bg);
            padding: 20px;
            display: flex;
            align-items: center;
            position: relative;
        }

        .aluno-header-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 3px solid white;
        }

        .aluno-header-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .aluno-header-status {
            position: absolute;
            top: 15px;
            right: 15px;
        }

        .aluno-body {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .aluno-detail-section {
            margin-bottom: 15px;
        }

        .aluno-detail-title {
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 0.9rem;
            color: #777;
            text-transform: uppercase;
        }

        .aluno-detail-item {
            display: flex;
            margin-bottom: 8px;
        }

        .aluno-detail-item i {
            min-width: 25px;
            color: var(--primary-purple);
        }

        .aluno-progress {
            margin-top: auto;
        }

        .aluno-footer {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--light-bg);
        }

        .aluno-joined {
            font-size: 0.85rem;
            color: #777;
        }

        .toggle-view-btn {
            display: flex;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #ddd;
        }

        .toggle-view-btn button {
            border: none;
            background-color: white;
            padding: 8px 15px;
            cursor: pointer;
        }

        .toggle-view-btn button.active {
            background-color: var(--primary-purple);
            color: white;
        }

        .toggle-view-btn button:first-child {
            border-right: 1px solid #ddd;
        }

        /* Responsividade */
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                height: 100vh;
                width: 280px;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 15px;
            }

            .search-filters {
                flex-direction: column;
                gap: 10px;
            }

            .search-box {
                min-width: 100%;
            }

            .aluno-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-grid">
        <!-- Sidebar de Administração Geral -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-graduation-cap fa-2x"></i>
                <h2 class="ms-2">Faciencia</h2>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Principal</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> Painel Geral
                        </a>
                    </li>
                    <li>
                        <a href="adm_license.html">
                            <i class="fas fa-id-card"></i> Licenciamento
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Gerenciamento</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_polo.html">
                            <i class="fas fa-globe"></i> Polos
                        </a>
                    </li>
                    <li>
                        <a href="adm_cursos.html">
                            <i class="fas fa-book-open"></i> Cursos
                        </a>
                    </li>
                    <li>
                        <a href="adm_alunos.html" class="active">
                            <i class="fas fa-users"></i> Alunos
                        </a>
                    </li>
                    <li>
                        <a href="adm_professores.html">
                            <i class="fas fa-chalkboard-teacher"></i> Professores
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Relatórios</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_relatorio_financeiro.html">
                            <i class="fas fa-dollar-sign"></i> Financeiro
                        </a>
                    </li>
                    <li>
                        <a href="adm_relatorio_desempenho.html">
                            <i class="fas fa-chart-line"></i> Desempenho
                        </a>
                    </li>
                    <li>
                        <a href="adm_relatorio_licencas.html">
                            <i class="fas fa-id-badge"></i> Licenças
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Sistema</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="adm_configuracoes.html">
                            <i class="fas fa-cogs"></i> Configurações
                        </a>
                    </li>
                    <li>
                        <a href="index.html" class="text-danger">
                            <i class="fas fa-sign-out-alt"></i> Sair
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Cabeçalho do Painel -->
            <header class="dashboard-header">
                <h1 class="m-0">Gerenciamento de Alunos</h1>
                <div class="d-flex align-items-center">
                    <div class="notification-icon">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="user-info">
                       
                        <div>
                            <h6 class="m-0">Admin Master</h6>
                            <small class="text-muted">Administrador</small>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Resumo dos Alunos -->
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-icon bg-purple">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="summary-text">
                        <h4>2.345</h4>
                        <p>Total de Alunos</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon bg-green">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="summary-text">
                        <h4>1.987</h4>
                        <p>Alunos Ativos</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon bg-blue">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="summary-text">
                        <h4>512</h4>
                        <p>Cursos Concluídos</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon bg-yellow">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <div class="summary-text">
                        <h4>358</h4>
                        <p>Alunos Inativos</p>
                    </div>
                </div>
            </div>

            <!-- Controles e Filtros -->
            <section class="admin-card">
                <div class="aluno-header">
                    <h4 class="m-0">Gerenciamento de Alunos</h4>
                    <div class="d-flex gap-3">
                        <div class="toggle-view-btn">
                            <button class="active" id="cardViewBtn">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button id="tableViewBtn">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAlunoModal">
                            <i class="fas fa-user-plus me-2"></i>Novo Aluno
                        </button>
                    </div>
                </div>

                <div class="search-filters">
                    <div class="search-box">
                        <input type="text" class="form-control" placeholder="Buscar aluno...">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="filter-dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter"></i> Status
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Todos</a></li>
                            <li><a class="dropdown-item" href="#">Ativos</a></li>
                            <li><a class="dropdown-item" href="#">Inativos</a></li>
                            <li><a class="dropdown-item" href="#">Sem curso</a></li>
                        </ul>
                    </div>
                    <div class="filter-dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-book-open"></i> Curso
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Todos</a></li>
                            <li><a class="dropdown-item" href="#">Desenvolvimento Web</a></li>
                            <li><a class="dropdown-item" href="#">Marketing Digital</a></li>
                            <li><a class="dropdown-item" href="#">Gestão de Negócios</a></li>
                            <li><a class="dropdown-item" href="#">Inglês para Negócios</a></li>
                            <li><a class="dropdown-item" href="#">UX/UI Design</a></li>
                        </ul>
                    </div>
                    <div class="filter-dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe"></i> Polo
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Todos</a></li>
                            <li><a class="dropdown-item" href="#">Polo São Paulo</a></li>
                            <li><a class="dropdown-item" href="#">Polo Rio de Janeiro</a></li>
                            <li><a class="dropdown-item" href="#">Polo Belo Horizonte</a></li>
                            <li><a class="dropdown-item" href="#">Polo Curitiba</a></li>
                        </ul>
                    </div>
                    <div class="filter-dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-sort"></i> Ordenar
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Nome (A-Z)</a></li>
                            <li><a class="dropdown-item" href="#">Nome (Z-A)</a></li>
                            <li><a class="dropdown-item" href="#">Data de cadastro (Recente)</a></li>
                            <li><a class="dropdown-item" href="#">Data de cadastro (Antigo)</a></li>
                            <li><a class="dropdown-item" href="#">Progresso (Maior)</a></li>
                            <li><a class="dropdown-item" href="#">Progresso (Menor)</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Visualização em Cards -->
                <div id="cardView" class="aluno-grid">
                    <!-- Card Aluno 1 -->
                    <div class="aluno-card">
                        <div class="aluno-header-info">
                         
                            <div>
                                <h5 class="aluno-header-name">João Silva</h5>
                                <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <span class="aluno-header-status status-pill bg-success">Ativo</span>
                        </div>
                        <div class="aluno-body">
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Informações Pessoais</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-phone"></i>
                                    <span>(11) 98765-4321</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>São Paulo, SP</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Polo São Paulo</span>
                                </div>
                            </div>
                            
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Cursos</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-laptop-code"></i>
                                    <span>Desenvolvimento Web Full Stack</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-bullhorn"></i>
                                    <span>Marketing Digital Avançado</span>
                                </div>
                            </div>
                            
                            <div class="aluno-progress">
                                <div class="progress-label d-flex justify-content-between">
                                    <span>Progresso médio</span>
                                    <span>85%</span>
                                </div>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-success" style="width: 85%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="aluno-footer">
                            <div class="aluno-joined">
                                <i class="fas fa-calendar-alt me-1"></i>Ingressou em 15/01/2024
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                <i class="fas fa-eye me-1"></i>Detalhes
                            </button>
                        </div>
                    </div>

                    <!-- Card Aluno 2 -->
                    <div class="aluno-card">
                        <div class="aluno-header-info">
                          
                            <div>
                                <h5 class="aluno-header-name">Maria Souza</h5>
                                <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <span class="aluno-header-status status-pill bg-success">Ativo</span>
                        </div>
                        <div class="aluno-body">
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail- <h6 class="aluno-detail-title">Informações Pessoais</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-phone"></i>
                                    <span>(11) 97654-3210</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Rio de Janeiro, RJ</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Polo Rio de Janeiro</span>
                                </div>
                            </div>
                            
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Cursos</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-laptop-code"></i>
                                    <span>Desenvolvimento Web Full Stack</span>
                                </div>
                            </div>
                            
                            <div class="aluno-progress">
                                <div class="progress-label d-flex justify-content-between">
                                    <span>Progresso médio</span>
                                    <span>92%</span>
                                </div>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-success" style="width: 92%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="aluno-footer">
                            <div class="aluno-joined">
                                <i class="fas fa-calendar-alt me-1"></i>Ingressou em 18/01/2024
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                <i class="fas fa-eye me-1"></i>Detalhes
                            </button>
                        </div>
                    </div>

                    <!-- Card Aluno 3 -->
                    <div class="aluno-card">
                        <div class="aluno-header-info">
                           
                            <div>
                                <h5 class="aluno-header-name">Pedro Santos</h5>
                                <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <span class="aluno-header-status status-pill bg-warning text-dark">Progresso Baixo</span>
                        </div>
                        <div class="aluno-body">
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Informações Pessoais</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-phone"></i>
                                    <span>(31) 98765-4321</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Belo Horizonte, MG</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Polo Belo Horizonte</span>
                                </div>
                            </div>
                            
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Cursos</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-chart-line"></i>
                                    <span>Gestão de Negócios Digitais</span>
                                </div>
                            </div>
                            
                            <div class="aluno-progress">
                                <div class="progress-label d-flex justify-content-between">
                                    <span>Progresso médio</span>
                                    <span>45%</span>
                                </div>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-warning" style="width: 45%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="aluno-footer">
                            <div class="aluno-joined">
                                <i class="fas fa-calendar-alt me-1"></i>Ingressou em 20/01/2024
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                <i class="fas fa-eye me-1"></i>Detalhes
                            </button>
                        </div>
                    </div>

                    <!-- Card Aluno 4 -->
                    <div class="aluno-card">
                        <div class="aluno-header-info">
                          
                            <div>
                                <h5 class="aluno-header-name">Ana Oliveira</h5>
                                <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <span class="aluno-header-status status-pill bg-success">Ativo</span>
                        </div>
                        <div class="aluno-body">
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Informações Pessoais</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-phone"></i>
                                    <span>(21) 99876-5432</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Rio de Janeiro, RJ</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Polo Rio de Janeiro</span>
                                </div>
                            </div>
                            
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Cursos</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-language"></i>
                                    <span>Inglês para Negócios</span>
                                </div>
                            </div>
                            
                            <div class="aluno-progress">
                                <div class="progress-label d-flex justify-content-between">
                                    <span>Progresso médio</span>
                                    <span>65%</span>
                                </div>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-info" style="width: 65%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="aluno-footer">
                            <div class="aluno-joined">
                                <i class="fas fa-calendar-alt me-1"></i>Ingressou em 05/02/2024
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                <i class="fas fa-eye me-1"></i>Detalhes
                            </button>
                        </div>
                    </div>

                    <!-- Card Aluno 5 -->
                    <div class="aluno-card">
                        <div class="aluno-header-info">
                           
                            <div>
                                <h5 class="aluno-header-name">Bruno Costa</h5>
                                <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <span class="aluno-header-status status-pill bg-danger">Inativo</span>
                        </div>
                        <div class="aluno-body">
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Informações Pessoais</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-phone"></i>
                                    <span>(11) 95678-4321</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>São Paulo, SP</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Polo São Paulo</span>
                                </div>
                            </div>
                            
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Cursos</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-laptop-code"></i>
                                    <span>Desenvolvimento Web Full Stack</span>
                                </div>
                            </div>
                            
                            <div class="aluno-progress">
                                <div class="progress-label d-flex justify-content-between">
                                    <span>Progresso médio</span>
                                    <span>20%</span>
                                </div>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-danger" style="width: 20%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="aluno-footer">
                            <div class="aluno-joined">
                                <i class="fas fa-calendar-alt me-1"></i>Ingressou em 10/02/2024
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                <i class="fas fa-eye me-1"></i>Detalhes
                            </button>
                        </div>
                    </div>

                    <!-- Card Aluno 6 -->
                    <div class="aluno-card">
                        <div class="aluno-header-info">
                          
                            <div>
                                <h5 class="aluno-header-name">Carla Gomes</h5>
                                <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                            <span class="aluno-header-status status-pill bg-success">Ativo</span>
                        </div>
                        <div class="aluno-body">
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Informações Pessoais</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-phone"></i>
                                    <span>(41) 98765-4321</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Curitiba, PR</span>
                                </div>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Polo Curitiba</span>
                                </div>
                            </div>
                            
                            <div class="aluno-detail-section">
                                <h6 class="aluno-detail-title">Cursos</h6>
                                <div class="aluno-detail-item">
                                    <i class="fas fa-palette"></i>
                                    <span>UX/UI Design Avançado</span>
                                </div>
                            </div>
                            
                            <div class="aluno-progress">
                                <div class="progress-label d-flex justify-content-between">
                                    <span>Progresso médio</span>
                                    <span>75%</span>
                                </div>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-success" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="aluno-footer">
                            <div class="aluno-joined">
                                <i class="fas fa-calendar-alt me-1"></i>Ingressou em 01/03/2024
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                <i class="fas fa-eye me-1"></i>Detalhes
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Visualização em Tabela (inicialmente oculta) -->
                <div id="tableView" style="display: none;">
                    <div class="table-responsive">
                        <table class="table aluno-table">
                            <thead>
                                <tr class="table-view-header">
                                    <th>Aluno</th>
                                    <th>Contato</th>
                                    <th>Polo</th>
                                    <th>Cursos</th>
                                    <th>Progresso</th>
                                    <th>Status</th>
                                    <th>Data de Cadastro</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="aluno-name-info">
                                            
                                            <div>
                                                <div class="fw-bold">João Silva</div>
                                                <small class="text-muted">ID: 001234</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><EMAIL></div>
                                        <small class="text-muted">(11) 98765-4321</small>
                                    </td>
                                    <td>São Paulo</td>
                                    <td>
                                        <span class="badge bg-primary">Desenvolvimento Web</span>
                                        <span class="badge bg-success">Marketing Digital</span>
                                    </td>
                                    <td>
                                        <div class="progress progress-thin" style="width: 100px;">
                                            <div class="progress-bar bg-success" style="width: 85%"></div>
                                        </div>
                                        <small>85%</small>
                                    </td>
                                    <td><span class="status-pill bg-success">Ativo</span></td>
                                    <td>15/01/2024</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="aluno-name-info">
                                            
                                            <div>
                                                <div class="fw-bold">Maria Souza</div>
                                                <small class="text-muted">ID: 001235</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><EMAIL></div>
                                        <small class="text-muted">(11) 97654-3210</small>
                                    </td>
                                    <td>Rio de Janeiro</td>
                                    <td>
                                        <span class="badge bg-primary">Desenvolvimento Web</span>
                                    </td>
                                    <td>
                                        <div class="progress progress-thin" style="width: 100px;">
                                            <div class="progress-bar bg-success" style="width: 92%"></div>
                                        </div>
                                        <small>92%</small>
                                    </td>
                                    <td><span class="status-pill bg-success">Ativo</span></td>
                                    <td>18/01/2024</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="aluno-name-info">
                                            
                                            <div>
                                                <div class="fw-bold">Pedro Santos</div>
                                                <small class="text-muted">ID: 001236</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><EMAIL></div>
                                        <small class="text-muted">(31) 98765-4321</small>
                                    </td>
                                    <td>Belo Horizonte</td>
                                    <td>
                                        <span class="badge bg-info">Gestão de Negócios</span>
                                    </td>
                                    <td>
                                        <div class="progress progress-thin" style="width: 100px;">
                                            <div class="progress-bar bg-warning" style="width: 45%"></div>
                                        </div>
                                        <small>45%</small>
                                    </td>
                                    <td><span class="status-pill bg-warning text-dark">Progresso Baixo</span></td>
                                    <td>20/01/2024</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="aluno-name-info">
                                           
                                            <div>
                                                <div class="fw-bold">Ana Oliveira</div>
                                                <small class="text-muted">ID: 001237</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><EMAIL></div>
                                        <small class="text-muted">(21) 99876-5432</small>
                                    </td>
                                    <td>Rio de Janeiro</td>
                                    <td>
                                        <span class="badge bg-warning">Inglês para Negócios</span>
                                    </td>
                                    <td>
                                        <div class="progress progress-thin" style="width: 100px;">
                                            <div class="progress-bar bg-info" style="width: 65%"></div>
                                        </div>
                                        <small>65%</small>
                                    </td>
                                    <td><span class="status-pill bg-success">Ativo</span></td>
                                    <td>05/02/2024</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="aluno-name-info">
                                           
                                            <div>
                                                <div class="fw-bold">Bruno Costa</div>
                                                <small class="text-muted">ID: 001238</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><EMAIL></div>
                                        <small class="text-muted">(11) 95678-4321</small>
                                    </td>
                                    <td>São Paulo</td>
                                    <td>
                                        <span class="badge bg-primary">Desenvolvimento Web</span>
                                    </td>
                                    <td>
                                        <div class="progress progress-thin" style="width: 100px;">
                                            <div class="progress-bar bg-danger" style="width: 20%"></div>
                                        </div>
                                        <small>20%</small>
                                    </td>
                                    <td><span class="status-pill bg-danger">Inativo</span></td>
                                    <td>10/02/2024</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="aluno-name-info">
                                            
                                            <div>
                                                <div class="fw-bold">Carla Gomes</div>
                                                <small class="text-muted">ID: 001239</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><EMAIL></div>
                                        <small class="text-muted">(41) 98765-4321</small>
                                    </td>
                                    <td>Curitiba</td>
                                    <td>
                                        <span class="badge bg-danger">UX/UI Design</span>
                                    </td>
                                    <td>
                                        <div class="progress progress-thin" style="width: 100px;">
                                            <div class="progress-bar bg-success" style="width: 75%"></div>
                                        </div>
                                        <small>75%</small>
                                    </td>
                                    <td><span class="status-pill bg-success">Ativo</span></td>
                                    <td>01/03/2024</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#alunoDetailModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>Mostrando 6 de 2.345 alunos</div>
                        <nav>
                            <ul class="pagination">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Próximo</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modal de Detalhes do Aluno -->
    <div class="modal fade" id="alunoDetailModal" tabindex="-1" aria-labelledby="alunoDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="alunoDetailModalLabel">Detalhes do Aluno</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="modal-subheader">
                        <div class="d-flex align-items-center">
                           
                            <div>
                                <h5 class="m-0">João Silva</h5>
                                <p class="m-0">ID: 001234</p>
                            </div>
                        </div>
                        <span class="status-pill bg-success">Ativo</span>
                    </div>

                    <!-- Abas para diferentes informações -->
                    <ul class="nav nav-tabs nav-tabs-custom" id="alunoDetailTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab">Informações</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab">Cursos</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">Desempenho</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">Histórico</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="alunoDetailTabsContent">
                        <!-- Aba de Informações Pessoais -->
                        <div class="tab-pane fade show active" id="info" role="tabpanel">
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="student-card">
                                        <div class="card-header">Informações Pessoais</div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr>
                                                    <th style="width: 35%">Nome Completo:</th>
                                                    <td>João Carlos da Silva</td>
                                                </tr>
                                                <tr>
                                                    <th style="width: 35%">Data de Nascimento:</th>
                                                    <td>15/09/1995</td>
                                                </tr>
                                                <tr>
                                                    <th>CPF:</th>
                                                    <td>123.456.789-00</td>
                                                </tr>
                                                <tr>
                                                    <th>RG:</th>
                                                    <td>MG-12.345.678</td>
                                                </tr>
                                                <tr>
                                                    <th>Estado Civil:</th>
                                                    <td>Solteiro</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="student-card">
                                        <div class="card-header">Contato</div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr>
                                                    <th style="width: 35%">E-mail:</th>
                                                    <td><EMAIL></td>
                                                </tr>
                                                <tr>
                                                    <th>Telefone:</th>
                                                    <td>(11) 98765-4321</td>
                                                </tr>
                                                <tr>
                                                    <th>Endereço:</th>
                                                    <td>Rua das Flores, 123</td>
                                                </tr>
                                                <tr>
                                                    <th>Cidade/Estado:</th>
                                                    <td>São Paulo, SP</td>
                                                </tr>
                                                <tr>
                                                    <th>CEP:</th>
                                                    <td>01234-567</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Courses Tab -->
                        <div class="tab-pane fade" id="courses" role="tabpanel">
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="student-card">
                                        <div class="card-header">Cursos Matriculados</div>
                                        <div class="card-body">
                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th>Curso</th>
                                                        <th>Polo</th>
                                                        <th>Data de Matrícula</th>
                                                        <th>Progresso</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Desenvolvimento Web Full Stack</td>
                                                        <td>São Paulo</td>
                                                        <td>15/01/2024</td>
                                                        <td>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar bg-success" role="progressbar" style="width: 85%;" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100">85%</div>
                                                            </div>
                                                        </td>
                                                        <td><span class="badge bg-success">Em Andamento</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Marketing Digital</td>
                                                        <td>São Paulo</td>
                                                        <td>20/02/2024</td>
                                                        <td>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar bg-info" role="progressbar" style="width: 40%;" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100">40%</div>
                                                            </div>
                                                        </td>
                                                        <td><span class="badge bg-warning">Iniciando</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Tab -->
                        <div class="tab-pane fade" id="performance" role="tabpanel">
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="student-card">
                                        <div class="card-header">Desempenho Geral</div>
                                        <div class="card-body">
                                            <div class="performance-summary">
                                                <div class="performance-item">
                                                    <span>Média Geral</span>
                                                    <span class="badge bg-success">8.5</span>
                                                </div>
                                                <div class="performance-item">
                                                    <span>Aproveitamento</span>
                                                    <span class="badge bg-info">85%</span>
                                                </div>
                                                <div class="performance-item">
                                                    <span>Módulos Concluídos</span>
                                                    <span class="badge bg-primary">12/15</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="student-card">
                                        <div class="card-header">Evolução de Notas</div>
                                        <div class="card-body">
                                            <div class="chart-container" style="position: relative; height:250px;">
                                                <canvas id="performanceChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- History Tab -->
                        <div class="tab-pane fade" id="history" role="tabpanel">
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="student-card">
                                        <div class="card-header">Histórico de Atividades</div>
                                        <div class="card-body">
                                            <ul class="timeline">
                                                <li>
                                                    <div class="timeline-item">
                                                        <span class="timeline-date">15/01/2024</span>
                                                        <div class="timeline-content">
                                                            <strong>Matrícula Realizada</strong>
                                                            <p>Início do curso de Desenvolvimento Web Full Stack</p>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="timeline-item">
                                                        <span class="timeline-date">20/02/2024</span>
                                                        <div class="timeline-content">
                                                            <strong>Curso Adicional</strong>
                                                            <p>Matriculado em Marketing Digital</p>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="timeline-item">
                                                        <span class="timeline-date">10/03/2024</span>
                                                        <div class="timeline-content">
                                                            <strong>Projeto Concluído</strong>
                                                            <p>Entrega do primeiro projeto de Desenvolvimento Web</p>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary">Editar Perfil</button>
                </div>
            </div>
        </div>
    </div>