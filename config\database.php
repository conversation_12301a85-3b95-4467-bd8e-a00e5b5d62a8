<?php
/**
 * Configuração do banco de dados
 * 
 * Este arquivo contém as configurações de conexão com o banco de dados
 */

// Configurações do banco de dados
define('DB_HOST', 'localhost');
define('DB_NAME', 'u682219090_faciencia_erp');
define('DB_USER', 'u682219090_faciencia_erp');
define('DB_PASS', 'T3cn0l0g1a@');
define('DB_CHARSET', 'utf8mb4');

// Configurações de timezone
date_default_timezone_set('America/Sao_Paulo');

// Criar conexão PDO global
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
} catch (PDOException $e) {
    error_log("Erro de conexão com banco de dados: " . $e->getMessage());
    die("Erro de conexão com o banco de dados. Verifique as configurações.");
}

// Função para obter conexão PDO
function getPDO() {
    global $pdo;
    return $pdo;
}
?>