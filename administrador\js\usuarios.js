// JavaScript para gerenciamento de usuários
console.log('Arquivo usuarios.js carregado!');

// Funções para modais
function abrirModalNovoUsuario() {
    console.log('Abrindo modal novo usuário');

    const modal = document.getElementById('modalNovoUsuario');
    const form = document.getElementById('formNovoUsuario');

    if (!modal || !form) {
        console.error('Modal ou formulário não encontrado:', { modal: !!modal, form: !!form });
        alert('Erro: Modal não encontrado. Recarregue a página e tente novamente.');
        return;
    }

    modal.classList.remove('hidden');
    form.reset();
}

function abrirModalEditarUsuario(usuarioId, nome, email, tipoUsuario, poloId) {
    console.log('Abrindo modal editar usuário:', usuarioId, nome, email, tipoUsuario, poloId);

    // Verifica se todos os elementos existem
    const modal = document.getElementById('modalEditarUsuario');
    const editUsuarioId = document.getElementById('editUsuarioId');
    const editNome = document.getElementById('editNome');
    const editEmail = document.getElementById('editEmail');
    const editTipoUsuario = document.getElementById('editTipoUsuario');
    const editPoloId = document.getElementById('editPoloId');
    const campoPoloEdit = document.getElementById('campoPoloIdEdit');

    if (!modal || !editUsuarioId || !editNome || !editEmail || !editTipoUsuario) {
        console.error('Elementos do modal não encontrados:', {
            modal: !!modal,
            editUsuarioId: !!editUsuarioId,
            editNome: !!editNome,
            editEmail: !!editEmail,
            editTipoUsuario: !!editTipoUsuario
        });
        alert('Erro: Modal não encontrado. Recarregue a página e tente novamente.');
        return;
    }

    // Abre o modal e preenche os dados
    modal.classList.remove('hidden');
    editUsuarioId.value = usuarioId;
    editNome.value = nome;
    editEmail.value = email;
    editTipoUsuario.value = tipoUsuario;
    if (editPoloId) editPoloId.value = poloId || '';

    // Mostrar/ocultar campo polo
    if (campoPoloEdit) {
        if (tipoUsuario === 'polo' || tipoUsuario === 'aluno') {
            campoPoloEdit.classList.remove('hidden');
        } else {
            campoPoloEdit.classList.add('hidden');
        }
    }
}

function abrirModalResetarSenha(usuarioId, nome) {
    console.log('Abrindo modal resetar senha:', usuarioId, nome);

    // Verifica se o modal existe
    const modal = document.getElementById('modalResetarSenha');
    if (!modal) {
        console.error('Modal modalResetarSenha não encontrado!');
        alert('Erro: Modal não encontrado. Recarregue a página e tente novamente.');
        return;
    }

    // Verifica se os elementos existem
    const resetUsuarioId = document.getElementById('resetUsuarioId');
    const resetUsuarioNome = document.getElementById('resetUsuarioNome');
    const formResetarSenha = document.getElementById('formResetarSenha');

    if (!resetUsuarioId || !resetUsuarioNome || !formResetarSenha) {
        console.error('Elementos do modal não encontrados:', {
            resetUsuarioId: !!resetUsuarioId,
            resetUsuarioNome: !!resetUsuarioNome,
            formResetarSenha: !!formResetarSenha
        });
        alert('Erro: Elementos do modal não encontrados. Recarregue a página e tente novamente.');
        return;
    }

    // Abre o modal e preenche os dados
    modal.classList.remove('hidden');
    resetUsuarioId.value = usuarioId;
    resetUsuarioNome.textContent = nome;
    formResetarSenha.reset();
    resetUsuarioId.value = usuarioId; // Define novamente após o reset
}

function fecharModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function togglePoloField(select) {
    const campoPoloId = document.getElementById('campoPoloId');
    if (select.value === 'polo' || select.value === 'aluno') {
        campoPoloId.classList.remove('hidden');
    } else {
        campoPoloId.classList.add('hidden');
    }
}

function togglePoloFieldEdit(select) {
    const campoPoloIdEdit = document.getElementById('campoPoloIdEdit');
    if (select.value === 'polo' || select.value === 'aluno') {
        campoPoloIdEdit.classList.remove('hidden');
    } else {
        campoPoloIdEdit.classList.add('hidden');
    }
}

// Função para alterar status do usuário
async function alterarStatusUsuario(usuarioId, status, nome) {
    const acoes = {
        'ativar': 'ativar',
        'desativar': 'desativar',
        'bloquear': 'bloquear',
        'desbloquear': 'desbloquear'
    };

    const acao = acoes[status];
    if (!acao) {
        alert('Ação inválida');
        return;
    }

    const mensagens = {
        'ativar': `Tem certeza que deseja ativar o usuário "${nome}"?`,
        'desativar': `Tem certeza que deseja desativar o usuário "${nome}"?`,
        'bloquear': `Tem certeza que deseja bloquear o usuário "${nome}"?`,
        'desbloquear': `Tem certeza que deseja desbloquear o usuário "${nome}"?`
    };

    if (!confirm(mensagens[status])) {
        return;
    }

    try {
        const response = await fetch('ajax/alterar_status_usuario.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                usuario_id: usuarioId,
                status: acao
            })
        });

        const result = await response.json();

        if (result.success) {
            alert(`Usuário ${acao === 'ativar' ? 'ativado' : acao === 'desativar' ? 'desativado' : acao === 'bloquear' ? 'bloqueado' : 'desbloqueado'} com sucesso!`);
            window.location.reload();
        } else {
            alert('Erro: ' + result.message);
        }
    } catch (error) {
        console.error('Erro:', error);
        alert('Erro ao alterar status do usuário. Tente novamente.');
    }
}

// Função para criar usuário
async function criarUsuario(event) {
    event.preventDefault();

    const btn = document.getElementById('btnCriarUsuario');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Criando...';
    btn.disabled = true;

    try {
        const formData = new FormData(event.target);
        formData.append('acao', 'criar_usuario');

        const response = await fetch('includes/ajax.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            alert('Usuário criado com sucesso!');
            fecharModal('modalNovoUsuario');
            window.location.reload();
        } else {
            alert('Erro: ' + result.message);
        }
    } catch (error) {
        console.error('Erro:', error);
        alert('Erro ao criar usuário. Tente novamente.');
    } finally {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

// Função para editar usuário
async function editarUsuario(event) {
    event.preventDefault();

    const btn = document.getElementById('btnEditarUsuario');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Salvando...';
    btn.disabled = true;

    try {
        const formData = new FormData(event.target);
        formData.append('acao', 'editar_usuario');

        const response = await fetch('includes/ajax.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            alert('Usuário atualizado com sucesso!');
            fecharModal('modalEditarUsuario');
            window.location.reload();
        } else {
            alert('Erro: ' + result.message);
        }
    } catch (error) {
        console.error('Erro:', error);
        alert('Erro ao editar usuário. Tente novamente.');
    } finally {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

// Função para resetar senha
async function resetarSenha(event) {
    event.preventDefault();

    const btn = document.getElementById('btnResetarSenha');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Resetando...';
    btn.disabled = true;

    try {
        const formData = new FormData(event.target);
        formData.append('acao', 'resetar_senha');

        const response = await fetch('includes/ajax.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            let message = 'Senha resetada com sucesso!';
            if (result.nova_senha) {
                message += '\n\nNova senha: ' + result.nova_senha;
                message += '\n\nAnote esta senha, pois ela não será exibida novamente.';
            }
            alert(message);
            fecharModal('modalResetarSenha');
            window.location.reload();
        } else {
            alert('Erro: ' + result.message);
        }
    } catch (error) {
        console.error('Erro:', error);
        alert('Erro ao resetar senha. Tente novamente.');
    } finally {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM carregado, inicializando eventos...');

    // Debug: Verificar se os modais existem
    const modals = ['modalNovoUsuario', 'modalEditarUsuario', 'modalResetarSenha'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        console.log(`Modal ${modalId}:`, modal ? 'ENCONTRADO' : 'NÃO ENCONTRADO');
    });

    // Fechar modal ao clicar fora dele
    window.onclick = function(event) {
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (event.target == modal) {
                fecharModal(modalId);
            }
        });
    }

    // Auto-atualizar página a cada 5 minutos
    setTimeout(() => {
        console.log('Auto-refresh da página...');
        window.location.reload();
    }, 300000);
});
