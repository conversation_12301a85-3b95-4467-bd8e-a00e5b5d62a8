<?php
/**
 * Classe para integração com API do Asaas
 */

class AsaasAPI {
    private $apiKey;
    private $walletId;
    private $webhookUrl;
    private $baseUrl;
    private $db;

    public function __construct($db) {
        $this->db = $db;
        $this->apiKey = '$aact_prod_000MzkwODA2MWY2OGM3MWRlMDU2NWM3MzJlNzZmNGZhZGY6OmY1YmExYTViLTJkZWItNGY1Yi04NTljLTdmYWIzNmM5ZDI4Njo6JGFhY2hfNmE5OThmZGMtMjFhMC00MjI1LTk3NjktY2M5ZTNlYTc1NjMy';
        $this->walletId = '207d59bf-8981-4797-8cbc-677f321bcf01';
        $this->webhookUrl = 'https://app.faciencia.edu.br/api/asaas_webhook.php';
        $this->baseUrl = 'https://api.asaas.com/v3';
    }

    /**
     * Fazer requisição para API do Asaas
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->baseUrl . $endpoint;

        $headers = [
            'access_token: ' . $this->apiKey,
            'Content-Type: application/json',
            'User-Agent: Mozilla/5.0 (compatible; Reinandus-Sistema/1.0)'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Desabilitar verificação SSL
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // Desabilitar verificação do host
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Seguir redirecionamentos
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3); // Máximo 3 redirecionamentos

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);

        // Log para debug (remover em produção)
        error_log("AsaasAPI Debug - URL: $url, HTTP Code: $httpCode, Error: $error");
        if ($response) {
            error_log("AsaasAPI Debug - Response: " . substr($response, 0, 500));
        }

        if ($error) {
            throw new Exception("Erro cURL: " . $error);
        }

        $decodedResponse = json_decode($response, true);

        if ($httpCode >= 400) {
            $errorMessage = isset($decodedResponse['errors'][0]['description'])
                ? $decodedResponse['errors'][0]['description']
                : 'Erro na API do Asaas';
            throw new Exception("Erro API Asaas ({$httpCode}): " . $errorMessage);
        }

        return $decodedResponse;
    }

    /**
     * Criar ou buscar cliente no Asaas
     */
    public function criarOuBuscarCliente($dadosCliente) {
        // Primeiro, tentar buscar cliente existente
        $cpfCnpj = preg_replace('/[^0-9]/', '', $dadosCliente['cpfCnpj'] ?? '');

        if ($cpfCnpj) {
            try {
                $clientes = $this->makeRequest("/customers?cpfCnpj={$cpfCnpj}");
                if (!empty($clientes['data'])) {
                    return $clientes['data'][0];
                }
            } catch (Exception $e) {
                // Se não encontrar, criar novo
            }
        }

        // Criar novo cliente
        $clienteData = [
            'name' => $dadosCliente['name'],
            'cpfCnpj' => $cpfCnpj,
            'email' => $dadosCliente['email'] ?? null,
            'phone' => $dadosCliente['phone'] ?? null,
            'mobilePhone' => $dadosCliente['mobilePhone'] ?? null,
            'address' => $dadosCliente['address'] ?? null,
            'addressNumber' => $dadosCliente['addressNumber'] ?? null,
            'complement' => $dadosCliente['complement'] ?? null,
            'province' => $dadosCliente['province'] ?? null,
            'city' => $dadosCliente['city'] ?? null,
            'state' => $dadosCliente['state'] ?? null,
            'postalCode' => $dadosCliente['postalCode'] ?? null,
            'externalReference' => $dadosCliente['externalReference'] ?? null
        ];

        // Remover campos nulos
        $clienteData = array_filter($clienteData, function($value) {
            return $value !== null && $value !== '';
        });

        return $this->makeRequest('/customers', 'POST', $clienteData);
    }

    /**
     * Criar cobrança no Asaas
     */
    public function criarCobranca($dadosCobranca) {
        $cobrancaData = [
            'customer' => $dadosCobranca['customer'],
            'billingType' => $dadosCobranca['billingType'] ?? 'BOLETO',
            'value' => $dadosCobranca['value'],
            'dueDate' => $dadosCobranca['dueDate'],
            'description' => $dadosCobranca['description'] ?? '',
            'externalReference' => $dadosCobranca['externalReference'] ?? null,
            'installmentCount' => $dadosCobranca['installmentCount'] ?? null,
            'installmentValue' => $dadosCobranca['installmentValue'] ?? null,
            'discount' => $dadosCobranca['discount'] ?? null,
            'interest' => $dadosCobranca['interest'] ?? null,
            'fine' => $dadosCobranca['fine'] ?? null,
            'postalService' => false
        ];

        // Remover campos nulos
        $cobrancaData = array_filter($cobrancaData, function($value) {
            return $value !== null && $value !== '';
        });

        return $this->makeRequest('/payments', 'POST', $cobrancaData);
    }

    /**
     * Buscar cobrança por ID
     */
    public function buscarCobranca($paymentId) {
        return $this->makeRequest("/payments/{$paymentId}");
    }

    /**
     * Obter URL do boleto (bankSlipUrl)
     * Método simplificado que usa buscarCobranca
     */
    public function obterURLBoleto($paymentId) {
        try {
            $cobranca = $this->buscarCobranca($paymentId);

            if (isset($cobranca['bankSlipUrl'])) {
                error_log("🔗 URL do boleto encontrada: " . $cobranca['bankSlipUrl']);
                return $cobranca['bankSlipUrl'];
            }

            throw new Exception('URL do boleto (bankSlipUrl) não encontrada na resposta do Asaas');

        } catch (Exception $e) {
            error_log("❌ Erro ao obter URL do boleto: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Cancelar cobrança
     */
    public function cancelarCobranca($paymentId) {
        try {
            $response = $this->makeRequest("/payments/{$paymentId}", 'DELETE');

            // Log para debug
            error_log("🗑️ Cobrança cancelada no Asaas: " . $paymentId);
            error_log("Resposta: " . json_encode($response));

            return $response;
        } catch (Exception $e) {
            error_log("❌ Erro ao cancelar cobrança no Asaas: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Listar cobranças
     */
    public function listarCobrancas($filtros = []) {
        $queryString = http_build_query($filtros);
        $endpoint = '/payments' . ($queryString ? '?' . $queryString : '');
        return $this->makeRequest($endpoint);
    }

    /**
     * Verificar se API está configurada
     */
    public function isConfigurado() {
        return !empty($this->apiKey) && !empty($this->walletId);
    }

    /**
     * Obter API Key (para exibição)
     */
    public function getApiKey() {
        return $this->apiKey;
    }

    /**
     * Obter saldo da conta
     */
    public function obterSaldo() {
        return $this->makeRequest('/finance/balance');
    }

    /**
     * Testar conexão com API
     */
    public function testarConexao() {
        try {
            $response = $this->makeRequest('/myAccount');
            return [
                'sucesso' => true,
                'dados' => $response
            ];
        } catch (Exception $e) {
            return [
                'sucesso' => false,
                'erro' => $e->getMessage()
            ];
        }
    }

    /**
     * Processar webhook do Asaas com sincronização financeira completa
     */
    public function processarWebhook($dados) {
        try {
            $event = $dados['event'] ?? '';
            $payment = $dados['payment'] ?? [];

            if (empty($payment['id'])) {
                throw new Exception('ID do pagamento não encontrado no webhook');
            }

            // Buscar boleto no banco local
            $boleto = $this->db->fetchOne(
                "SELECT * FROM polos_boletos WHERE asaas_payment_id = ?",
                [$payment['id']]
            );

            if (!$boleto) {
                throw new Exception('Boleto não encontrado no sistema local');
            }

            // Usar sincronização financeira completa
            require_once __DIR__ . '/SincronizacaoFinanceira.php';
            $sincronizacao = new SincronizacaoFinanceira($this->db, $this);

            $resultado = $sincronizacao->sincronizarPagamentoBoleto($boleto['id'], $payment);

            return [
                'sucesso' => $resultado['sucesso'],
                'boleto_id' => $boleto['id'],
                'foi_pago' => $resultado['foi_pago'],
                'valor_pago' => $resultado['valor_pago'],
                'conta_receber_id' => $resultado['conta_receber_id'] ?? null,
                'transacao_id' => $resultado['transacao_id'] ?? null
            ];

        } catch (Exception $e) {
            error_log("Erro ao processar webhook Asaas: " . $e->getMessage());
            return ['sucesso' => false, 'erro' => $e->getMessage()];
        }
    }

    /**
     * Mapear status do Asaas para status local
     */
    public function mapearStatusAsaas($statusAsaas) {
        $mapeamento = [
            'PENDING' => 'pendente',
            'RECEIVED' => 'pago',
            'CONFIRMED' => 'pago',
            'OVERDUE' => 'vencido',
            'REFUNDED' => 'cancelado',
            'RECEIVED_IN_CASH' => 'pago',
            'REFUND_REQUESTED' => 'cancelado',
            'CHARGEBACK_REQUESTED' => 'cancelado',
            'CHARGEBACK_DISPUTE' => 'cancelado',
            'AWAITING_CHARGEBACK_REVERSAL' => 'cancelado',
            'DUNNING_REQUESTED' => 'vencido',
            'DUNNING_RECEIVED' => 'pago',
            'AWAITING_RISK_ANALYSIS' => 'processando'
        ];

        return $mapeamento[$statusAsaas] ?? 'pendente';
    }
}
