  
                            <!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de Cursos - Faciencia EAD</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --very-light-purple: #E6E6FA;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
            --text-muted: #6c757d;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
            --border-radius: 10px;
            --card-shadow: 0 6px 15px rgba(106, 90, 205, 0.1);
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        /* Sidebar Moderna */
        .sidebar {
            background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            position: relative;
            z-index: 10;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar-logo i {
            font-size: 2rem;
            margin-right: 10px;
        }

        .sidebar-logo h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section-header {
            font-size: 0.85rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.6);
            margin-left: 10px;
            margin-bottom: 15px;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding-left: 0;
            margin-bottom: 30px;
        }

        .sidebar-menu li {
            margin-bottom: 8px;
        }

        .sidebar-menu a {
            color: var(--white);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed) ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-menu a.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background-color: var(--white);
            border-radius: 0 2px 2px 0;
        }

        .sidebar-menu a i {
            margin-right: 15px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            transition: all var(--transition-speed) ease;
        }

        .sidebar-menu a:hover i {
            transform: scale(1.2);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
        }

        /* Conteúdo Principal */
        .main-content {
            background-color: var(--light-bg);
            padding: 30px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--very-light-purple);
        }

        .dashboard-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--secondary-purple);
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .polo-info {
            text-align: right;
        }

        .polo-info .polo-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--secondary-purple);
        }

        .polo-info .polo-role {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .user-avatar {
            position: relative;
            cursor: pointer;
        }

        .user-avatar img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--white);
        }

        /* Cards e Elementos de UI */
        .dashboard-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 25px;
            margin-bottom: 30px;
            transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-purple), var(--light-purple));
        }

        /* Tabelas Estilizadas */
        .custom-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom-table thead th {
            background-color: var(--very-light-purple);
            color: var(--secondary-purple);
            font-weight: 600;
            padding: 15px;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
        }

        .custom-table thead th:first-child {
            border-top-left-radius: 10px;
        }

        .custom-table thead th:last-child {
            border-top-right-radius: 10px;
        }

        .custom-table tbody tr {
            transition: all var(--transition-speed) ease;
        }

        .custom-table tbody tr:hover {
            background-color: rgba(106, 90, 205, 0.05);
            transform: scale(1.01);
        }

        .custom-table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-top: 1px solid #eee;
            font-size: 0.95rem;
        }

        .custom-table tbody tr:first-child td {
            border-top: none;
        }

        /* Cursos Grid */
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .course-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed) ease;
            display: flex;
            flex-direction: column;
            height: 100%;
            position: relative;
        }

        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(106, 90, 205, 0.15);
        }

        .course-image {
            height: 140px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .course-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
            display: flex;
            align-items: flex-end;
            padding: 15px;
        }

        .course-category {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: rgba(255, 255, 255, 0.9);
            color: var(--primary-purple);
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .course-title {
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        .course-content {
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .course-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .course-stat {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .course-stat i {
            color: var(--primary-purple);
            font-size: 1rem;
        }

        .course-description {
            font-size: 0.9rem;
            color: var(--text-muted);
            margin-bottom: 15px;
            flex: 1;
        }

        .course-actions {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .badge.bg-success {
            background-color: rgba(40, 167, 69, 0.15) !important;
            color: var(--success-green);
        }

        .badge.bg-warning {
            background-color: rgba(255, 193, 7, 0.15) !important;
            color: #d18700;
        }

        .badge.bg-danger {
            background-color: rgba(220, 53, 69, 0.15) !important;
            color: var(--danger-red);
        }

        .badge.bg-info {
            background-color: rgba(23, 162, 184, 0.15) !important;
            color: var(--info-blue);
        }

        .badge.bg-secondary {
            background-color: rgba(108, 117, 125, 0.15) !important;
            color: var(--text-muted);
        }

        /* Botões e Ações */
        .btn-group .btn {
            padding: 6px 12px;
            border-radius: 8px;
            margin-right: 5px;
            transition: all var(--transition-speed) ease;
        }

        .btn-outline-primary {
            color: var(--primary-purple);
            border-color: var(--primary-purple);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-purple);
            color: white;
        }

        .btn-outline-danger {
            color: var(--danger-red);
            border-color: var(--danger-red);
        }

        .btn-outline-danger:hover {
            background-color: var(--danger-red);
            color: white;
        }

        .btn-primary {
            background-color: var(--primary-purple);
            border-color: var(--primary-purple);
        }

        .btn-primary:hover {
            background-color: var(--secondary-purple);
            border-color: var(--secondary-purple);
        }

        .btn-sm {
            font-size: 0.85rem;
        }

        /* Filtros e Pesquisa */
        .filters-bar {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
        }

        .search-box {
            flex-grow: 1;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border-radius: 50px;
            border: 1px solid #eee;
            background-color: var(--white);
            transition: all var(--transition-speed) ease;
        }

        .search-box input:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--light-purple);
            border-color: var(--light-purple);
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .filters-dropdown {
            min-width: 160px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-icon {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Mobile Responsiveness */
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                height: 100vh;
                width: 280px;
                z-index: 1000;
                transition: left var(--transition-speed) ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 20px 15px;
            }
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .user-info {
                align-self: flex-end;
            }

            .filters-bar {
                flex-direction: column;
            }

            .action-buttons {
                width: 100%;
            }

            .action-buttons .btn {
                flex: 1;
            }

            .courses-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-grid">
        <!-- Sidebar Aprimorada -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-graduation-cap"></i>
                <h2>Faciencia</h2>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Principal</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> Painel Geral
                        </a>
                    </li>
                    <li>
                        <a href="polo_cursos.html" class="active">
                            <i class="fas fa-book-open"></i> Cursos
                        </a>
                    </li>
                    <li>
                        <a href="polo_aluno.html">
                            <i class="fas fa-users"></i> Alunos
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Gestão</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_matriculas.html">
                            <i class="fas fa-user-plus"></i> Matrículas
                        </a>
                    </li>
                    <li>
                        <a href="polo_certificados.html">
                            <i class="fas fa-certificate"></i> Certificados
                        </a>
                    </li>
                    <li>
                        <a href="polo_relatorio.html">
                            <i class="fas fa-chart-bar"></i> Relatórios
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Sistema</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_config.html">
                            <i class="fas fa-cogs"></i> Configurações
                        </a>
                    </li>
                    <li>
                        <a href="polo_suporte.html">
                            <i class="fas fa-headset"></i> Suporte
                        </a>
                    </li>
                    <li>
                        <a href="index.html" class="text-danger">
                            <i class="fas fa-sign-out-alt"></i> Sair
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <p>Faciencia EAD © 2024</p>
                <small>Versão 2.5.3</small>
            </div>
        </aside>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Cabeçalho do Painel -->
            <header class="dashboard-header">
                <h1>Gerenciamento de Cursos</h1>
                <div class="user-info">
                    <div class="polo-info">
                        <div class="polo-name">Polo São Paulo</div>
                        <div class="polo-role">Administrador</div>
                    </div>
                    <div class="user-avatar">
                        <img src="/api/placeholder/50/50" alt="Foto de Perfil">
                        <span class="notification-badge">3</span>
                    </div>
                </div>
            </header>

            <!-- Filtros e Barra de Ações -->
            <section class="filters-bar">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" class="form-control" placeholder="Buscar cursos...">
                </div>
                
                <select class="form-select filters-dropdown">
                    <option selected>Todas as Categorias</option>
                    <option>Tecnologia</option>
                    <option>Marketing</option>
                    <option>Negócios</option>
                    <option>Design</option>
                    <option>Idiomas</option>
                </select>
                
                <select class="form-select filters-dropdown">
                    <option selected>Todos os Status</option>
                    <option>Ativo</option>
                    <option>Em Breve</option>
                    <option>Inativo</option>
                    <option>Arquivado</option>
                </select>
                
                <div class="action-buttons">
                    <button class="btn btn-primary btn-icon">
                        <i class="fas fa-plus"></i> Novo Curso
                    </button>
                    <button class="btn btn-outline-primary btn-icon">
                        <i class="fas fa-file-export"></i> Exportar
                    </button>
                </div>
            </section>

            <!-- Resumo de Cursos -->
            <section class="dashboard-card">
                <div class="row mb-4">
                    <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                        <div class="p-3 bg-light rounded text-center">
                            <h3 class="text-primary mb-1">15</h3>
                            <p class="mb-0 text-muted">Total de Cursos</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                        <div class="p-3 bg-light rounded text-center">
                            <h3 class="text-success mb-1">12</h3>
                            <p class="mb-0 text-muted">Cursos Ativos</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                        <div class="p-3 bg-light rounded text-center">
                            <h3 class="text-info mb-1">3</h3>
                            <p class="mb-0 text-muted">Em Lançamento</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="p-3 bg-light rounded text-center">
                            <h3 class="text-secondary mb-1">423</h3>
                            <p class="mb-0 text-muted">Alunos Matriculados</p>
                        </div>
                    </div>
                </div>

                <!-- Tabela de Cursos -->
                <div class="table-responsive">
                    <table class="table custom-table">
                        <thead>
                            <tr>
                                <th style="width: 40%">Curso</th>
                                <th>Categoria</th>
                                <th>Alunos</th>
                                <th>Avaliação</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/60/40" class="rounded me-3" alt="Thumbnail">
                                        <div>
                                            <div class="fw-bold">Marketing Digital Avançado</div>
                                            <div class="small text-muted">Prof. Roberto Andrade</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Marketing</td>
                                <td>82</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">4.8</span>
                                        <div>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star-half-alt text-warning"></i>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="badge bg-success">Ativo</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-danger" title="Desativar">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/60/40" class="rounded me-3" alt="Thumbnail">
                                        <div>
                                            <div class="fw-bold">UX/UI Design</div>
                                            <div class="small text-muted">Profa. Mariana Fernandes</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Design</td>
                                <td>58</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">4.7</span>
                                        <div>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star-half-alt text-warning"></i>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="badge bg-success">Ativo</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-danger" title="Desativar">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/60/40" class="rounded me-3" alt="Thumbnail">
                                        <div>
                                            <div class="fw-bold">Inglês para Negócios</div>
                                            <div class="small text-muted">Prof. Richard Thompson</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Idiomas</td>
                                <td>43</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">4.5</span>
                                        <div>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star text-warning"></i>
                                            <i class="fas fa-star-half-alt text-warning"></i>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="badge bg-success">Ativo</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-danger" title="Desativar">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/60/40" class="rounded me-3" alt="Thumbnail">
                                        <div>
                                            <div class="fw-bold">Data Science na Prática</div>
                                            <div class="small text-muted">Profa. Amanda Oliveira</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Tecnologia</td>
                                <td>0</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">-</span>
                                        <div class="text-muted">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="badge bg-info">Em Breve</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-danger" title="Desativar">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/60/40" class="rounded me-3" alt="Thumbnail">
                                        <div>
                                            <div class="fw-bold">Gestão Ágil de Projetos</div>
                                            <div class="small text-muted">Prof. Pedro Martins</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Negócios</td>
                                <td>0</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">-</span>
                                        <div class="text-muted">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                </td>
                                <td><span class="badge bg-info">Em Breve</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-danger" title="Desativar">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>Mostrando 7 de 15 cursos</div>
                    <nav>
                        <ul class="pagination">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">Próximo</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </section>

            <!-- Cursos em Destaque (Visualização de Cards) -->
            <section class="dashboard-card">
                <h4 class="mb-4">Cursos em Destaque</h4>
                
                <div class="courses-grid">
                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h5 class="course-title">Marketing Digital Avançado</h5>
                            </div>
                            <span class="course-category">Marketing</span>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-users"></i>
                                    <span>82 alunos</span>
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.8 (56 avaliações)</span>
                                </div>
                            </div>
                            <div class="course-description">
                                <p>Curso completo de estratégias avançadas para marketing em plataformas digitais com foco em resultados e ROI.</p>
                            </div>
                            <div class="course-actions">
                                <span class="badge bg-success">Ativo</span>
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> Detalhes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h5 class="course-title">Desenvolvimento Web Full Stack</h5>
                            </div>
                            <span class="course-category">Tecnologia</span>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-users"></i>
                                    <span>65 alunos</span>
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (42 avaliações)</span>
                                </div>
                            </div>
                            <div class="course-description">
                                <p>Formação completa para desenvolvedores front-end e back-end com projetos práticos e tecnologias atuais.</p>
                            </div>
                            <div class="course-actions">
                                <span class="badge bg-success">Ativo</span>
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> Detalhes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h5 class="course-title">UX/UI Design</h5>
                            </div>
                            <span class="course-category">Design</span>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-users"></i>
                                    <span>58 alunos</span>
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.7 (38 avaliações)</span>
                                </div>
                            </div>
                            <div class="course-description">
                                <p>Aprenda a criar experiências digitais centradas no usuário com foco em usabilidade, acessibilidade e design de interfaces.</p>
                            </div>
                            <div class="course-actions">
                                <span class="badge bg-success">Ativo</span>
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> Detalhes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Próximos Lançamentos -->
            <section class="dashboard-card">
                <h4 class="mb-4">Próximos Lançamentos</h4>
                
                <div class="courses-grid">
                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h5 class="course-title">Data Science na Prática</h5>
                            </div>
                            <span class="course-category">Tecnologia</span>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Lançamento: 15/06/2024</span>
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-clock"></i>
                                    <span>120 horas</span>
                                </div>
                            </div>
                            <div class="course-description">
                                <p>Do básico ao avançado em análise de dados, machine learning e visualização com Python, R e ferramentas de BI.</p>
                            </div>
                            <div class="course-actions">
                                <span class="badge bg-info">Em Breve</span>
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> Detalhes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h5 class="course-title">Gestão Ágil de Projetos</h5>
                            </div>
                            <span class="course-category">Negócios</span>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Lançamento: 22/06/2024</span>
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-clock"></i>
                                    <span>90 horas</span>
                                </div>
                            </div>
                            <div class="course-description">
                                <p>Metodologias ágeis para gerenciamento de projetos com foco em Scrum, Kanban e práticas de liderança eficiente.</p>
                            </div>
                            <div class="course-actions">
                                <span class="badge bg-info">Em Breve</span>
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> Detalhes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="course-card">
                        <div class="course-image" style="background-image: url('/api/placeholder/300/140')">
                            <div class="course-overlay">
                                <h5 class="course-title">Inteligência Artificial para Marketing</h5>
                            </div>
                            <span class="course-category">Marketing/Tecnologia</span>
                        </div>
                        <div class="course-content">
                            <div class="course-stats">
                                <div class="course-stat">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Lançamento: 30/06/2024</span>
                                </div>
                                <div class="course-stat">
                                    <i class="fas fa-clock"></i>
                                    <span>80 horas</span>
                                </div>
                            </div>
                            <div class="course-description">
                                <p>Como utilizar IA e automação para otimizar campanhas de marketing, segmentação de público e análise de dados.</p>
                            </div>
                            <div class="course-actions">
                                <span class="badge bg-info">Em Breve</span>
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> Detalhes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Bootstrap JS Bundle com Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Script para Mobile Sidebar Toggle -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Adicionar botão para mobile
            const mobileToggle = document.createElement('button');
            mobileToggle.classList.add('btn', 'btn-primary', 'd-md-none', 'position-fixed');
            mobileToggle.style.top = '10px';
            mobileToggle.style.left = '10px';
            mobileToggle.style.zIndex = '1001';
            mobileToggle.innerHTML = '<i class="fas fa-bars"></i>';
            document.body.appendChild(mobileToggle);
            
            // Adicionar funcionalidade de toggle
            mobileToggle.addEventListener('click', function() {
                const sidebar = document.querySelector('.sidebar');
                sidebar.classList.toggle('show');
            });
            
            // Fechar sidebar ao clicar fora dela (em mobile)
            document.addEventListener('click', function(event) {
                const sidebar = document.querySelector('.sidebar');
                const mobileToggle = document.querySelector('.btn-primary.d-md-none');
                
                if (!sidebar.contains(event.target) && event.target !== mobileToggle) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>>
                          