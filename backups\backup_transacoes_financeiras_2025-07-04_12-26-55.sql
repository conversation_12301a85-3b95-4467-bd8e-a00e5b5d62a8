-- Backup da tabela transacoes_financeiras em 2025-07-04_12-26-55
-- Total de registros: 42

INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('1', 'receita', 'Mensalidade - <PERSON>', '1500.00', '2025-07-01', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('2', 'receita', 'Mensalidade - <PERSON>', '1500.00', '2025-07-02', NULL, '1', NULL, 'cartao', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('3', 'receita', 'Mensalidade - Pedro Costa', '1500.00', '2025-07-03', NULL, '2', NULL, 'boleto', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('4', 'receita', 'Venda de curso online', '800.00', '2025-06-15', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('5', 'receita', 'Consultoria educacional', '2000.00', '2025-06-20', NULL, '2', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('6', 'despesa', 'Salário Professor A', '3500.00', '2025-07-01', NULL, '1', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('7', 'despesa', 'Aluguel sede', '2800.00', '2025-07-05', NULL, '2', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('8', 'despesa', 'Material didático', '650.00', '2025-07-02', NULL, '1', NULL, 'cartao', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('9', 'despesa', 'Marketing digital', '450.00', '2025-06-28', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('10', 'despesa', 'Software licenças', '320.00', '2025-06-25', NULL, '2', NULL, 'boleto', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('11', 'receita', 'Mensalidades maio', '15000.00', '2025-05-15', NULL, '1', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('12', 'despesa', 'Despesas operacionais maio', '8500.00', '2025-05-20', NULL, '1', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('13', 'receita', 'Mensalidades abril', '14200.00', '2025-04-15', NULL, '2', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('14', 'despesa', 'Despesas operacionais abril', '8200.00', '2025-04-20', NULL, '2', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:48:33', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('15', 'receita', 'Mensalidade - João Silva', '1500.00', '2025-07-01', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('16', 'receita', 'Mensalidade - Maria Santos', '1500.00', '2025-07-02', NULL, '1', NULL, 'cartao', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('17', 'receita', 'Mensalidade - Pedro Costa', '1500.00', '2025-07-03', NULL, '2', NULL, 'boleto', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('18', 'receita', 'Venda de curso online', '800.00', '2025-06-15', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('19', 'receita', 'Consultoria educacional', '2000.00', '2025-06-20', NULL, '2', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('20', 'despesa', 'Salário Professor A', '3500.00', '2025-07-01', NULL, '1', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('21', 'despesa', 'Aluguel sede', '2800.00', '2025-07-05', NULL, '2', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('22', 'despesa', 'Material didático', '650.00', '2025-07-02', NULL, '1', NULL, 'cartao', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('23', 'despesa', 'Marketing digital', '450.00', '2025-06-28', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('24', 'despesa', 'Software licenças', '320.00', '2025-06-25', NULL, '2', NULL, 'boleto', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('25', 'receita', 'Mensalidades maio', '15000.00', '2025-05-15', NULL, '1', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('26', 'despesa', 'Despesas operacionais maio', '8500.00', '2025-05-20', NULL, '1', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('27', 'receita', 'Mensalidades abril', '14200.00', '2025-04-15', NULL, '2', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('28', 'despesa', 'Despesas operacionais abril', '8200.00', '2025-04-20', NULL, '2', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:10', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('29', 'receita', 'Mensalidade - João Silva', '1500.00', '2025-07-01', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('30', 'receita', 'Mensalidade - Maria Santos', '1500.00', '2025-07-02', NULL, '1', NULL, 'cartao', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('31', 'receita', 'Mensalidade - Pedro Costa', '1500.00', '2025-07-03', NULL, '2', NULL, 'boleto', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('32', 'receita', 'Venda de curso online', '800.00', '2025-06-15', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('33', 'receita', 'Consultoria educacional', '2000.00', '2025-06-20', NULL, '2', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('34', 'despesa', 'Salário Professor A', '3500.00', '2025-07-01', NULL, '1', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('35', 'despesa', 'Aluguel sede', '2800.00', '2025-07-05', NULL, '2', NULL, 'transferencia', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('36', 'despesa', 'Material didático', '650.00', '2025-07-02', NULL, '1', NULL, 'cartao', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('37', 'despesa', 'Marketing digital', '450.00', '2025-06-28', NULL, '1', NULL, 'pix', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('38', 'despesa', 'Software licenças', '320.00', '2025-06-25', NULL, '2', NULL, 'boleto', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('39', 'receita', 'Mensalidades maio', '15000.00', '2025-05-15', NULL, '1', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('40', 'despesa', 'Despesas operacionais maio', '8500.00', '2025-05-20', NULL, '1', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('41', 'receita', 'Mensalidades abril', '14200.00', '2025-04-15', NULL, '2', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
INSERT INTO transacoes_financeiras (id, tipo, descricao, valor, data_transacao, categoria_id, conta_bancaria_id, conta_destino_id, forma_pagamento, referencia_tipo, referencia_id, status, observacoes, comprovante_path, usuario_id, created_at, updated_at) VALUES ('42', 'despesa', 'Despesas operacionais abril', '8200.00', '2025-04-20', NULL, '2', NULL, 'multiplos', NULL, NULL, 'efetivada', NULL, NULL, NULL, '2025-07-03 13:50:54', NULL);
