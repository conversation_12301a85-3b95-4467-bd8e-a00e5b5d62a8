<?php
/**
 * CONFIGURAÇÃO AUTOMÁTICA DO MÓDULO FINANCEIRO
 * 
 * Este arquivo deve ser incluído no início de TODAS as páginas do módulo financeiro
 * para garantir sincronização automática e configurações corretas.
 */

// Evitar execução múltipla
if (defined('FINANCEIRO_CONFIG_LOADED')) {
    return;
}
define('FINANCEIRO_CONFIG_LOADED', true);

// Configurações do módulo financeiro
define('FINANCEIRO_AUTO_SYNC', true);           // Ativar sincronização automática
define('FINANCEIRO_CACHE_TIME', 300);          // Cache de 5 minutos
define('FINANCEIRO_DEBUG_MODE', false);        // Modo debug (logs detalhados)
define('FINANCEIRO_FORCE_SYNC_POST', true);    // Forçar sync em operações POST

/**
 * Classe de configuração do módulo financeiro
 */
class FinanceiroConfig {
    private static $instance = null;
    private $db;
    private $sync_enabled = true;
    
    private function __construct($database) {
        $this->db = $database;
        $this->initialize();
    }
    
    public static function getInstance($database = null) {
        if (self::$instance === null) {
            self::$instance = new self($database);
        }
        return self::$instance;
    }
    
    /**
     * Inicializa configurações do módulo
     */
    private function initialize() {
        // Verificar se as tabelas necessárias existem
        $this->checkRequiredTables();
        
        // Incluir sincronização automática se habilitada
        if (FINANCEIRO_AUTO_SYNC && $this->sync_enabled) {
            $this->loadAutoSync();
        }
        
        // Configurar hooks de operações
        $this->setupOperationHooks();
        
        // Configurar tratamento de erros
        $this->setupErrorHandling();
    }
    
    /**
     * Verifica tabelas necessárias
     */
    private function checkRequiredTables() {
        $required_tables = [
            'contas_pagar',
            'contas_receber', 
            'contas_bancarias',
            'transacoes_financeiras'
        ];
        
        foreach ($required_tables as $table) {
            if (!$this->tableExists($table)) {
                $this->createMissingTable($table);
            }
        }
    }
    
    /**
     * Verifica se tabela existe
     */
    private function tableExists($table_name) {
        try {
            $result = $this->db->fetchOne("SHOW TABLES LIKE ?", [$table_name]);
            return $result !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Cria tabelas faltantes
     */
    private function createMissingTable($table_name) {
        try {
            switch ($table_name) {
                case 'transacoes_financeiras':
                    $this->db->query("
                        CREATE TABLE IF NOT EXISTS transacoes_financeiras (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            tipo ENUM('receita', 'despesa') NOT NULL,
                            descricao TEXT NOT NULL,
                            valor DECIMAL(10,2) NOT NULL,
                            data_transacao DATE NOT NULL,
                            conta_bancaria_id INT,
                            categoria_id INT,
                            referencia_tipo VARCHAR(50),
                            referencia_id INT,
                            usuario_id INT DEFAULT 1,
                            status ENUM('pendente', 'efetivada', 'cancelada') DEFAULT 'efetivada',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_tipo (tipo),
                            INDEX idx_data (data_transacao),
                            INDEX idx_conta (conta_bancaria_id),
                            INDEX idx_referencia (referencia_tipo, referencia_id)
                        )
                    ");
                    break;
                    
                case 'log_sincronizacao_financeira':
                    $this->db->query("
                        CREATE TABLE IF NOT EXISTS log_sincronizacao_financeira (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            tabela_origem VARCHAR(50) NOT NULL,
                            operacao VARCHAR(20) NOT NULL,
                            registro_id INT NOT NULL,
                            valor DECIMAL(10,2) NOT NULL,
                            descricao TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_tabela_origem (tabela_origem),
                            INDEX idx_created_at (created_at)
                        )
                    ");
                    break;
            }
            
            if (FINANCEIRO_DEBUG_MODE) {
                error_log("Tabela $table_name criada automaticamente");
            }
            
        } catch (Exception $e) {
            error_log("Erro ao criar tabela $table_name: " . $e->getMessage());
        }
    }
    
    /**
     * Carrega sincronização automática
     */
    private function loadAutoSync() {
        $auto_sync_file = __DIR__ . '/auto_sync.php';
        if (file_exists($auto_sync_file)) {
            require_once $auto_sync_file;
        }
    }
    
    /**
     * Configura hooks de operações
     */
    private function setupOperationHooks() {
        // Hook para operações de pagamento
        if (isset($_POST['acao']) && in_array($_POST['acao'], ['pagar_conta', 'receber_conta', 'gerar_boleto'])) {
            $this->processOperationHook($_POST['acao']);
        }
        
        // Hook para operações via GET
        if (isset($_GET['action']) && in_array($_GET['action'], ['pay', 'receive', 'sync'])) {
            $this->processOperationHook($_GET['action']);
        }
    }
    
    /**
     * Processa hooks de operações
     */
    private function processOperationHook($action) {
        try {
            switch ($action) {
                case 'pagar_conta':
                case 'pay':
                    $this->onContaPaga();
                    break;
                    
                case 'receber_conta':
                case 'receive':
                    $this->onContaRecebida();
                    break;
                    
                case 'gerar_boleto':
                    $this->onBoletoGerado();
                    break;
                    
                case 'sync':
                    $this->forceSync();
                    break;
            }
        } catch (Exception $e) {
            error_log("Erro no hook de operação $action: " . $e->getMessage());
        }
    }
    
    /**
     * Hook executado quando conta é paga
     */
    private function onContaPaga() {
        if (isset($_POST['conta_id']) && isset($_POST['valor_pago'])) {
            $this->syncContaPagar($_POST['conta_id'], $_POST['valor_pago']);
        }
    }
    
    /**
     * Hook executado quando conta é recebida
     */
    private function onContaRecebida() {
        if (isset($_POST['conta_id'])) {
            $this->syncContaReceber($_POST['conta_id']);
        }
    }
    
    /**
     * Hook executado quando boleto é gerado
     */
    private function onBoletoGerado() {
        // Sincronizar após geração de boleto
        $this->quickSync();
    }
    
    /**
     * Sincroniza conta a pagar específica
     */
    private function syncContaPagar($conta_id, $valor_pago) {
        try {
            $conta = $this->db->fetchOne("SELECT * FROM contas_pagar WHERE id = ?", [$conta_id]);
            if (!$conta) return;
            
            // Verificar se já tem transação
            $transacao_existe = $this->db->fetchOne("
                SELECT id FROM transacoes_financeiras 
                WHERE referencia_tipo = 'conta_pagar' AND referencia_id = ?
            ", [$conta_id]);
            
            if (!$transacao_existe) {
                // Criar transação financeira
                $this->db->query("
                    INSERT INTO transacoes_financeiras 
                    (tipo, descricao, valor, data_transacao, conta_bancaria_id,
                     categoria_id, referencia_tipo, referencia_id, usuario_id, status, created_at)
                    VALUES ('despesa', ?, ?, NOW(), ?, 1, 'conta_pagar', ?, 1, 'efetivada', NOW())
                ", [
                    $conta['descricao'],
                    $valor_pago,
                    $conta['conta_bancaria_id'] ?: 1,
                    $conta_id
                ]);
                
                // Atualizar saldo bancário
                $this->db->query("
                    UPDATE contas_bancarias 
                    SET saldo_atual = saldo_atual - ?,
                        data_ultima_sincronizacao = NOW()
                    WHERE id = ?
                ", [$valor_pago, $conta['conta_bancaria_id'] ?: 1]);
            }
            
        } catch (Exception $e) {
            error_log("Erro ao sincronizar conta a pagar: " . $e->getMessage());
        }
    }
    
    /**
     * Sincroniza conta a receber específica
     */
    private function syncContaReceber($conta_id) {
        try {
            $conta = $this->db->fetchOne("SELECT * FROM contas_receber WHERE id = ?", [$conta_id]);
            if (!$conta) return;
            
            // Verificar se já tem transação
            $transacao_existe = $this->db->fetchOne("
                SELECT id FROM transacoes_financeiras 
                WHERE referencia_tipo = 'conta_receber' AND referencia_id = ?
            ", [$conta_id]);
            
            if (!$transacao_existe && $conta['status'] == 'recebido') {
                // Criar transação financeira
                $this->db->query("
                    INSERT INTO transacoes_financeiras 
                    (tipo, descricao, valor, data_transacao, conta_bancaria_id,
                     categoria_id, referencia_tipo, referencia_id, usuario_id, status, created_at)
                    VALUES ('receita', ?, ?, ?, ?, 1, 'conta_receber', ?, 1, 'efetivada', NOW())
                ", [
                    $conta['descricao'],
                    $conta['valor'],
                    $conta['data_recebimento'] ?: date('Y-m-d'),
                    $conta['conta_bancaria_id'] ?: 1,
                    $conta_id
                ]);
                
                // Atualizar saldo bancário
                $this->db->query("
                    UPDATE contas_bancarias 
                    SET saldo_atual = saldo_atual + ?,
                        data_ultima_sincronizacao = NOW()
                    WHERE id = ?
                ", [$conta['valor'], $conta['conta_bancaria_id'] ?: 1]);
            }
            
        } catch (Exception $e) {
            error_log("Erro ao sincronizar conta a receber: " . $e->getMessage());
        }
    }
    
    /**
     * Sincronização rápida
     */
    private function quickSync() {
        try {
            // Incluir auto_sync se não foi carregado
            if (!class_exists('AutoSync')) {
                $this->loadAutoSync();
            }
            
            // Forçar sincronização
            if (class_exists('AutoSync')) {
                $auto_sync = new AutoSync($this->db);
            }
            
        } catch (Exception $e) {
            error_log("Erro na sincronização rápida: " . $e->getMessage());
        }
    }
    
    /**
     * Força sincronização completa
     */
    private function forceSync() {
        $this->quickSync();
    }
    
    /**
     * Configura tratamento de erros
     */
    private function setupErrorHandling() {
        // Configurar log de erros específico do módulo financeiro
        if (FINANCEIRO_DEBUG_MODE) {
            ini_set('log_errors', 1);
            ini_set('error_log', __DIR__ . '/../logs/financeiro_errors.log');
        }
    }
    
    /**
     * Obtém indicadores sincronizados
     */
    public function getIndicadoresSincronizados() {
        try {
            return $this->db->fetchOne("
                SELECT 
                    -- Receitas do mês atual
                    (SELECT COALESCE(SUM(valor), 0) FROM contas_receber 
                     WHERE status = 'recebido' AND MONTH(COALESCE(data_recebimento, data_vencimento)) = MONTH(CURDATE()) 
                     AND YEAR(COALESCE(data_recebimento, data_vencimento)) = YEAR(CURDATE())) as receitas_mes_atual,
                    
                    -- Despesas do mês atual
                    (SELECT COALESCE(SUM(valor), 0) FROM contas_pagar 
                     WHERE status = 'pago' AND MONTH(COALESCE(data_pagamento, data_vencimento)) = MONTH(CURDATE()) 
                     AND YEAR(COALESCE(data_pagamento, data_vencimento)) = YEAR(CURDATE())) as despesas_mes_atual,
                    
                    -- Saldo total das contas bancárias
                    (SELECT COALESCE(SUM(saldo_atual), 0) FROM contas_bancarias 
                     WHERE status = 'ativo' OR status IS NULL) as saldo_total_bancos,
                    
                    -- Contas pendentes
                    (SELECT COALESCE(SUM(valor), 0) FROM contas_receber 
                     WHERE status = 'pendente') as total_receber,
                    (SELECT COALESCE(SUM(valor), 0) FROM contas_pagar 
                     WHERE status = 'pendente') as total_pagar,
                     
                    -- Última sincronização
                    (SELECT MAX(data_ultima_sincronizacao) FROM contas_bancarias) as ultima_sincronizacao
            ");
            
        } catch (Exception $e) {
            error_log("Erro ao obter indicadores sincronizados: " . $e->getMessage());
            return [
                'receitas_mes_atual' => 0,
                'despesas_mes_atual' => 0,
                'saldo_total_bancos' => 0,
                'total_receber' => 0,
                'total_pagar' => 0,
                'ultima_sincronizacao' => null
            ];
        }
    }
    
    /**
     * Verifica se sistema está sincronizado
     */
    public function isSincronizado() {
        try {
            // Verificar se há operações pendentes de sincronização
            $pendencias = $this->db->fetchOne("
                SELECT 
                    (SELECT COUNT(*) FROM contas_pagar cp
                     WHERE cp.status = 'pago' AND cp.data_pagamento IS NOT NULL
                     AND NOT EXISTS (
                         SELECT 1 FROM transacoes_financeiras tf 
                         WHERE tf.referencia_tipo = 'conta_pagar' AND tf.referencia_id = cp.id
                     )) +
                    (SELECT COUNT(*) FROM contas_receber cr
                     WHERE cr.status = 'recebido' AND cr.data_recebimento IS NOT NULL
                     AND NOT EXISTS (
                         SELECT 1 FROM transacoes_financeiras tf 
                         WHERE tf.referencia_tipo = 'conta_receber' AND tf.referencia_id = cr.id
                     )) as total_pendencias
            ");
            
            return $pendencias['total_pendencias'] == 0;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Obtém status da sincronização
     */
    public function getStatusSincronizacao() {
        $sincronizado = $this->isSincronizado();
        $indicadores = $this->getIndicadoresSincronizados();
        
        return [
            'sincronizado' => $sincronizado,
            'ultima_sincronizacao' => $indicadores['ultima_sincronizacao'],
            'status' => $sincronizado ? 'OK' : 'PENDENTE',
            'cor' => $sincronizado ? 'green' : 'orange'
        ];
    }
}

// Inicializar configuração automática se o banco estiver disponível
if (isset($db) && $db) {
    try {
        $financeiro_config = FinanceiroConfig::getInstance($db);
    } catch (Exception $e) {
        error_log("Erro ao inicializar configuração do módulo financeiro: " . $e->getMessage());
    }
}

// Função helper para obter indicadores sincronizados
function getIndicadoresFinanceiros() {
    global $financeiro_config;
    if ($financeiro_config) {
        return $financeiro_config->getIndicadoresSincronizados();
    }
    return [];
}

// Função helper para verificar status de sincronização
function getStatusSincronizacao() {
    global $financeiro_config;
    if ($financeiro_config) {
        return $financeiro_config->getStatusSincronizacao();
    }
    return ['sincronizado' => false, 'status' => 'ERRO', 'cor' => 'red'];
}
?>
