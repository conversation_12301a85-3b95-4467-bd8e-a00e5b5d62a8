-- ============================================================================
-- ROLLBACK - SISTEMA DE ROXINHOS
-- ============================================================================
-- Script para reverter as mudanças do sistema de roxinhos em caso de problemas
-- ATENÇÃO: Este script remove TODOS os dados do sistema de roxinhos
-- Use apenas em caso de emergência!
-- ============================================================================

SELECT 'INICIANDO ROLLBACK DO SISTEMA DE ROXINHOS' as status;
SELECT 'ATENÇÃO: Este processo irá remover TODOS os dados!' as aviso;

-- Confirmar que queremos fazer o rollback
-- Descomente a linha abaixo apenas se tiver certeza
-- SET @confirmar_rollback = 'SIM_TENHO_CERTEZA';

-- Verificar confirmação
SELECT 
    CASE 
        WHEN @confirmar_rollback = 'SIM_TENHO_CERTEZA' 
        THEN 'Rollback autorizado - prosseguindo...' 
        ELSE 'Rollback NÃO autorizado - defina @confirmar_rollback = "SIM_TENHO_CERTEZA" para prosseguir' 
    END as status_confirmacao;

-- ============================================================================
-- 1. REMOVER TRIGGERS (se confirmado)
-- ============================================================================

DROP TRIGGER IF EXISTS `tr_roxinho_historico_insert`;
DROP TRIGGER IF EXISTS `tr_roxinho_historico_update`;
DROP TRIGGER IF EXISTS `tr_roxinho_comissao_historico`;
DROP TRIGGER IF EXISTS `tr_roxinho_pagamento_historico`;

SELECT 'Triggers removidos' as status;

-- ============================================================================
-- 2. REMOVER VIEWS (se confirmado)
-- ============================================================================

DROP VIEW IF EXISTS `vw_roxinho_comissoes_detalhadas`;
DROP VIEW IF EXISTS `vw_roxinho_resumo`;

SELECT 'Views removidas' as status;

-- ============================================================================
-- 3. REMOVER TABELAS (se confirmado e se @confirmar_rollback estiver definido)
-- ============================================================================

-- Remover tabelas na ordem correta (dependências primeiro)
SET @sql_drop_pagamentos = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'DROP TABLE IF EXISTS `roxinho_pagamentos`', 'SELECT "Rollback não confirmado - tabela roxinho_pagamentos mantida" as status');
PREPARE stmt FROM @sql_drop_pagamentos;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_drop_historico = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'DROP TABLE IF EXISTS `roxinho_historico`', 'SELECT "Rollback não confirmado - tabela roxinho_historico mantida" as status');
PREPARE stmt FROM @sql_drop_historico;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_drop_comissoes = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'DROP TABLE IF EXISTS `roxinho_comissoes`', 'SELECT "Rollback não confirmado - tabela roxinho_comissoes mantida" as status');
PREPARE stmt FROM @sql_drop_comissoes;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_drop_polos = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'DROP TABLE IF EXISTS `roxinho_polos`', 'SELECT "Rollback não confirmado - tabela roxinho_polos mantida" as status');
PREPARE stmt FROM @sql_drop_polos;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- 4. REVERTER TABELA ROXINHOS PARA ESTRUTURA ORIGINAL (se confirmado)
-- ============================================================================

-- Remover colunas adicionadas (se confirmado)
SET @sql_remove_observacoes = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `observacoes`', 'SELECT "Rollback não confirmado - coluna observacoes mantida" as status');
PREPARE stmt FROM @sql_remove_observacoes;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_remove_taxa_padrao = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `taxa_padrao`', 'SELECT "Rollback não confirmado - coluna taxa_padrao mantida" as status');
PREPARE stmt FROM @sql_remove_taxa_padrao;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_remove_pix = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `pix`', 'SELECT "Rollback não confirmado - coluna pix mantida" as status');
PREPARE stmt FROM @sql_remove_pix;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_remove_conta = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `conta`', 'SELECT "Rollback não confirmado - coluna conta mantida" as status');
PREPARE stmt FROM @sql_remove_conta;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_remove_agencia = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `agencia`', 'SELECT "Rollback não confirmado - coluna agencia mantida" as status');
PREPARE stmt FROM @sql_remove_agencia;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_remove_banco = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `banco`', 'SELECT "Rollback não confirmado - coluna banco mantida" as status');
PREPARE stmt FROM @sql_remove_banco;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_remove_endereco = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `endereco`', 'SELECT "Rollback não confirmado - coluna endereco mantida" as status');
PREPARE stmt FROM @sql_remove_endereco;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql_remove_updated_at = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` DROP COLUMN IF EXISTS `updated_at`', 'SELECT "Rollback não confirmado - coluna updated_at mantida" as status');
PREPARE stmt FROM @sql_remove_updated_at;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Reverter enum do status (se confirmado)
SET @sql_revert_status = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 'ALTER TABLE `roxinhos` MODIFY COLUMN `status` ENUM("ativo", "inativo") DEFAULT "ativo"', 'SELECT "Rollback não confirmado - enum status mantido" as status');
PREPARE stmt FROM @sql_revert_status;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- 5. REMOVER DADOS DE EXEMPLO (se confirmado)
-- ============================================================================

-- Remover dados de exemplo inseridos pelo sistema
SET @sql_remove_exemplos = IF(@confirmar_rollback = 'SIM_TENHO_CERTEZA', 
    'DELETE FROM `roxinhos` WHERE cpf IN ("123.456.789-01", "987.654.321-02", "456.789.123-03")', 
    'SELECT "Rollback não confirmado - dados de exemplo mantidos" as status'
);
PREPARE stmt FROM @sql_remove_exemplos;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- 6. VERIFICAÇÃO FINAL
-- ============================================================================

SELECT 'VERIFICANDO ESTADO APÓS ROLLBACK...' as status;

-- Verificar tabelas restantes
SELECT 
    TABLE_NAME as tabela_restante
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE '%roxinho%';

-- Verificar estrutura da tabela roxinhos após rollback
SELECT 
    COLUMN_NAME as coluna_restante
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'roxinhos'
ORDER BY ORDINAL_POSITION;

-- Verificar views restantes
SELECT 
    TABLE_NAME as view_restante
FROM information_schema.VIEWS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE '%roxinho%';

-- Verificar triggers restantes
SELECT 
    TRIGGER_NAME as trigger_restante
FROM information_schema.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE() 
AND TRIGGER_NAME LIKE '%roxinho%';

SELECT 
    CASE 
        WHEN @confirmar_rollback = 'SIM_TENHO_CERTEZA' 
        THEN 'ROLLBACK CONCLUÍDO! Sistema de Roxinhos removido.' 
        ELSE 'ROLLBACK NÃO EXECUTADO - Confirmação necessária' 
    END as status_final;

-- Limpar variável
SET @confirmar_rollback = NULL;
