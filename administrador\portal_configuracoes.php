<?php
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

require_once '../includes/Database.php';

$db = Database::getInstance();
$message = '';
$messageType = '';

// Processar configurações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'limpar_logs':
                $dias = (int)$_POST['dias_logs'];
                $deleted = $db->query("DELETE FROM alunos_atividades WHERE created_at < DATE_SUB(NOW(), INTERVAL $dias DAY)");
                $message = "Logs mais antigos que $dias dias foram removidos!";
                $messageType = 'success';
                break;
                
            case 'resetar_tentativas':
                $db->query("UPDATE alunos_acesso SET tentativas_login = 0, bloqueado_ate = NULL");
                $message = 'Todas as tentativas de login foram resetadas!';
                $messageType = 'success';
                break;
                
            case 'backup_portal':
                // Criar backup das tabelas do portal
                $tabelas = ['alunos_acesso', 'alunos_atividades', 'mensalidades_alunos', 'notas_disciplinas', 'notificacoes'];
                $backup_file = 'backup_portal_' . date('Y-m-d_H-i-s') . '.sql';
                
                $sql_backup = "-- Backup Portal do Aluno - " . date('Y-m-d H:i:s') . "\n\n";
                
                foreach ($tabelas as $tabela) {
                    $sql_backup .= "-- Tabela: $tabela\n";
                    $sql_backup .= "DROP TABLE IF EXISTS `{$tabela}_backup`;\n";
                    $sql_backup .= "CREATE TABLE `{$tabela}_backup` LIKE `$tabela`;\n";
                    $sql_backup .= "INSERT INTO `{$tabela}_backup` SELECT * FROM `$tabela`;\n\n";
                }
                
                file_put_contents("../uploads/backup/$backup_file", $sql_backup);
                $message = "Backup criado: $backup_file";
                $messageType = 'success';
                break;
                
            case 'recriar_tabelas':
                // Executar setup das tabelas
                $setup_sql = file_get_contents('../portal_aluno/database/portal_aluno_setup.sql');
                if ($setup_sql) {
                    $statements = explode(';', $setup_sql);
                    $executed = 0;
                    
                    foreach ($statements as $statement) {
                        $statement = trim($statement);
                        if (!empty($statement)) {
                            try {
                                $db->query($statement);
                                $executed++;
                            } catch (Exception $e) {
                                // Ignorar erros de tabelas já existentes
                                if (strpos($e->getMessage(), 'already exists') === false) {
                                    throw $e;
                                }
                            }
                        }
                    }
                    
                    $message = "Executadas $executed instruções SQL para recriar tabelas!";
                    $messageType = 'success';
                } else {
                    throw new Exception('Arquivo de setup não encontrado');
                }
                break;
        }
    } catch (Exception $e) {
        $message = 'Erro: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Buscar estatísticas
$stats = [];
$stats['total_acessos'] = $db->fetch("SELECT COUNT(*) as total FROM alunos_acesso")['total'];
$stats['acessos_ativos'] = $db->fetch("SELECT COUNT(*) as total FROM alunos_acesso WHERE status = 'ativo'")['total'];
$stats['total_mensalidades'] = $db->fetch("SELECT COUNT(*) as total FROM mensalidades_alunos")['total'];
$stats['total_logs'] = $db->fetch("SELECT COUNT(*) as total FROM alunos_atividades")['total'];
$stats['logs_30_dias'] = $db->fetch("SELECT COUNT(*) as total FROM alunos_atividades WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")['total'];

// Verificar integridade das tabelas
$tabelas_portal = ['alunos_acesso', 'alunos_atividades', 'mensalidades_alunos', 'notas_disciplinas', 'notificacoes'];
$tabelas_status = [];

foreach ($tabelas_portal as $tabela) {
    try {
        $exists = $db->fetch("SHOW TABLES LIKE '$tabela'");
        if ($exists) {
            $count = $db->fetch("SELECT COUNT(*) as total FROM $tabela")['total'];
            $tabelas_status[$tabela] = ['exists' => true, 'count' => $count];
        } else {
            $tabelas_status[$tabela] = ['exists' => false, 'count' => 0];
        }
    } catch (Exception $e) {
        $tabelas_status[$tabela] = ['exists' => false, 'count' => 0, 'error' => $e->getMessage()];
    }
}

$page_title = 'Configurações do Portal';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Administrador</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'red': {
                            50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5',
                            400: '#f87171', 500: '#ef4444', 600: '#dc2626', 700: '#b91c1c',
                            800: '#991b1b', 900: '#7f1d1d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <div class="bg-red-600 text-white p-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-xl font-bold"><?php echo $page_title; ?></h1>
                <p class="text-red-100">Configurações e manutenção do portal dos alunos</p>
            </div>
            <div class="flex space-x-4">
                <a href="index.php" class="bg-red-700 hover:bg-red-800 px-4 py-2 rounded-md">
                    <i class="fas fa-arrow-left mr-2"></i>Voltar
                </a>
                <a href="portal_monitoramento.php" class="bg-red-700 hover:bg-red-800 px-4 py-2 rounded-md">
                    <i class="fas fa-chart-line mr-2"></i>Monitoramento
                </a>
            </div>
        </div>
    </div>

    <!-- Content -->
    <main class="p-6">
        <!-- Messages -->
        <?php if ($message): ?>
        <div class="mb-6 p-4 rounded-md <?php echo $messageType === 'success' ? 'bg-green-50 border border-green-200 text-green-700' : 'bg-red-50 border border-red-200 text-red-700'; ?>">
            <div class="flex items-center">
                <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                <?php echo $message; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Estatísticas do Portal -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Estatísticas do Portal</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600"><?php echo $stats['total_acessos']; ?></div>
                    <div class="text-sm text-gray-500">Total de Acessos</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600"><?php echo $stats['acessos_ativos']; ?></div>
                    <div class="text-sm text-gray-500">Acessos Ativos</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600"><?php echo $stats['total_mensalidades']; ?></div>
                    <div class="text-sm text-gray-500">Mensalidades</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600"><?php echo $stats['total_logs']; ?></div>
                    <div class="text-sm text-gray-500">Total de Logs</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600"><?php echo $stats['logs_30_dias']; ?></div>
                    <div class="text-sm text-gray-500">Logs (30 dias)</div>
                </div>
            </div>
        </div>

        <!-- Status das Tabelas -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Status das Tabelas do Portal</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($tabelas_status as $tabela => $status): ?>
                <div class="border rounded-lg p-4 <?php echo $status['exists'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'; ?>">
                    <div class="flex items-center justify-between">
                        <h3 class="font-medium text-gray-900"><?php echo $tabela; ?></h3>
                        <i class="fas <?php echo $status['exists'] ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'; ?>"></i>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">
                        <?php if ($status['exists']): ?>
                            <?php echo number_format($status['count']); ?> registros
                        <?php else: ?>
                            Tabela não existe
                        <?php endif; ?>
                    </p>
                    <?php if (isset($status['error'])): ?>
                    <p class="text-xs text-red-600 mt-1"><?php echo $status['error']; ?></p>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Ações de Manutenção -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Manutenção do Portal</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Limpar Logs -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">Limpar Logs Antigos</h3>
                    <p class="text-sm text-gray-500 mb-3">Remove logs de atividades mais antigos que X dias</p>
                    
                    <form method="POST" class="space-y-3">
                        <input type="hidden" name="action" value="limpar_logs">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Dias para manter</label>
                            <input type="number" name="dias_logs" value="90" min="30" max="365" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500">
                        </div>
                        <button type="submit" onclick="return confirm('Confirma a limpeza dos logs?')"
                                class="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-4 rounded-md">
                            <i class="fas fa-trash mr-2"></i>Limpar Logs
                        </button>
                    </form>
                </div>

                <!-- Resetar Tentativas -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">Resetar Bloqueios</h3>
                    <p class="text-sm text-gray-500 mb-3">Remove todos os bloqueios de login dos alunos</p>
                    
                    <form method="POST" class="space-y-3">
                        <input type="hidden" name="action" value="resetar_tentativas">
                        <button type="submit" onclick="return confirm('Confirma o reset de todos os bloqueios?')"
                                class="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md">
                            <i class="fas fa-unlock mr-2"></i>Resetar Bloqueios
                        </button>
                    </form>
                </div>

                <!-- Backup -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">Backup do Portal</h3>
                    <p class="text-sm text-gray-500 mb-3">Cria backup de todas as tabelas do portal</p>
                    
                    <form method="POST" class="space-y-3">
                        <input type="hidden" name="action" value="backup_portal">
                        <button type="submit" onclick="return confirm('Criar backup do portal?')"
                                class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md">
                            <i class="fas fa-download mr-2"></i>Criar Backup
                        </button>
                    </form>
                </div>

                <!-- Recriar Tabelas -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">Recriar Tabelas</h3>
                    <p class="text-sm text-gray-500 mb-3">Executa o setup completo das tabelas do portal</p>
                    
                    <form method="POST" class="space-y-3">
                        <input type="hidden" name="action" value="recriar_tabelas">
                        <button type="submit" onclick="return confirm('ATENÇÃO: Isso pode afetar dados existentes. Confirma?')"
                                class="w-full bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded-md">
                            <i class="fas fa-database mr-2"></i>Recriar Tabelas
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Links Úteis -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Links Úteis</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <a href="../portal_aluno/setup.php?key=faciencia_setup_2025" target="_blank" 
                   class="block text-center bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-md">
                    <i class="fas fa-cogs mr-2"></i>Setup do Portal
                </a>
                
                <a href="../portal_aluno/login.php" target="_blank" 
                   class="block text-center bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-md">
                    <i class="fas fa-external-link-alt mr-2"></i>Portal do Aluno
                </a>
                
                <a href="portal_monitoramento.php" 
                   class="block text-center bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-md">
                    <i class="fas fa-chart-line mr-2"></i>Monitoramento
                </a>
                
                <a href="../secretaria/acessos_alunos.php" 
                   class="block text-center bg-purple-500 hover:bg-purple-600 text-white font-medium py-3 px-4 rounded-md">
                    <i class="fas fa-users mr-2"></i>Gestão de Acessos
                </a>
            </div>
        </div>

        <!-- Informações do Sistema -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Informações do Sistema</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-2">Portal do Aluno</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li><strong>Versão:</strong> 1.0</li>
                        <li><strong>Status:</strong> <span class="text-green-600">Ativo</span></li>
                        <li><strong>Última Atualização:</strong> <?php echo date('d/m/Y'); ?></li>
                        <li><strong>Módulos:</strong> Login, Financeiro, Notas, Monitoramento</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-800 mb-2">Banco de Dados</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li><strong>Tabelas Portal:</strong> <?php echo count($tabelas_portal); ?> tabelas</li>
                        <li><strong>Registros Ativos:</strong> <?php echo $stats['total_acessos']; ?> acessos</li>
                        <li><strong>Logs Armazenados:</strong> <?php echo $stats['total_logs']; ?> registros</li>
                        <li><strong>Status:</strong> <span class="text-green-600">Conectado</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
