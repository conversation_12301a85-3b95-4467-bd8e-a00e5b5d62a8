<?php
/**
 * VIEW: Registrar Pagamento de Conta
 * Arquivo: views/contas_pagar/pagar.php
 */

if (!$conta || $conta['status'] !== 'pendente') {
    echo '<div class="text-center py-12">';
    echo '<h3 class="text-lg font-medium text-gray-900">Conta não encontrada ou já paga</h3>';
    echo '<a href="contas_pagar.php?acao=listar" class="mt-4 inline-block bg-red-600 text-white px-4 py-2 rounded-lg">Voltar à Listagem</a>';
    echo '</div>';
    return;
}

// Calcular dias em atraso
$dias_atraso = 0;
if ($conta['data_vencimento'] < date('Y-m-d')) {
    $dias_atraso = (strtotime(date('Y-m-d')) - strtotime($conta['data_vencimento'])) / (60 * 60 * 24);
}
?>

<div class="max-w-4xl mx-auto">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="contas_pagar.php?acao=dashboard" 
                   class="text-gray-700 hover:text-red-600 inline-flex items-center">
                    <i class="fas fa-home mr-2"></i>
                    Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="contas_pagar.php?acao=listar" 
                       class="text-gray-700 hover:text-red-600">
                        Contas a Pagar
                    </a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">Registrar Pagamento</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Informações da Conta -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-credit-card text-green-600 mr-2"></i>
                Registrar Pagamento
            </h2>
            <p class="text-gray-600 mt-1">Confirme os dados e informe as informações do pagamento</p>
        </div>

        <div class="p-6">
            <!-- Alerta de Atraso -->
            <?php if ($dias_atraso > 0): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-red-900">Conta Vencida</h4>
                            <p class="text-red-700">
                                Esta conta está vencida há <strong><?php echo $dias_atraso; ?> dia(s)</strong>. 
                                Verifique se há multas ou juros a serem aplicados.
                            </p>
                        </div>
                    </div>
                </div>
            <?php elseif ($conta['data_vencimento'] === date('Y-m-d')): ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-clock text-yellow-500 mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-yellow-900">Vence Hoje</h4>
                            <p class="text-yellow-700">Esta conta vence hoje. Registre o pagamento para manter o controle em dia.</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Dados da Conta -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Descrição</label>
                        <p class="mt-1 text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($conta['descricao']); ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Fornecedor</label>
                        <p class="mt-1 text-gray-900"><?php echo htmlspecialchars($conta['fornecedor_nome'] ?: 'Não informado'); ?></p>
                    </div>

                    <?php if ($conta['observacoes']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Observações</label>
                            <p class="mt-1 text-gray-600"><?php echo htmlspecialchars($conta['observacoes']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Valor Original</label>
                        <p class="mt-1 text-2xl font-bold text-gray-900">
                            R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Data de Vencimento</label>
                        <p class="mt-1 text-gray-900 <?php echo $dias_atraso > 0 ? 'text-red-600 font-semibold' : ''; ?>">
                            <?php echo date('d/m/Y', strtotime($conta['data_vencimento'])); ?>
                            <?php if ($dias_atraso > 0): ?>
                                <span class="text-sm">(<?php echo $dias_atraso; ?> dia(s) em atraso)</span>
                            <?php endif; ?>
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Categoria</label>
                        <p class="mt-1 text-gray-900">
                            <?php 
                            if ($conta['categoria_id']) {
                                $categoria = $db->fetchOne("SELECT nome FROM categorias_financeiras WHERE id = ?", [$conta['categoria_id']]);
                                echo htmlspecialchars($categoria['nome'] ?? 'Categoria não encontrada');
                            } else {
                                echo 'Sem categoria';
                            }
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulário de Pagamento -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-money-bill-wave text-green-600 mr-2"></i>
                Dados do Pagamento
            </h3>
        </div>

        <form method="POST" action="contas_pagar.php?acao=pagar&id=<?php echo $conta['id']; ?>" 
              class="p-6" id="formPagamento">
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Valor Pago -->
                <div>
                    <label for="valor_pago" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign mr-1"></i>
                        Valor Pago *
                    </label>
                    <div class="relative">
                        <span class="absolute left-3 top-3 text-gray-500">R$</span>
                        <input type="text" id="valor_pago" name="valor_pago" required
                               value="<?php echo number_format($conta['valor'], 2, ',', '.'); ?>"
                               class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors input-moeda">
                    </div>
                    <div class="mt-2 flex items-center space-x-4">
                        <button type="button" onclick="aplicarValorOriginal()" 
                                class="text-sm text-blue-600 hover:text-blue-800">
                            Valor original
                        </button>
                        <button type="button" onclick="abrirCalculadoraJuros()" 
                                class="text-sm text-orange-600 hover:text-orange-800">
                            <i class="fas fa-calculator mr-1"></i>
                            Calcular juros
                        </button>
                    </div>
                </div>

                <!-- Data do Pagamento -->
                <div>
                    <label for="data_pagamento" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar mr-1"></i>
                        Data do Pagamento *
                    </label>
                    <input type="date" id="data_pagamento" name="data_pagamento" required
                           value="<?php echo date('Y-m-d'); ?>"
                           max="<?php echo date('Y-m-d'); ?>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                    <p class="text-sm text-gray-500 mt-1">Data em que o pagamento foi realizado</p>
                </div>

                <!-- Forma de Pagamento -->
                <div>
                    <label for="forma_pagamento" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-credit-card mr-1"></i>
                        Forma de Pagamento *
                    </label>
                    <select id="forma_pagamento" name="forma_pagamento" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                        <option value="">Selecione a forma de pagamento</option>
                        <option value="dinheiro">💵 Dinheiro</option>
                        <option value="cartao_debito">💳 Cartão de Débito</option>
                        <option value="cartao_credito">💳 Cartão de Crédito</option>
                        <option value="transferencia">🏦 Transferência Bancária</option>
                        <option value="pix">📱 PIX</option>
                        <option value="boleto">📄 Boleto Bancário</option>
                        <option value="cheque">📝 Cheque</option>
                        <option value="deposito">🏧 Depósito Bancário</option>
                    </select>
                </div>

                <!-- Observações do Pagamento -->
                <div>
                    <label for="observacoes_pagamento" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-sticky-note mr-1"></i>
                        Observações do Pagamento
                    </label>
                    <textarea id="observacoes_pagamento" name="observacoes_pagamento" rows="3"
                              placeholder="Comprovante nº, desconto aplicado, etc..."
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors resize-none"></textarea>
                </div>
            </div>

            <!-- Resumo do Pagamento -->
            <div id="resumo-pagamento" class="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
                <h4 class="font-semibold text-green-900 mb-4">
                    <i class="fas fa-receipt mr-2"></i>
                    Resumo do Pagamento
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-green-700">Valor Original:</span>
                        <span class="font-semibold ml-2">R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?></span>
                    </div>
                    <div id="valor-pago-resumo">
                        <span class="text-green-700">Valor a Pagar:</span>
                        <span class="font-semibold ml-2">R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?></span>
                    </div>
                    <div id="diferenca-resumo">
                        <span class="text-green-700">Diferença:</span>
                        <span class="font-semibold ml-2">R$ 0,00</span>
                    </div>
                </div>
            </div>

            <!-- Ações -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200 mt-8">
                <div class="flex space-x-3">
                    <a href="contas_pagar.php?acao=listar" 
                       class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg transition-colors font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar
                    </a>
                    
                    <a href="contas_pagar.php?acao=visualizar&id=<?php echo $conta['id']; ?>" 
                       class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-6 py-3 rounded-lg transition-colors font-medium">
                        <i class="fas fa-eye mr-2"></i>
                        Ver Detalhes
                    </a>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="salvarRascunho()" 
                            class="bg-yellow-100 hover:bg-yellow-200 text-yellow-700 px-6 py-3 rounded-lg transition-colors font-medium">
                        <i class="fas fa-save mr-2"></i>
                        Salvar Rascunho
                    </button>
                    
                    <button type="submit" 
                            class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg transition-colors font-medium shadow-md hover:shadow-lg">
                        <i class="fas fa-check mr-2"></i>
                        Confirmar Pagamento
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Calculadora de Juros Modal -->
    <div id="modalCalculadoraJuros" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-calculator text-orange-600 mr-2"></i>
                    Calculadora de Juros e Multas
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Valor Original</label>
                    <input type="text" id="calc-valor-original" readonly
                           class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Multa (%)</label>
                        <input type="number" id="calc-multa" value="2" step="0.1" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Juros/dia (%)</label>
                        <input type="number" id="calc-juros" value="0.033" step="0.001" min="0" max="10"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Dias de atraso</label>
                    <input type="number" id="calc-dias" value="<?php echo max(0, $dias_atraso); ?>" min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md">
                </div>
                
                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Valor Original:</span>
                            <span id="calc-resultado-original">R$ 0,00</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Multa:</span>
                            <span id="calc-resultado-multa">R$ 0,00</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Juros:</span>
                            <span id="calc-resultado-juros">R$ 0,00</span>
                        </div>
                        <div class="flex justify-between font-semibold border-t border-orange-300 pt-2">
                            <span>Total a Pagar:</span>
                            <span id="calc-resultado-total">R$ 0,00</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button type="button" onclick="fecharCalculadoraJuros()" 
                        class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
                    Cancelar
                </button>
                <button type="button" onclick="aplicarCalculoJuros()" 
                        class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors">
                    <i class="fas fa-check mr-2"></i>
                    Aplicar Cálculo
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const valorPagoInput = document.getElementById('valor_pago');
    const valorOriginal = <?php echo $conta['valor']; ?>;
    
    // Atualizar resumo quando valor muda
    valorPagoInput.addEventListener('input', function() {
        atualizarResumo();
    });
    
    // Validação do formulário
    document.getElementById('formPagamento').addEventListener('submit', function(e) {
        const valorPago = parseFloat(valorPagoInput.value.replace(/[^\d,]/g, '').replace(',', '.'));
        
        if (isNaN(valorPago) || valorPago <= 0) {
            e.preventDefault();
            alert('Informe um valor pago válido.');
            return;
        }
        
        if (!document.getElementById('forma_pagamento').value) {
            e.preventDefault();
            alert('Selecione a forma de pagamento.');
            return;
        }
        
        // Confirmação final
        const diferenca = Math.abs(valorPago - valorOriginal);
        let confirmacao = true;
        
        if (diferenca > 0.01) {
            const tipo = valorPago > valorOriginal ? 'maior' : 'menor';
            const msg = `O valor pago é ${tipo} que o valor original. Diferença: R$ ${diferenca.toFixed(2).replace('.', ',')}.\n\nDeseja continuar?`;
            confirmacao = confirm(msg);
        } else {
            confirmacao = confirm('Confirma o registro deste pagamento?\n\nEsta ação não pode ser desfeita.');
        }
        
        if (!confirmacao) {
            e.preventDefault();
        }
    });
    
    // Carregar rascunho se existir
    carregarRascunhoPagamento();
});

function atualizarResumo() {
    const valorPago = parseFloat(document.getElementById('valor_pago').value.replace(/[^\d,]/g, '').replace(',', '.'));
    const valorOriginal = <?php echo $conta['valor']; ?>;
    
    if (!isNaN(valorPago)) {
        const diferenca = valorPago - valorOriginal;
        
        document.getElementById('valor-pago-resumo').innerHTML = 
            `<span class="text-green-700">Valor a Pagar:</span>
             <span class="font-semibold ml-2">R$ ${valorPago.toFixed(2).replace('.', ',')}</span>`;
        
        const diferencaElement = document.getElementById('diferenca-resumo');
        if (Math.abs(diferenca) < 0.01) {
            diferencaElement.innerHTML = 
                `<span class="text-green-700">Diferença:</span>
                 <span class="font-semibold ml-2 text-green-600">R$ 0,00</span>`;
        } else if (diferenca > 0) {
            diferencaElement.innerHTML = 
                `<span class="text-green-700">Diferença:</span>
                 <span class="font-semibold ml-2 text-blue-600">+R$ ${diferenca.toFixed(2).replace('.', ',')}</span>`;
        } else {
            diferencaElement.innerHTML = 
                `<span class="text-green-700">Diferença:</span>
                 <span class="font-semibold ml-2 text-red-600">-R$ ${Math.abs(diferenca).toFixed(2).replace('.', ',')}</span>`;
        }
    }
}

function aplicarValorOriginal() {
    document.getElementById('valor_pago').value = '<?php echo number_format($conta['valor'], 2, ',', '.'); ?>';
    atualizarResumo();
}

function abrirCalculadoraJuros() {
    const valorOriginal = <?php echo $conta['valor']; ?>;
    document.getElementById('calc-valor-original').value = 'R$ ' + valorOriginal.toFixed(2).replace('.', ',');
    document.getElementById('modalCalculadoraJuros').classList.remove('hidden');
    document.getElementById('modalCalculadoraJuros').classList.add('flex');
    calcularJuros();
    
    // Event listeners para recalcular automaticamente
    ['calc-multa', 'calc-juros', 'calc-dias'].forEach(id => {
        document.getElementById(id).addEventListener('input', calcularJuros);
    });
}

function fecharCalculadoraJuros() {
    document.getElementById('modalCalculadoraJuros').classList.add('hidden');
    document.getElementById('modalCalculadoraJuros').classList.remove('flex');
}

function calcularJuros() {
    const valorOriginal = <?php echo $conta['valor']; ?>;
    const multa = parseFloat(document.getElementById('calc-multa').value) || 0;
    const jurosDiario = parseFloat(document.getElementById('calc-juros').value) || 0;
    const dias = parseInt(document.getElementById('calc-dias').value) || 0;
    
    const valorMulta = valorOriginal * (multa / 100);
    const valorJuros = valorOriginal * (jurosDiario / 100) * dias;
    const valorTotal = valorOriginal + valorMulta + valorJuros;
    
    document.getElementById('calc-resultado-original').textContent = 'R$ ' + valorOriginal.toFixed(2).replace('.', ',');
    document.getElementById('calc-resultado-multa').textContent = 'R$ ' + valorMulta.toFixed(2).replace('.', ',');
    document.getElementById('calc-resultado-juros').textContent = 'R$ ' + valorJuros.toFixed(2).replace('.', ',');
    document.getElementById('calc-resultado-total').textContent = 'R$ ' + valorTotal.toFixed(2).replace('.', ',');
}

function aplicarCalculoJuros() {
    const valorTotal = document.getElementById('calc-resultado-total').textContent.replace('R$ ', '');
    document.getElementById('valor_pago').value = valorTotal;
    
    // Adicionar observação automática
    const observacoes = document.getElementById('observacoes_pagamento');
    const multa = document.getElementById('calc-multa').value;
    const juros = document.getElementById('calc-juros').value;
    const dias = document.getElementById('calc-dias').value;
    
    const obs = `Multa: ${multa}% | Juros: ${juros}%/dia | Dias atraso: ${dias}`;
    observacoes.value = observacoes.value ? observacoes.value + ' | ' + obs : obs;
    
    fecharCalculadoraJuros();
    atualizarResumo();
}

function salvarRascunho() {
    if (typeof(Storage) !== "undefined") {
        const dados = {
            conta_id: <?php echo $conta['id']; ?>,
            valor_pago: document.getElementById('valor_pago').value,
            data_pagamento: document.getElementById('data_pagamento').value,
            forma_pagamento: document.getElementById('forma_pagamento').value,
            observacoes_pagamento: document.getElementById('observacoes_pagamento').value,
            timestamp: new Date().getTime()
        };
        
        localStorage.setItem('rascunho_pagamento_' + <?php echo $conta['id']; ?>, JSON.stringify(dados));
        
        // Mostrar feedback
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check mr-2"></i>Salvo!';
        btn.classList.add('bg-green-100', 'text-green-700');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('bg-green-100', 'text-green-700');
        }, 2000);
    }
}

function carregarRascunhoPagamento() {
    if (typeof(Storage) !== "undefined") {
        const rascunho = localStorage.getItem('rascunho_pagamento_' + <?php echo $conta['id']; ?>);
        if (rascunho) {
            const dados = JSON.parse(rascunho);
            
            // Verificar se o rascunho não é muito antigo (24 horas)
            const agora = new Date().getTime();
            const idade = agora - dados.timestamp;
            if (idade < 24 * 60 * 60 * 1000) { // 24 horas em milissegundos
                if (confirm('Encontramos um rascunho de pagamento salvo. Deseja carregá-lo?')) {
                    document.getElementById('valor_pago').value = dados.valor_pago || '';
                    document.getElementById('data_pagamento').value = dados.data_pagamento || '';
                    document.getElementById('forma_pagamento').value = dados.forma_pagamento || '';
                    document.getElementById('observacoes_pagamento').value = dados.observacoes_pagamento || '';
                    atualizarResumo();
                } else {
                    localStorage.removeItem('rascunho_pagamento_' + <?php echo $conta['id']; ?>);
                }
            } else {
                // Remover rascunho antigo
                localStorage.removeItem('rascunho_pagamento_' + <?php echo $conta['id']; ?>);
            }
        }
    }
}

// Auto-save a cada 30 segundos
setInterval(() => {
    const valorPago = document.getElementById('valor_pago').value;
    const formaPagamento = document.getElementById('forma_pagamento').value;
    
    if (valorPago || formaPagamento) {
        salvarRascunho();
    }
}, 30000);

// Atalhos do teclado
document.addEventListener('keydown', function(e) {
    // Ctrl + Enter = Confirmar pagamento
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('formPagamento').submit();
    }
    
    // Ctrl + S = Salvar rascunho
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        salvarRascunho();
    }
    
    // F2 = Abrir calculadora
    if (e.key === 'F2') {
        e.preventDefault();
        abrirCalculadoraJuros();
    }
    
    // ESC = Voltar ou fechar modal
    if (e.key === 'Escape') {
        const modal = document.getElementById('modalCalculadoraJuros');
        if (!modal.classList.contains('hidden')) {
            fecharCalculadoraJuros();
        } else {
            window.location.href = 'contas_pagar.php?acao=listar';
        }
    }
});

// Inicializar
atualizarResumo();
</script>