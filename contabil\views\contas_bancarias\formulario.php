<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas <?php echo $acao === 'nova' ? 'fa-plus' : 'fa-edit'; ?> text-green-500 mr-2"></i>
                <?php echo $acao === 'nova' ? 'Nova Conta Bancária' : 'Editar Conta Bancária'; ?>
            </h3>
            <a href="contas_bancarias.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <form method="POST" class="p-6">
        <input type="hidden" name="acao" value="salvar">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Nome da Conta -->
            <div class="md:col-span-2">
                <label for="nome" class="block text-sm font-medium text-gray-700 mb-2">
                    Nome da Conta <span class="text-red-500">*</span>
                </label>
                <input type="text" id="nome" name="nome" required
                       value="<?php echo htmlspecialchars($conta['nome'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                       placeholder="Ex: Conta Corrente Principal - Itaú">
            </div>

            <!-- Banco -->
            <div>
                <label for="banco" class="block text-sm font-medium text-gray-700 mb-2">
                    Banco <span class="text-red-500">*</span>
                </label>
                <input type="text" id="banco" name="banco" required
                       value="<?php echo htmlspecialchars($conta['banco'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                       placeholder="Ex: Banco Itaú, Banco do Brasil">
            </div>

            <!-- Tipo de Conta -->
            <div>
                <label for="tipo_conta" class="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Conta <span class="text-red-500">*</span>
                </label>
                <select id="tipo_conta" name="tipo_conta" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Selecione o tipo</option>
                    <option value="corrente" <?php echo ($conta['tipo_conta'] ?? '') === 'corrente' ? 'selected' : ''; ?>>Conta Corrente</option>
                    <option value="poupanca" <?php echo ($conta['tipo_conta'] ?? '') === 'poupanca' ? 'selected' : ''; ?>>Conta Poupança</option>
                    <option value="investimento" <?php echo ($conta['tipo_conta'] ?? '') === 'investimento' ? 'selected' : ''; ?>>Conta Investimento</option>
                    <option value="caixa" <?php echo ($conta['tipo_conta'] ?? '') === 'caixa' ? 'selected' : ''; ?>>Caixa</option>
                </select>
            </div>

            <!-- Agência -->
            <div>
                <label for="agencia" class="block text-sm font-medium text-gray-700 mb-2">
                    Agência <span class="text-red-500">*</span>
                </label>
                <input type="text" id="agencia" name="agencia" required
                       value="<?php echo htmlspecialchars($conta['agencia'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                       placeholder="Ex: 1234-5">
            </div>

            <!-- Conta -->
            <div>
                <label for="conta" class="block text-sm font-medium text-gray-700 mb-2">
                    Número da Conta <span class="text-red-500">*</span>
                </label>
                <input type="text" id="conta" name="conta" required
                       value="<?php echo htmlspecialchars($conta['conta'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                       placeholder="Ex: 12345-6">
            </div>

            <!-- Saldo Inicial -->
            <div>
                <label for="saldo_inicial" class="block text-sm font-medium text-gray-700 mb-2">
                    Saldo Inicial <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-2 text-gray-500">R$</span>
                    <input type="text" id="saldo_inicial" name="saldo_inicial" required
                           value="<?php echo isset($conta['saldo_inicial']) ? number_format($conta['saldo_inicial'], 2, ',', '.') : '0,00'; ?>"
                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                           placeholder="0,00"
                           onkeyup="formatarMoeda(this)">
                </div>
                <p class="text-xs text-gray-500 mt-1">Saldo da conta no momento do cadastro</p>
            </div>

            <!-- Status -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                    Status <span class="text-red-500">*</span>
                </label>
                <select id="status" name="status" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="ativa" <?php echo ($conta['status'] ?? 'ativa') === 'ativa' ? 'selected' : ''; ?>>Ativa</option>
                    <option value="inativa" <?php echo ($conta['status'] ?? '') === 'inativa' ? 'selected' : ''; ?>>Inativa</option>
                    <option value="encerrada" <?php echo ($conta['status'] ?? '') === 'encerrada' ? 'selected' : ''; ?>>Encerrada</option>
                </select>
            </div>

            <!-- Observações -->
            <div class="md:col-span-2">
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações
                </label>
                <textarea id="observacoes" name="observacoes" rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="Informações adicionais sobre a conta..."><?php echo htmlspecialchars($conta['observacoes'] ?? ''); ?></textarea>
            </div>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="contas_bancarias.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <div class="flex items-center space-x-4">
                <button type="button" onclick="limparFormulario()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-eraser mr-2"></i>
                    Limpar
                </button>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    <?php echo $acao === 'nova' ? 'Cadastrar' : 'Atualizar'; ?>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Informações Importantes -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Informações Importantes</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li><strong>Saldo Inicial:</strong> Informe o saldo real da conta no momento do cadastro</li>
                    <li><strong>Movimentações:</strong> Após o cadastro, o saldo será atualizado automaticamente com as transações</li>
                    <li><strong>Status Inativa:</strong> A conta não aparecerá nas opções de movimentação</li>
                    <li><strong>Status Encerrada:</strong> A conta será mantida apenas para histórico</li>
                    <li>Verifique os dados bancários antes de salvar para evitar erros</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Bancos Sugeridos -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            Bancos Mais Utilizados
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button type="button" onclick="preencherBanco('Banco do Brasil')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">Banco do Brasil</div>
                <div class="text-sm text-gray-500">001</div>
            </button>
            <button type="button" onclick="preencherBanco('Banco Itaú')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">Banco Itaú</div>
                <div class="text-sm text-gray-500">341</div>
            </button>
            <button type="button" onclick="preencherBanco('Banco Bradesco')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">Banco Bradesco</div>
                <div class="text-sm text-gray-500">237</div>
            </button>
            <button type="button" onclick="preencherBanco('Caixa Econômica')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">Caixa Econômica</div>
                <div class="text-sm text-gray-500">104</div>
            </button>
            <button type="button" onclick="preencherBanco('Banco Santander')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">Banco Santander</div>
                <div class="text-sm text-gray-500">033</div>
            </button>
            <button type="button" onclick="preencherBanco('Banco Inter')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">Banco Inter</div>
                <div class="text-sm text-gray-500">077</div>
            </button>
            <button type="button" onclick="preencherBanco('Nubank')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">Nubank</div>
                <div class="text-sm text-gray-500">260</div>
            </button>
            <button type="button" onclick="preencherBanco('C6 Bank')" class="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="font-medium text-gray-800">C6 Bank</div>
                <div class="text-sm text-gray-500">336</div>
            </button>
        </div>
    </div>
</div>

<script>
function limparFormulario() {
    if (confirm('Tem certeza que deseja limpar todos os campos?')) {
        document.getElementById('nome').value = '';
        document.getElementById('banco').value = '';
        document.getElementById('tipo_conta').value = '';
        document.getElementById('agencia').value = '';
        document.getElementById('conta').value = '';
        document.getElementById('saldo_inicial').value = '0,00';
        document.getElementById('status').value = 'ativa';
        document.getElementById('observacoes').value = '';
        document.getElementById('nome').focus();
    }
}

function preencherBanco(nomeBanco) {
    document.getElementById('banco').value = nomeBanco;
    if (!document.getElementById('nome').value) {
        document.getElementById('nome').value = 'Conta Corrente - ' + nomeBanco;
    }
}

// Auto-focus no primeiro campo
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('nome').focus();
});
</script>
