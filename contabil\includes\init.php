<?php
/**
 * Arquivo de inicialização do módulo financeiro
 *
 * Este arquivo deve ser incluído no início de todas as páginas do financeiro
 * Baseado no init.php do secretaria que funciona corretamente
 */

// Carrega as configurações
require_once __DIR__ . '/../../config/config.php';

// Carrega as classes necessárias
require_once __DIR__ . '/../../includes/Database.php';
require_once __DIR__ . '/../../includes/Auth.php';

// Inicia a sessão
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Função para exibir mensagens de alerta
function showAlert($message, $type = 'success') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

// Função para definir mensagens para o usuário
if (!function_exists('setMensagem')) {
    function setMensagem($tipo, $mensagem) {
        $_SESSION['mensagem'] = [
            'tipo' => $tipo,
            'texto' => $mensagem
        ];
    }
}

// Função para obter mensagens de alerta
function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

// Função para redirecionar
if (!function_exists('redirect')) {
    function redirect($url) {
        header("Location: $url");
        exit;
    }
}

// Função para exigir login
if (!function_exists('exigirLogin')) {
    function exigirLogin() {
        Auth::requireLogin();
    }
}

// Função para exigir permissão
if (!function_exists('exigirPermissao')) {
    function exigirPermissao($modulo, $nivel = 'visualizar') {
        Auth::requirePermission($modulo, $nivel);
    }
}

// Função para fazer logout
function fazerLogout() {
    Auth::logout();
    redirect('../login.php');
}

// Função para obter ID do usuário
if (!function_exists('getUsuarioId')) {
    function getUsuarioId() {
        return Auth::getUserId();
    }
}

// Função para obter email do usuário
if (!function_exists('getUsuarioEmail')) {
    function getUsuarioEmail() {
        return Auth::getUserEmail();
    }
}

// Função para obter nome do usuário
if (!function_exists('getUsuarioNome')) {
    function getUsuarioNome() {
        return Auth::getUserName();
    }
}

// Função para obter tipo do usuário
if (!function_exists('getUsuarioTipo')) {
    function getUsuarioTipo() {
        return Auth::getUserType();
    }
}

// Função para verificar se usuário tem permissão
if (!function_exists('usuarioTemPermissao')) {
    function usuarioTemPermissao($modulo, $nivel = 'visualizar') {
        return Auth::hasPermission($modulo, $nivel);
    }
}

// Função para verificar se usuário está logado
if (!function_exists('usuarioLogado')) {
    function usuarioLogado() {
        return Auth::isLoggedIn();
    }
}

// Função para formatar moeda
if (!function_exists('formatarMoeda')) {
    function formatarMoeda($valor) {
        return 'R$ ' . number_format($valor, 2, ',', '.');
    }
}

// Função para formatar data
if (!function_exists('formatarData')) {
    function formatarData($data) {
        if (empty($data) || $data === '0000-00-00') {
            return '-';
        }
        return date('d/m/Y', strtotime($data));
    }
}

// Função para formatar data e hora
if (!function_exists('formatarDataHora')) {
    function formatarDataHora($data) {
        if (empty($data) || $data === '0000-00-00 00:00:00') {
            return '-';
        }
        return date('d/m/Y H:i', strtotime($data));
    }
}

// Função para sanitizar HTML
if (!function_exists('sanitizeHtml')) {
    function sanitizeHtml($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
}

// Função para registrar log (se existir)
if (!function_exists('registrarLog')) {
    function registrarLog($modulo, $acao, $descricao, $usuario_id = null) {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("
                INSERT INTO log_sincronizacao_financeira 
                (tabela_origem, operacao, registro_id, valor, descricao, created_at)
                VALUES (?, ?, ?, 0, ?, NOW())
            ");
            $stmt->execute([
                $modulo,
                $acao,
                $usuario_id ?? getUsuarioId(),
                $descricao
            ]);
        } catch (Exception $e) {
            // Ignorar erros de log para não quebrar o sistema
        }
    }
}

// Função para obter configuração do sistema
if (!function_exists('getConfiguracao')) {
    function getConfiguracao($chave, $padrao = null) {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT valor FROM configuracoes WHERE chave = ?");
            $stmt->execute([$chave]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? $result['valor'] : $padrao;
        } catch (Exception $e) {
            return $padrao;
        }
    }
}

// Função para definir timezone
if (!function_exists('setTimezone')) {
    function setTimezone($timezone = 'America/Sao_Paulo') {
        date_default_timezone_set($timezone);
    }
}

// Definir timezone padrão
setTimezone();

// Função para debug (apenas em desenvolvimento)
if (!function_exists('debug')) {
    function debug($data, $die = false) {
        if (defined('DEBUG') && DEBUG === true) {
            echo '<pre>';
            print_r($data);
            echo '</pre>';
            if ($die) die();
        }
    }
}

// Função para verificar se é ambiente de desenvolvimento
if (!function_exists('isDevelopment')) {
    function isDevelopment() {
        return defined('DEBUG') && DEBUG === true;
    }
}

// Função para obter URL base
if (!function_exists('getBaseUrl')) {
    function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['SCRIPT_NAME']);
        return $protocol . '://' . $host . $path;
    }
}

// Função para incluir CSS
if (!function_exists('incluirCSS')) {
    function incluirCSS($arquivo) {
        $baseUrl = getBaseUrl();
        echo "<link rel='stylesheet' href='{$baseUrl}/assets/css/{$arquivo}'>";
    }
}

// Função para incluir JS
if (!function_exists('incluirJS')) {
    function incluirJS($arquivo) {
        $baseUrl = getBaseUrl();
        echo "<script src='{$baseUrl}/assets/js/{$arquivo}'></script>";
    }
}

// Definir constantes úteis
if (!defined('FINANCEIRO_PATH')) {
    define('FINANCEIRO_PATH', __DIR__ . '/..');
}

if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__ . '/../..');
}

// Função para incluir template
if (!function_exists('incluirTemplate')) {
    function incluirTemplate($template, $dados = []) {
        extract($dados);
        $arquivo = FINANCEIRO_PATH . "/templates/{$template}.php";
        if (file_exists($arquivo)) {
            include $arquivo;
        }
    }
}
?>
