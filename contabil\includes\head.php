<?php
/**
 * Cabeçalho HTML Padrão - Módulo Financeiro
 * Arquivo único para garantir consistência em todas as páginas
 */

// Definir título padrão se não foi definido
if (!isset($titulo_pagina)) {
    $titulo_pagina = "Módulo Financeiro";
}
?>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="Sistema Financeiro Faciência ERP">
<meta name="author" content="Faciência ERP">
<title>Faciência ERP - <?php echo htmlspecialchars($titulo_pagina); ?></title>

<!-- CSS ÚNICO E DEFINITIVO -->
<link rel="stylesheet" href="css/financeiro.css">

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="../assets/favicon.ico">

<!-- Meta tags para PWA -->
<meta name="theme-color" content="#059669">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">

<!-- CSS Inline para garantir carregamento imediato -->
<style>
/* CSS crítico inline para carregamento instantâneo */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
    line-height: 1.6;
}

/* Sidebar fixa */
.sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 250px !important;
    height: 100vh !important;
    background: linear-gradient(180deg, #059669 0%, #047857 100%) !important;
    color: white !important;
    z-index: 1000 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    transition: transform 0.3s ease !important;
}

/* Main content */
.main-content {
    margin-left: 250px !important;
    min-height: 100vh !important;
    width: calc(100% - 250px) !important;
}

/* Mobile */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%) !important;
    }
    .sidebar.active {
        transform: translateX(0) !important;
    }
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }
}

/* Loading spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes críticas */
.flex { display: flex; }
.h-screen { height: 100vh; }
.overflow-hidden { overflow: hidden; }
.bg-white { background-color: #ffffff; }
.text-gray-800 { color: #1f2937; }
.p-6 { padding: 1.5rem; }
.rounded-lg { border-radius: 0.5rem; }
.shadow { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
</style>

<!-- JavaScript para funcionalidades básicas -->
<script>
// Função para toggle da sidebar mobile
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('active');
    }
}

// Função para fechar sidebar ao clicar fora (mobile)
document.addEventListener('click', function(event) {
    const sidebar = document.querySelector('.sidebar');
    const toggleBtn = document.querySelector('.sidebar-toggle');
    
    if (window.innerWidth <= 768 && sidebar && sidebar.classList.contains('active')) {
        if (!sidebar.contains(event.target) && !toggleBtn?.contains(event.target)) {
            sidebar.classList.remove('active');
        }
    }
});

// Log de debug
console.log('Faciência ERP - Módulo Financeiro carregado');
console.log('Página:', '<?php echo $titulo_pagina; ?>');
console.log('Timestamp:', new Date().toISOString());
</script>
