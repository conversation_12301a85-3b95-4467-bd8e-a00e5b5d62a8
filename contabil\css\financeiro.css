/**
 * CSS Simples do <PERSON>ódulo <PERSON>iro
 * Mantém o layout original, apenas corrige problemas básicos
 */

/* Reset básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Reset CSS completo */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ============================================================================
   ESTILOS BASE
   ============================================================================ */

/* Corpo da página */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #f9fafb;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Links */
a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Botões base */
button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.2s ease;
}

/* Inputs base */
input, select, textarea {
    font-family: inherit;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 8px 12px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tabelas */
table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
}

tr:hover {
    background-color: #f9fafb;
}

/* ============================================================================
   LAYOUT PRINCIPAL E FLEXBOX
   ============================================================================ */

/* Sistema Flexbox */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-1 {
    flex: 1;
}

.flex-none {
    flex: none;
}

.flex-auto {
    flex: auto;
}

/* Alinhamentos Flexbox */
.items-start {
    align-items: flex-start;
}

.items-center {
    align-items: center;
}

.items-end {
    align-items: flex-end;
}

.items-stretch {
    align-items: stretch;
}

.justify-start {
    justify-content: flex-start;
}

.justify-center {
    justify-content: center;
}

.justify-end {
    justify-content: flex-end;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

/* Dimensões */
.h-screen {
    height: 100vh;
}

.h-full {
    height: 100%;
}

.w-full {
    width: 100%;
}

.min-h-screen {
    min-height: 100vh;
}

/* Overflow */
.overflow-hidden {
    overflow: hidden;
}

.overflow-visible {
    overflow: visible;
}

.overflow-auto {
    overflow: auto;
}

.overflow-x-auto {
    overflow-x: auto;
}

.overflow-y-auto {
    overflow-y: auto;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.overflow-y-hidden {
    overflow-y: hidden;
}

/* ============================================================================
   SIDEBAR E NAVEGAÇÃO
   ============================================================================ */

/* Sidebar principal - CORRIGIDA PARA ROLAGEM */
.sidebar {
    width: 250px !important;
    min-width: 250px !important;
    max-width: 250px !important;
    transition: all 0.3s ease !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    z-index: 1000 !important;
    background: linear-gradient(180deg, #1f2937 0%, #111827 100%) !important;
    color: #f9fafb !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
    border-right: 1px solid #374151 !important;
    /* Garantir rolagem suave - FORÇADA */
    scrollbar-width: thin !important;
    scrollbar-color: #6b7280 #1f2937 !important;
    /* Forçar rolagem mesmo com poucos itens */
    padding-bottom: 50px !important;
}

/* Scrollbar personalizada para webkit */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #1f2937;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Estados da sidebar */
.sidebar-collapsed {
    width: 70px;
    min-width: 70px;
    max-width: 70px;
}

.sidebar-expanded {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
}

/* Logo da sidebar */
.sidebar .logo {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #374151;
    background: rgba(0, 0, 0, 0.2);
}

.sidebar .logo h2 {
    color: #60a5fa;
    font-size: 18px;
    font-weight: 700;
    margin: 0;
}

/* Menu da sidebar - CORRIGIDO PARA ROLAGEM */
.sidebar-menu {
    padding: 0;
    margin: 0;
    list-style: none;
    /* Forçar altura mínima para ativar rolagem */
    min-height: calc(100vh - 100px);
    padding-bottom: 100px;
}

.sidebar-menu li {
    margin: 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #d1d5db;
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.sidebar-menu a:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #60a5fa;
    border-left-color: #3b82f6;
    text-decoration: none;
}

.sidebar-menu a.active {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
    border-left-color: #3b82f6;
    font-weight: 600;
}

.sidebar-menu i {
    width: 20px;
    margin-right: 12px;
    text-align: center;
    font-size: 16px;
}

/* FORÇAR rolagem na sidebar */
.sidebar nav {
    height: 100%;
    overflow-y: auto;
    padding-bottom: 50px;
}

/* Conteúdo principal ajustado para sidebar - CORRIGIDO */
.main-content {
    margin-left: 250px !important;
    width: calc(100% - 250px) !important;
    min-height: 100vh !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    z-index: 1 !important;
    background: #f9fafb !important;
}

/* Quando sidebar está colapsada */
.sidebar-collapsed + .main-content {
    margin-left: 70px !important;
    width: calc(100% - 70px) !important;
}

/* FORÇAR que o conteúdo não fique atrás da sidebar */
body .main-content,
.flex .main-content,
.h-screen .main-content {
    margin-left: 250px !important;
    padding-left: 0 !important;
    width: calc(100% - 250px) !important;
}

/* Para elementos com classe flex que podem interferir */
.flex.h-screen .main-content {
    margin-left: 250px !important;
    width: calc(100% - 250px) !important;
}

/* Garantir que header e main não fiquem atrás */
.main-content header,
.main-content main {
    position: relative !important;
    z-index: 2 !important;
}

/* ============================================================================
   CARDS E COMPONENTES DO DASHBOARD
   ============================================================================ */

/* Cards básicos */
.card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* Cards do dashboard */
.dashboard-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
}

/* Header dos cards */
.card-header {
    padding: 16px 20px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 12px 20px;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    font-size: 12px;
    color: #6b7280;
}

/* Cards de métricas */
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: none;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.metric-card:hover::before {
    opacity: 1;
}

/* Variações de cores dos metric cards */
.metric-card.green {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.metric-card.red {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

.metric-card.blue {
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
}

.metric-card.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
}

.metric-card.orange {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.metric-card.indigo {
    background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%);
}

.metric-card.pink {
    background: linear-gradient(135deg, #ec4899 0%, #f472b6 100%);
}

.metric-card.teal {
    background: linear-gradient(135deg, #14b8a6 0%, #5eead4 100%);
}

/* Conteúdo dos metric cards */
.metric-card .metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin: 8px 0;
}

.metric-card .metric-label {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
}

.metric-card .metric-change {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 4px;
}

.metric-card .metric-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Alertas */
.alert-card {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Grid system */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
}

.gap-4 {
    gap: 1rem;
}

.gap-6 {
    gap: 1.5rem;
}

.gap-8 {
    gap: 2rem;
}

/* Responsive */
@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
    
    .lg\:grid-cols-6 {
        grid-template-columns: repeat(6, minmax(0, 1fr));
    }
}

@media (min-width: 1280px) {
    .xl\:grid-cols-6 {
        grid-template-columns: repeat(6, minmax(0, 1fr));
    }
}

/* Espaçamentos */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }

.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }

.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }
.space-x-4 > * + * { margin-left: 1rem; }

/* Cores de fundo */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-800 { background-color: #1f2937; }

.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }

.bg-green-50 { background-color: #f0fdf4; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-500 { background-color: #22c55e; }
.bg-green-600 { background-color: #16a34a; }

.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fee2e2; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-600 { background-color: #dc2626; }

.bg-yellow-50 { background-color: #fefce8; }
.bg-yellow-100 { background-color: #fef3c7; }

.bg-purple-50 { background-color: #faf5ff; }
.bg-purple-100 { background-color: #f3e8ff; }

.bg-orange-50 { background-color: #fff7ed; }
.bg-orange-100 { background-color: #ffedd5; }

/* Cores de texto */
.text-white { color: #ffffff; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }

.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }

.text-green-600 { color: #16a34a; }
.text-green-700 { color: #15803d; }

.text-red-600 { color: #dc2626; }
.text-red-700 { color: #b91c1c; }

/* Tamanhos de texto */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

/* Pesos de fonte */
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Bordas */
.border { border-width: 1px; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-blue-200 { border-color: #bfdbfe; }
.border-red-200 { border-color: #fecaca; }
.border-yellow-200 { border-color: #fde68a; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

/* Sombras */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Transições */
.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }
.duration-200 { transition-duration: 200ms; }

/* Hover effects */
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-green-700:hover { background-color: #15803d; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:scale-105:hover { transform: scale(1.05); }

/* Flexbox utilities */
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }

/* Posicionamento */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/* Dimensões */
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-96 { height: 24rem; }
.h-100 { height: 25rem; }

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Text alignment */
.text-center { text-align: center; }
.text-right { text-align: right; }

/* ============================================================================
   BOTÕES E COMPONENTES INTERATIVOS
   ============================================================================ */

/* Botões base */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1.4;
    min-height: 40px;
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Variações de botões */
.btn-primary {
    background: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background: #2563eb;
    color: #ffffff;
}

.btn-secondary {
    background: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #4b5563;
    color: #ffffff;
}

.btn-success {
    background: #10b981;
    color: #ffffff;
}

.btn-success:hover {
    background: #059669;
    color: #ffffff;
}

.btn-danger {
    background: #ef4444;
    color: #ffffff;
}

.btn-danger:hover {
    background: #dc2626;
    color: #ffffff;
}

.btn-warning {
    background: #f59e0b;
    color: #ffffff;
}

.btn-warning:hover {
    background: #d97706;
    color: #ffffff;
}

.btn-info {
    background: #06b6d4;
    color: #ffffff;
}

.btn-info:hover {
    background: #0891b2;
    color: #ffffff;
}

/* Botões outline */
.btn-outline-primary {
    background: transparent;
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.btn-outline-primary:hover {
    background: #3b82f6;
    color: #ffffff;
}

.btn-outline-secondary {
    background: transparent;
    color: #6b7280;
    border: 1px solid #6b7280;
}

.btn-outline-secondary:hover {
    background: #6b7280;
    color: #ffffff;
}

/* Tamanhos de botões */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
    min-height: 48px;
}

.btn-xl {
    padding: 16px 32px;
    font-size: 18px;
    min-height: 56px;
}

/* Botão de bloco */
.btn-block {
    width: 100%;
    justify-content: center;
}

/* Grupo de botões */
.btn-group {
    display: inline-flex;
    border-radius: 6px;
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-right: none;
}

/* ============================================================================
   FORMULÁRIOS E INPUTS
   ============================================================================ */

/* Grupos de formulário */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: #ef4444;
}

/* Inputs */
.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    background: #ffffff;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
    background: #f3f4f6;
    color: #6b7280;
    cursor: not-allowed;
}

.form-control.is-invalid {
    border-color: #ef4444;
}

.form-control.is-invalid:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Select */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
}

/* Textarea */
.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Checkbox e Radio */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.form-check-input {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    cursor: pointer;
}

.form-check-label {
    cursor: pointer;
    font-weight: normal;
    margin-bottom: 0;
}

/* Mensagens de validação */
.invalid-feedback {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #ef4444;
}

.valid-feedback {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #10b981;
}

/* ============================================================================
   ALERTAS E NOTIFICAÇÕES
   ============================================================================ */

/* Alertas base */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid transparent;
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.4;
}

.alert-success {
    background: #d1fae5;
    border-color: #a7f3d0;
    color: #065f46;
}

.alert-info {
    background: #dbeafe;
    border-color: #93c5fd;
    color: #1e40af;
}

.alert-warning {
    background: #fef3c7;
    border-color: #fcd34d;
    color: #92400e;
}

.alert-danger {
    background: #fee2e2;
    border-color: #fca5a5;
    color: #991b1b;
}

/* Alert com ícones */
.alert-with-icon {
    display: flex;
    align-items: flex-start;
}

.alert-with-icon .alert-icon {
    margin-right: 12px;
    font-size: 16px;
    margin-top: 2px;
}

/* Alert dismissible */
.alert-dismissible {
    position: relative;
    padding-right: 50px;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 12px;
    right: 16px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    opacity: 0.7;
}

.alert-dismissible .btn-close:hover {
    opacity: 1;
}

/* ============================================================================
   RESPONSIVIDADE
   ============================================================================ */

/* Tablets */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 70px;
        width: calc(100% - 70px);
    }

    .sidebar {
        width: 70px;
        min-width: 70px;
        max-width: 70px;
    }

    .sidebar-menu a span {
        display: none;
    }

    .sidebar .logo h2 {
        font-size: 14px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
        width: 250px !important;
        min-width: 250px !important;
        max-width: 250px !important;
        z-index: 1000 !important;
    }

    .sidebar.active,
    .sidebar.sidebar-expanded {
        transform: translateX(0) !important;
    }

    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    body .main-content {
        margin-left: 0 !important;
    }

    .metric-card {
        padding: 16px;
    }

    .metric-card .metric-value {
        font-size: 2rem;
    }

    .btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .card-body {
        padding: 16px;
    }

    table {
        font-size: 12px;
    }

    th, td {
        padding: 8px;
    }
}

/* Mobile pequeno */
@media (max-width: 480px) {
    .metric-card .metric-value {
        font-size: 1.5rem;
    }

    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .card-body {
        padding: 12px;
    }
}
