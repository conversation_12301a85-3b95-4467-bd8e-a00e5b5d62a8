-- ============================================================================
-- CRIAÇÃO DAS TABELAS PARA AS PÁGINAS FALTANTES DO MÓDULO FINANCEIRO
-- ============================================================================
-- 
-- Este script cria todas as tabelas necessárias para as 4 páginas implementadas:
-- 1. Ativo Não Circulante
-- 2. Centro de Custos
-- 3. Fluxo de Caixa Projetado
-- 4. Orçamento e Planejamento
--
-- Execute este script no banco de dados para criar as estruturas necessárias
-- ============================================================================

-- 1. ATIVO NÃO CIRCULANTE
-- ============================================================================

-- Tabela principal de ativos não circulantes
CREATE TABLE IF NOT EXISTS financeiro_ativo_nao_circulante (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_ativo ENUM('imobilizado', 'intangivel', 'investimento') NOT NULL,
    categoria VARCHAR(50) NOT NULL,
    descricao TEXT NOT NULL,
    numero_patrimonio VARCHAR(50),
    data_aquisicao DATE NOT NULL,
    valor_aquisicao DECIMAL(15,2) NOT NULL,
    vida_util_anos INT,
    taxa_depreciacao_anual DECIMAL(8,4),
    depreciacao_acumulada DECIMAL(15,2) DEFAULT 0,
    valor_liquido DECIMAL(15,2) NOT NULL,
    fornecedor VARCHAR(255),
    nota_fiscal VARCHAR(100),
    localizacao VARCHAR(255),
    responsavel VARCHAR(255),
    observacoes TEXT,
    status ENUM('ativo', 'baixado') DEFAULT 'ativo',
    motivo_baixa VARCHAR(255),
    data_baixa DATE,
    valor_baixa DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tipo_ativo (tipo_ativo),
    INDEX idx_categoria (categoria),
    INDEX idx_status (status),
    INDEX idx_data_aquisicao (data_aquisicao)
);

-- 2. CENTRO DE CUSTOS
-- ============================================================================

-- Tabela de centros de custos
CREATE TABLE IF NOT EXISTS financeiro_centro_custos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    codigo VARCHAR(20) NOT NULL UNIQUE,
    nome VARCHAR(255) NOT NULL,
    tipo ENUM('curso', 'departamento', 'unidade', 'projeto', 'atividade') NOT NULL,
    responsavel VARCHAR(255),
    descricao TEXT,
    meta_receita_mensal DECIMAL(15,2) DEFAULT 0,
    meta_custo_mensal DECIMAL(15,2) DEFAULT 0,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_codigo (codigo),
    INDEX idx_tipo (tipo),
    INDEX idx_status (status)
);

-- Tabela de alocação de custos
CREATE TABLE IF NOT EXISTS financeiro_alocacao_custos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    centro_custo_id INT NOT NULL,
    tipo_custo ENUM('pessoal', 'material', 'servicos', 'equipamentos', 'infraestrutura', 'marketing', 'administrativo', 'rateio_indireto', 'outros') NOT NULL,
    descricao TEXT NOT NULL,
    valor DECIMAL(15,2) NOT NULL,
    data_competencia DATE NOT NULL,
    documento_origem VARCHAR(100),
    observacoes TEXT,
    status ENUM('alocado', 'cancelado') DEFAULT 'alocado',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (centro_custo_id) REFERENCES financeiro_centro_custos(id) ON DELETE CASCADE,
    INDEX idx_centro_custo (centro_custo_id),
    INDEX idx_tipo_custo (tipo_custo),
    INDEX idx_data_competencia (data_competencia),
    INDEX idx_status (status)
);

-- 3. FLUXO DE CAIXA PROJETADO
-- ============================================================================

-- Tabela de projeções de fluxo de caixa
CREATE TABLE IF NOT EXISTS financeiro_fluxo_projetado (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo ENUM('receita', 'despesa') NOT NULL,
    descricao TEXT NOT NULL,
    data_inicio DATE NOT NULL,
    data_fim DATE NOT NULL,
    valor_mensal DECIMAL(15,2) NOT NULL,
    categoria VARCHAR(100) NOT NULL,
    observacoes TEXT,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tipo (tipo),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_data_fim (data_fim),
    INDEX idx_categoria (categoria),
    INDEX idx_status (status)
);

-- Tabela de aplicações financeiras
CREATE TABLE IF NOT EXISTS financeiro_aplicacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_aplicacao ENUM('cdb', 'lci', 'lca', 'tesouro_direto', 'fundos', 'poupanca', 'outros') NOT NULL,
    instituicao VARCHAR(255) NOT NULL,
    valor_aplicado DECIMAL(15,2) NOT NULL,
    data_aplicacao DATE NOT NULL,
    data_vencimento DATE,
    taxa_juros DECIMAL(8,4) NOT NULL,
    observacoes TEXT,
    status ENUM('ativo', 'resgatado', 'vencido') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tipo_aplicacao (tipo_aplicacao),
    INDEX idx_instituicao (instituicao),
    INDEX idx_data_aplicacao (data_aplicacao),
    INDEX idx_data_vencimento (data_vencimento),
    INDEX idx_status (status)
);

-- Tabela de cenários de planejamento
CREATE TABLE IF NOT EXISTS financeiro_cenarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    tipo_cenario ENUM('otimista', 'realista', 'pessimista', 'personalizado') NOT NULL,
    fator_receita DECIMAL(5,4) DEFAULT 1.0000, -- Multiplicador para receitas (ex: 1.1 = +10%)
    fator_despesa DECIMAL(5,4) DEFAULT 1.0000, -- Multiplicador para despesas (ex: 0.9 = -10%)
    data_inicio DATE NOT NULL,
    data_fim DATE NOT NULL,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tipo_cenario (tipo_cenario),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_status (status)
);

-- 4. ORÇAMENTO E PLANEJAMENTO
-- ============================================================================

-- Tabela de orçamento anual
CREATE TABLE IF NOT EXISTS financeiro_orcamento (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ano YEAR NOT NULL,
    categoria VARCHAR(100) NOT NULL,
    subcategoria VARCHAR(100),
    descricao TEXT NOT NULL,
    tipo ENUM('receita', 'despesa') NOT NULL,
    jan DECIMAL(15,2) DEFAULT 0,
    fev DECIMAL(15,2) DEFAULT 0,
    mar DECIMAL(15,2) DEFAULT 0,
    abr DECIMAL(15,2) DEFAULT 0,
    mai DECIMAL(15,2) DEFAULT 0,
    jun DECIMAL(15,2) DEFAULT 0,
    jul DECIMAL(15,2) DEFAULT 0,
    ago DECIMAL(15,2) DEFAULT 0,
    set DECIMAL(15,2) DEFAULT 0,
    out DECIMAL(15,2) DEFAULT 0,
    nov DECIMAL(15,2) DEFAULT 0,
    dez DECIMAL(15,2) DEFAULT 0,
    valor_total_anual DECIMAL(15,2) NOT NULL,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_ano (ano),
    INDEX idx_categoria (categoria),
    INDEX idx_tipo (tipo),
    INDEX idx_status (status)
);

-- Tabela de valores realizados do orçamento
CREATE TABLE IF NOT EXISTS financeiro_orcamento_realizado (
    id INT AUTO_INCREMENT PRIMARY KEY,
    orcamento_id INT NOT NULL,
    mes TINYINT NOT NULL, -- 1 a 12
    valor_realizado DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (orcamento_id) REFERENCES financeiro_orcamento(id) ON DELETE CASCADE,
    UNIQUE KEY unique_orcamento_mes (orcamento_id, mes),
    INDEX idx_orcamento (orcamento_id),
    INDEX idx_mes (mes)
);

-- Tabela de metas financeiras
CREATE TABLE IF NOT EXISTS financeiro_metas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ano YEAR NOT NULL,
    tipo_meta ENUM('receita', 'despesa', 'resultado', 'margem', 'crescimento', 'outros') NOT NULL,
    descricao TEXT NOT NULL,
    valor_meta DECIMAL(15,2) NOT NULL,
    prazo DATE NOT NULL,
    responsavel VARCHAR(255),
    status ENUM('ativo', 'atingida', 'nao_atingida', 'cancelada') DEFAULT 'ativo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_ano (ano),
    INDEX idx_tipo_meta (tipo_meta),
    INDEX idx_prazo (prazo),
    INDEX idx_status (status)
);

-- ============================================================================
-- INSERÇÃO DE DADOS DE EXEMPLO (OPCIONAL)
-- ============================================================================

-- Exemplos de centros de custos
INSERT IGNORE INTO financeiro_centro_custos (codigo, nome, tipo, responsavel, meta_receita_mensal, meta_custo_mensal) VALUES
('CURSO-ADM', 'Curso de Administração', 'curso', 'Prof. João Silva', 50000.00, 35000.00),
('CURSO-CONT', 'Curso de Contabilidade', 'curso', 'Prof. Maria Santos', 40000.00, 28000.00),
('DEPTO-ADM', 'Departamento Administrativo', 'departamento', 'Ana Costa', 0.00, 15000.00),
('DEPTO-TI', 'Departamento de TI', 'departamento', 'Carlos Oliveira', 0.00, 12000.00);

-- Exemplos de categorias de orçamento
INSERT IGNORE INTO financeiro_orcamento (ano, categoria, subcategoria, descricao, tipo, jan, fev, mar, abr, mai, jun, jul, ago, set, out, nov, dez, valor_total_anual) VALUES
(YEAR(CURDATE()), 'mensalidades', 'graduacao', 'Mensalidades de Graduação', 'receita', 80000, 85000, 90000, 85000, 80000, 75000, 70000, 75000, 85000, 90000, 85000, 80000, 980000),
(YEAR(CURDATE()), 'pessoal', 'professores', 'Salários de Professores', 'despesa', 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 45000, 540000),
(YEAR(CURDATE()), 'infraestrutura', 'energia', 'Energia Elétrica', 'despesa', 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 3500, 42000);

-- Exemplos de metas
INSERT IGNORE INTO financeiro_metas (ano, tipo_meta, descricao, valor_meta, prazo, responsavel) VALUES
(YEAR(CURDATE()), 'receita', 'Meta de Receita Anual', 1200000.00, CONCAT(YEAR(CURDATE()), '-12-31'), 'Diretor Financeiro'),
(YEAR(CURDATE()), 'margem', 'Meta de Margem Líquida', 15.00, CONCAT(YEAR(CURDATE()), '-12-31'), 'Diretor Financeiro'),
(YEAR(CURDATE()), 'crescimento', 'Meta de Crescimento de Alunos', 10.00, CONCAT(YEAR(CURDATE()), '-12-31'), 'Diretor Acadêmico');

-- ============================================================================
-- COMENTÁRIOS FINAIS
-- ============================================================================

-- Este script cria todas as estruturas necessárias para as 4 páginas implementadas.
-- As tabelas incluem:
-- 
-- 1. ATIVO NÃO CIRCULANTE:
--    - financeiro_ativo_nao_circulante: Controle de imobilizado, intangível e investimentos
-- 
-- 2. CENTRO DE CUSTOS:
--    - financeiro_centro_custos: Definição dos centros de custos
--    - financeiro_alocacao_custos: Alocação de custos aos centros
-- 
-- 3. FLUXO DE CAIXA PROJETADO:
--    - financeiro_fluxo_projetado: Projeções de receitas e despesas
--    - financeiro_aplicacoes: Controle de aplicações financeiras
--    - financeiro_cenarios: Cenários de planejamento
-- 
-- 4. ORÇAMENTO E PLANEJAMENTO:
--    - financeiro_orcamento: Orçamento anual por categoria
--    - financeiro_orcamento_realizado: Valores realizados vs orçados
--    - financeiro_metas: Metas e objetivos financeiros
-- 
-- Todas as tabelas incluem:
-- - Chaves primárias auto-incrementais
-- - Índices para otimização de consultas
-- - Chaves estrangeiras com integridade referencial
-- - Campos de auditoria (created_at, updated_at)
-- - Validações através de ENUMs
-- - Campos de status para controle de estado
-- 
-- Execute este script no seu banco de dados MySQL/MariaDB para criar
-- todas as estruturas necessárias para o funcionamento completo das
-- 4 páginas implementadas do módulo financeiro.
