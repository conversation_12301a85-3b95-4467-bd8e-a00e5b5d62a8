<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mensagens - Faciencia EAD</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        /* Reutilizando o CSS dos páginas anteriores com algumas pequenas modificações */
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --very-light-purple: #E6E6FA;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
            --text-muted: #6c757d;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
            --border-radius: 10px;
            --card-shadow: 0 6px 15px rgba(106, 90, 205, 0.1);
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        /* Sidebar Moderna */
        .sidebar {
            background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            position: relative;
            z-index: 10;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar-logo i {
            font-size: 2rem;
            margin-right: 10px;
        }

        .sidebar-logo h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .sidebar-menu {
            list-style: none;
            padding-left: 0;
            margin-bottom: 30px;
        }

        .sidebar-menu li {
            margin-bottom: 8px;
        }

        .sidebar-menu a {
            color: var(--white);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed) ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-menu a.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background-color: var(--white);
            border-radius: 0 2px 2px 0;
        }

        .sidebar-menu a i {
            margin-right: 15px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            transition: all var(--transition-speed) ease;
        }

        .sidebar-menu a:hover i {
            transform: scale(1.2);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
        }

        /* Conteúdo Principal */
        .main-content {
            background-color: var(--light-bg);
            padding: 30px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--very-light-purple);
        }

        .dashboard-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--secondary-purple);
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .student-info {
            text-align: right;
        }

        .student-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--secondary-purple);
        }

        .student-email {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .user-avatar {
            position: relative;
            cursor: pointer;
        }

        .user-avatar img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--white);
        }

        /* Cards e Elementos de UI */
        .dashboard-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 25px;
            margin-bottom: 30px;
            transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-purple), var(--light-purple));
        }

        .card-header-custom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .card-header-custom h4 {
            font-weight: 600;
            color: var(--secondary-purple);
            margin: 0;
            font-size: 1.25rem;
        }

        /* Estilos específicos para Mensagens */
        .messages-container {
            display: grid;
            grid-template-columns: 350px 1fr;
            border: 1px solid var(--very-light-purple);
            border-radius: var(--border-radius);
            min-height: 70vh;
        }

        .contacts-list {
            border-right: 1px solid var(--very-light-purple);
            overflow-y: auto;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid var(--very-light-purple);
            cursor: pointer;
            transition: background-color var(--transition-speed) ease;
        }

        .contact-item:hover, .contact-item.active {
            background-color: var(--very-light-purple);
        }

        .contact-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
        }

        .contact-info {
            flex-grow: 1;
        }

        .contact-name {
            font-weight: 600;
            color: var(--secondary-purple);
            margin-bottom: 5px;
        }

        .contact-last-message {
            color: var(--text-muted);
            font-size: 0.85rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .contact-badge {
            background-color: var(--danger-red);
            color: var(--white);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }

        .chat-window {
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid var(--very-light-purple);
        }

        .chat-header-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
        }

        .chat-header-info {
            flex-grow: 1;
        }

        .chat-header-name {
            font-weight: 600;
            color: var(--secondary-purple);
        }

        .chat-header-status {
            color: var(--text-muted);
            font-size: 0.85rem;
        }

        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 20px;
            background-color: var(--light-bg);
        }

        .message {
            max-width: 70%;
            margin-bottom: 15px;
            clear: both;
        }

        .message-sent {
            float: right;
            text-align: right;
        }

        .message-received {
            float: left;
            text-align: left;
        }

        .message-content {
            padding: 10px 15px;
            border-radius: var(--border-radius);
            max-width: 100%;
            word-wrap: break-word;
        }

        .message-sent .message-content {
            background-color: var(--primary-purple);
            color: var(--white);
        }

        .message-received .message-content {
            background-color: var(--very-light-purple);
            color: var(--text-dark);
        }

        .message-time {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 5px;
        }

        .chat-input {
            display: flex;
            align-items: center;
            padding: 15px;
            border-top: 1px solid var(--very-light-purple);
        }

        .chat-input-field {
            flex-grow: 1;
            margin-right: 15px;
        }

        /* Responsividade */
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                height: 100vh;
                width: 280px;
                z-index: 1000;
                transition: left var(--transition-speed) ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 20px 15px;
            }

            .messages-container {
                grid-template-columns: 1fr;
            }

            .contacts-list {
                border-right: none;
                border-bottom: 1px solid var(--very-light-purple);
                max-height: 300px;
                overflow-y: auto;
            }
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .user-info {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-grid">
        <!-- Sidebar Moderna -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-graduation-cap"></i>
                <h2>Faciencia</h2>
            </div>
            <ul class="sidebar-menu">
                <li>
                    <a href="aluno_dashboard.html">
                        <i class="fas fa-home"></i> Início
                    </a>
                </li>
                <li>
                    <a href="meuscursos.html">
                        <i class="fas fa-book"></i> Meus Cursos
                    </a>
                </li>
                <li>
                    <a href="calendario.html">
                        <i class="fas fa-calendar-alt"></i> Calendário
                    </a>
                </li>
                <li>
                    <a href="desempenho.html">
                        <i class="fas fa-chart-line"></i> Desempenho
                    </a>
                </li>
                <li>
                    <a href="certificado.html">
                        <i class="fas fa-certificate"></i> Certificados
                    </a>
                </li>
                <li>
                    <a href="material.html">
                        <i class="fas fa-file-alt"></i> Materiais
                    </a>
                </li>
                <li>
                   
                        <a href="mensagens.html" class="active">
                            <i class="fas fa-comment-alt"></i> Mensagens
                        </a>
                    </li>
                    <li>
                        <a href="perfil.html">
                            <i class="fas fa-user-cog"></i> Perfil
                        </a>
                    </li>
                    <li>
                        <a href="index.html" class="text-danger">
                            <i class="fas fa-sign-out-alt"></i> Sair
                        </a>
                    </li>
                </ul>
                <div class="sidebar-footer">
                    <p>Faciencia EAD © 2024</p>
                    <small>Versão 2.5.3</small>
                </div>
            </aside>

            <!-- Conteúdo Principal -->
            <main class="main-content">
                <!-- Cabeçalho do Painel -->
                <header class="dashboard-header">
                    <h1>Central de Mensagens</h1>
                    <div class="user-info">
                        <div class="student-info">
                            <div class="student-name">Maria Silva</div>
                            <div class="student-email"><EMAIL></div>
                        </div>
                        <div class="user-avatar">
                            <img src="/api/placeholder/50/50" alt="Foto de Perfil">
                            <span class="notification-badge">2</span>
                        </div>
                    </div>
                </header>

                <!-- Seção de Mensagens -->
                <div class="dashboard-card p-0">
                    <div class="messages-container">
                        <!-- Lista de Contatos -->
                        <div class="contacts-list">
                            <div class="contact-item active">
                                <img src="/api/placeholder/50/50" alt="Professor João" class="contact-avatar">
                                <div class="contact-info">
                                    <div class="contact-name">Professor João</div>
                                    <div class="contact-last-message">Sobre o seu último projeto...</div>
                                </div>
                                <div class="contact-badge">3</div>
                            </div>

                            <div class="contact-item">
                                <img src="/api/placeholder/50/50" alt="Suporte Faciencia" class="contact-avatar">
                                <div class="contact-info">
                                    <div class="contact-name">Suporte Faciencia</div>
                                    <div class="contact-last-message">Como podemos ajudar?</div>
                                </div>
                                <div class="contact-badge">1</div>
                            </div>

                            <div class="contact-item">
                                <img src="/api/placeholder/50/50" alt="Coordenação" class="contact-avatar">
                                <div class="contact-info">
                                    <div class="contact-name">Coordenação</div>
                                    <div class="contact-last-message">Informações sobre matrícula...</div>
                                </div>
                            </div>

                            <div class="contact-item">
                                <img src="/api/placeholder/50/50" alt="Maria Santos" class="contact-avatar">
                                <div class="contact-info">
                                    <div class="contact-name">Maria Santos</div>
                                    <div class="contact-last-message">Grupo de estudo</div>
                                </div>
                            </div>
                        </div>

                        <!-- Janela de Chat -->
                        <div class="chat-window">
                            <!-- Cabeçalho do Chat -->
                            <div class="chat-header">
                                <img src="/api/placeholder/40/40" alt="Professor João" class="chat-header-avatar">
                                <div class="chat-header-info">
                                    <div class="chat-header-name">Professor João</div>
                                    <div class="chat-header-status">Online</div>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-video"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Mensagens do Chat -->
                            <div class="chat-messages">
                                <div class="message message-received">
                                    <div class="message-content">
                                        Olá, gostaria de discutir o projeto final de marketing digital.
                                    </div>
                                    <div class="message-time">Hoje, 10:30</div>
                                </div>

                                <div class="message message-sent">
                                    <div class="message-content">
                                        Claro! Qual parte específica você gostaria de abordar?
                                    </div>
                                    <div class="message-time">Hoje, 10:35</div>
                                </div>

                                <div class="message message-received">
                                    <div class="message-content">
                                        Estou com dúvidas sobre a estratégia de marketing digital para o estudo de caso.
                                    </div>
                                    <div class="message-time">Hoje, 10:37</div>
                                </div>
                            </div>

                            <!-- Entrada de Mensagem -->
                            <div class="chat-input">
                                <div class="chat-input-field">
                                    <input type="text" class="form-control" placeholder="Digite sua mensagem...">
                                </div>
                                <button class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Bootstrap JS e dependências -->
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
    </body>
</html> 