<?php
/**
 * Script para executar TODAS as tabelas do módulo financeiro de uma vez
 * Execute este arquivo para garantir que todas as estruturas estejam criadas
 */

// Configurações de erro
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Conectar ao banco
require_once '../../secretaria/includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>🚀 Criando TODAS as tabelas do módulo financeiro...</h2>\n";
    
    // Lista de arquivos SQL para executar
    $arquivos_sql = [
        'criar_tabelas_faltantes.sql' => 'Tabelas das 4 páginas implementadas',
        'criar_tabelas_boletos.sql' => 'Tabelas do módulo de boletos'
    ];
    
    $total_success = 0;
    $total_errors = 0;
    
    foreach ($arquivos_sql as $arquivo => $descricao) {
        echo "<h3>📄 Executando: $arquivo</h3>\n";
        echo "<p><em>$descricao</em></p>\n";
        
        $arquivo_path = __DIR__ . '/' . $arquivo;
        
        if (!file_exists($arquivo_path)) {
            echo "<p style='color: orange;'>⚠️ Arquivo não encontrado: $arquivo</p>\n";
            continue;
        }
        
        // Ler o arquivo SQL
        $sql_content = file_get_contents($arquivo_path);
        
        // Dividir em comandos individuais
        $commands = explode(';', $sql_content);
        
        $file_success = 0;
        $file_errors = 0;
        
        foreach ($commands as $command) {
            $command = trim($command);
            
            // Pular comandos vazios e comentários
            if (empty($command) || strpos($command, '--') === 0) {
                continue;
            }
            
            try {
                $db->query($command);
                $file_success++;
                $total_success++;
            } catch (Exception $e) {
                $file_errors++;
                $total_errors++;
                
                // Mostrar apenas erros que não sejam "tabela já existe"
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "<p style='color: red; font-size: 0.9em;'>❌ " . htmlspecialchars($e->getMessage()) . "</p>\n";
                }
            }
        }
        
        echo "<p>✅ Comandos executados: $file_success | ❌ Erros: $file_errors</p>\n";
        echo "<hr>\n";
    }
    
    echo "<h3>📊 Resumo Final:</h3>\n";
    echo "<p><strong>Total de comandos executados:</strong> $total_success</p>\n";
    echo "<p><strong>Total de erros:</strong> $total_errors</p>\n";
    
    // Verificar se as principais tabelas foram criadas
    $tabelas_principais = [
        'financeiro_ativo_nao_circulante',
        'financeiro_centro_custos',
        'financeiro_fluxo_projetado',
        'financeiro_orcamento',
        'polos_boletos'
    ];
    
    $tabelas_criadas = 0;
    echo "<h3>🔍 Verificando tabelas principais:</h3>\n";
    
    foreach ($tabelas_principais as $tabela) {
        try {
            $result = $db->query("SHOW TABLES LIKE ?", [$tabela]);
            if ($result) {
                echo "<p style='color: green;'>✅ $tabela</p>\n";
                $tabelas_criadas++;
            } else {
                echo "<p style='color: red;'>❌ $tabela</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ $tabela (erro na verificação)</p>\n";
        }
    }
    
    $percentual = round(($tabelas_criadas / count($tabelas_principais)) * 100, 1);
    
    if ($tabelas_criadas === count($tabelas_principais)) {
        echo "<div style='background: #D1FAE5; border: 1px solid #10B981; color: #065F46; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
        echo "<h3>🎉 SUCESSO TOTAL!</h3>\n";
        echo "<p><strong>Todas as tabelas principais foram criadas com sucesso!</strong></p>\n";
        echo "<p>O módulo financeiro está 100% funcional e pronto para uso.</p>\n";
        echo "<p><strong>Completude:</strong> $percentual%</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #FEF3C7; border: 1px solid #F59E0B; color: #92400E; padding: 20px; border-radius: 10px; margin: 20px 0;'>\n";
        echo "<h3>⚠️ ATENÇÃO</h3>\n";
        echo "<p>Algumas tabelas podem não ter sido criadas corretamente.</p>\n";
        echo "<p><strong>Completude:</strong> $percentual%</p>\n";
        echo "<p>Verifique os erros acima e execute novamente se necessário.</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Erro fatal: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalação Completa - Módulo Financeiro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 10px;
        }
        .actions {
            background: #F3F4F6;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .actions a {
            background: #3B82F6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
            display: inline-block;
            font-weight: bold;
        }
        .actions a:hover {
            background: #2563EB;
        }
        .actions a.success {
            background: #10B981;
        }
        .actions a.success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Instalação Completa do Módulo Financeiro</h1>
        
        <div class="actions">
            <h3>🎯 Próximos Passos:</h3>
            <a href="../index.php" class="success">🏠 Acessar Módulo Financeiro</a>
            <a href="verificar_tabelas.php">🔍 Verificar Tabelas</a>
            <a href="../boletos.php">📄 Testar Boletos</a>
            <a href="../ativo_nao_circulante.php">🏢 Testar Ativo Não Circulante</a>
            <a href="../centro_custos.php">📊 Testar Centro de Custos</a>
            <a href="../fluxo_caixa_projetado.php">💰 Testar Fluxo Projetado</a>
            <a href="../orcamento.php">📋 Testar Orçamento</a>
        </div>
        
        <div style="background: #DBEAFE; border: 1px solid #3B82F6; color: #1E40AF; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>📋 O que foi instalado:</h3>
            <ul>
                <li><strong>Ativo Não Circulante:</strong> Controle de patrimônio com depreciação</li>
                <li><strong>Centro de Custos:</strong> Alocação e rateio de custos</li>
                <li><strong>Fluxo de Caixa Projetado:</strong> Projeções e aplicações financeiras</li>
                <li><strong>Orçamento e Planejamento:</strong> Controle orçamentário completo</li>
                <li><strong>Módulo de Boletos:</strong> Gestão de boletos por polo</li>
                <li><strong>Estrutura Completa:</strong> 16 tabelas com relacionamentos</li>
            </ul>
        </div>
        
        <hr>
        <p><small>Sistema Reinandus - Módulo Financeiro Completo - Versão 1.0</small></p>
    </div>
</body>
</html>
