<?php
/**
 * Webhook do Asaas para receber notificações de pagamento
 */

// Headers de segurança
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://api.asaas.com');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Log de todas as requisições recebidas
$logFile = __DIR__ . '/../logs/asaas_webhook.log';
$requestData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'headers' => getallheaders(),
    'body' => file_get_contents('php://input'),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
];

// Criar diretório de logs se não existir
if (!is_dir(dirname($logFile))) {
    mkdir(dirname($logFile), 0755, true);
}

file_put_contents($logFile, json_encode($requestData) . "\n", FILE_APPEND | LOCK_EX);

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Verificar se há dados
$input = file_get_contents('php://input');
if (empty($input)) {
    http_response_code(400);
    echo json_encode(['error' => 'No data received']);
    exit;
}

// Decodificar JSON
$dados = json_decode($input, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON']);
    exit;
}

try {
    // Incluir dependências
    require_once __DIR__ . '/../includes/init.php';
    require_once __DIR__ . '/../secretaria/includes/Database.php';
    require_once __DIR__ . '/../financeiro/includes/AsaasAPI.php';

    $db = Database::getInstance();
    $asaasAPI = new AsaasAPI($db);

    // Log detalhado do webhook
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $dados['event'] ?? 'unknown',
        'payment_id' => $dados['payment']['id'] ?? 'unknown',
        'payment_status' => $dados['payment']['status'] ?? 'unknown',
        'full_data' => $dados
    ];

    $detailedLogFile = __DIR__ . '/../logs/asaas_webhook_detailed.log';
    file_put_contents($detailedLogFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);

    // Processar webhook
    $resultado = $asaasAPI->processarWebhook($dados);
    if ($resultado['sucesso']) {
        // Log de sucesso
        $successLog = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $dados['event'] ?? 'unknown',
            'payment_id' => $dados['payment']['id'] ?? 'unknown',
            'boleto_id' => $resultado['boleto_id'] ?? 'unknown',
            'status' => 'success'
        ];

        $successLogFile = __DIR__ . '/../logs/asaas_webhook_success.log';
        file_put_contents($successLogFile, json_encode($successLog) . "\n", FILE_APPEND | LOCK_EX);

        // Resposta de sucesso
        http_response_code(200);
        echo json_encode([
            'status' => 'success',
            'message' => 'Webhook processado com sucesso',
            'boleto_id' => $resultado['boleto_id'] ?? null
        ]);
    } else {
        // Log de erro
        $errorLog = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $dados['event'] ?? 'unknown',
            'payment_id' => $dados['payment']['id'] ?? 'unknown',
            'error' => $resultado['erro'] ?? 'unknown',
            'status' => 'error'
        ];

        $errorLogFile = __DIR__ . '/../logs/asaas_webhook_errors.log';
        file_put_contents($errorLogFile, json_encode($errorLog) . "\n", FILE_APPEND | LOCK_EX);

        // Resposta de erro (mas ainda 200 para não reenviar)
        http_response_code(200);
        echo json_encode([
            'status' => 'error',
            'message' => 'Erro ao processar webhook',
            'error' => $resultado['erro'] ?? 'Erro desconhecido'
        ]);
    }

} catch (Exception $e) {
    // Log de exceção
    $exceptionLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'exception' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
        'input_data' => $dados ?? null
    ];

    $exceptionLogFile = __DIR__ . '/../logs/asaas_webhook_exceptions.log';
    file_put_contents($exceptionLogFile, json_encode($exceptionLog) . "\n", FILE_APPEND | LOCK_EX);

    // Resposta de erro interno
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}

// Função para obter headers de forma compatível
function getallheaders() {
    if (function_exists('getallheaders')) {
        return getallheaders();
    }

    $headers = [];
    foreach ($_SERVER as $name => $value) {
        if (substr($name, 0, 5) == 'HTTP_') {
            $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
        }
    }
    return $headers;
}
?>

