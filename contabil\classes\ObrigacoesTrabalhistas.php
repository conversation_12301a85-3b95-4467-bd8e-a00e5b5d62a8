<?php
/**
 * CLASSE OBRIGAÇÕES TRABALHISTAS - CORRIGIDA
 *
 * Não carrega configurações diretamente - assume que já foram carregadas
 */

class ObrigacoesTrabalhistas {
    private $db;

    public function __construct() {
        // Usa a instância já criada do Database
        $this->db = Database::getInstance();
    }
    
    /**
     * Gerar folha de pagamento mensal
     */
    public function gerarFolhaPagamento($mes_referencia, $ano_referencia) {
        $funcionarios = $this->getFuncionariosAtivos();
        $folha_id = $this->criarCabecalhoFolha($mes_referencia, $ano_referencia);
        
        foreach ($funcionarios as $funcionario) {
            $this->calcularFolhaFuncionario($folha_id, $funcionario, $mes_referencia, $ano_referencia);
        }
        
        return $folha_id;
    }
    
    /**
     * Calcular folha individual do funcionário
     */
    private function calcularFolhaFuncionario($folha_id, $funcionario, $mes, $ano) {
        $salario_base = $funcionario['salario'];
        $dias_trabalhados = $this->calcularDiasTrabalhados($funcionario['id'], $mes, $ano);
        
        // Cálculos básicos
        $salario_proporcional = ($salario_base / 30) * $dias_trabalhados;
        $inss = $this->calcularINSS($salario_proporcional);
        $irrf = $this->calcularIRRF($salario_proporcional - $inss);
        $fgts = $salario_proporcional * 0.08;
        
        // Provisões
        $ferias = $salario_proporcional / 12;
        $decimo_terceiro = $salario_proporcional / 12;
        
        // Inserir na folha
        $sql = "INSERT INTO folha_pagamento_itens 
                (folha_id, funcionario_id, salario_base, salario_liquido, inss_funcionario, 
                 irrf, fgts, provisao_ferias, provisao_13_salario, dias_trabalhados)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $salario_liquido = $salario_proporcional - $inss - $irrf;
        
        $this->db->execute($sql, [
            $folha_id, $funcionario['id'], $salario_base, $salario_liquido,
            $inss, $irrf, $fgts, $ferias, $decimo_terceiro, $dias_trabalhados
        ]);
        
        // Gerar lançamentos contábeis
        $this->gerarLancamentosFolha($funcionario, $salario_proporcional, $inss, $fgts, $mes, $ano);
    }
    
    /**
     * Calcular INSS do funcionário
     */
    private function calcularINSS($salario) {
        // Tabela INSS 2024 (simplificada)
        if ($salario <= 1412.00) return $salario * 0.075;
        if ($salario <= 2666.68) return $salario * 0.09;
        if ($salario <= 4000.03) return $salario * 0.12;
        if ($salario <= 7786.02) return $salario * 0.14;
        return 7786.02 * 0.14; // Teto
    }
    
    /**
     * Calcular IRRF
     */
    private function calcularIRRF($base_calculo) {
        // Tabela IRRF 2024 (simplificada)
        if ($base_calculo <= 2112.00) return 0;
        if ($base_calculo <= 2826.65) return ($base_calculo * 0.075) - 158.40;
        if ($base_calculo <= 3751.05) return ($base_calculo * 0.15) - 370.40;
        if ($base_calculo <= 4664.68) return ($base_calculo * 0.225) - 651.73;
        return ($base_calculo * 0.275) - 884.96;
    }
    
    /**
     * Gerar lançamentos contábeis da folha
     */
    private function gerarLancamentosFolha($funcionario, $salario, $inss, $fgts, $mes, $ano) {
        $numero_lancamento = "FOLHA-{$funcionario['id']}-{$ano}{$mes}";
        
        // Criar lançamento principal
        $sql = "INSERT INTO lancamentos_contabeis 
                (numero_lancamento, data_lancamento, historico, valor_total, tipo_lancamento)
                VALUES (?, ?, ?, ?, 'folha_pagamento')";
        
        $this->db->execute($sql, [
            $numero_lancamento,
            date('Y-m-t', mktime(0, 0, 0, $mes, 1, $ano)),
            "Folha de pagamento - {$funcionario['nome']} - {$mes}/{$ano}",
            $salario
        ]);
        
        $lancamento_id = $this->db->lastInsertId();
        
        // Débito: Despesa com salários
        $this->adicionarItemLancamento($lancamento_id, '*********', 'D', $salario, 'Salário');
        
        // Crédito: Salários a pagar
        $this->adicionarItemLancamento($lancamento_id, '*********', 'C', $salario - $inss, 'Salário líquido a pagar');
        
        // Crédito: INSS a recolher
        $this->adicionarItemLancamento($lancamento_id, '*********', 'C', $inss, 'INSS funcionário');
        
        // Débito: Despesa FGTS / Crédito: FGTS a recolher
        $this->adicionarItemLancamento($lancamento_id, '*********', 'D', $fgts, 'FGTS');
        $this->adicionarItemLancamento($lancamento_id, '2.1.3.003', 'C', $fgts, 'FGTS a recolher');
    }
}
?>