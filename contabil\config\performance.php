<?php
/**
 * CONFIGURAÇÕES DE PERFORMANCE PARA MÓDULO FINANCEIRO
 * 
 * Configurações para otimizar performance em páginas com muitos dados
 */

// Configurações de paginação
define('ALUNOS_POR_PAGINA', 20);
define('MAX_ALUNOS_BUSCA', 50);
define('MIN_CARACTERES_BUSCA', 2);

// Configurações de cache
define('CACHE_ALUNOS_TEMPO', 300); // 5 minutos
define('CACHE_POLOS_TEMPO', 3600); // 1 hora
define('CACHE_CURSOS_TEMPO', 3600); // 1 hora

// Configurações de timeout
define('AJAX_TIMEOUT', 30); // 30 segundos
define('BUSCA_DELAY', 300); // 300ms delay para busca

// Configurações de limite de dados
define('MAX_MENSALIDADES_LOTE', 1000);
define('MAX_BOLETOS_LOTE', 500);
define('MAX_RELATORIOS_REGISTROS', 10000);

// Configurações de otimização de queries
define('USE_INDEX_HINTS', true);
define('USE_QUERY_CACHE', true);
define('OPTIMIZE_JOINS', true);

/**
 * Função para verificar se deve usar cache
 */
function shouldUseCache($tipo) {
    $cache_config = [
        'alunos' => CACHE_ALUNOS_TEMPO,
        'polos' => CACHE_POLOS_TEMPO,
        'cursos' => CACHE_CURSOS_TEMPO
    ];
    
    return isset($cache_config[$tipo]) && $cache_config[$tipo] > 0;
}

/**
 * Função para obter configurações de paginação
 */
function getPaginationConfig($tipo = 'default') {
    $configs = [
        'alunos' => [
            'per_page' => ALUNOS_POR_PAGINA,
            'max_results' => MAX_ALUNOS_BUSCA
        ],
        'mensalidades' => [
            'per_page' => 50,
            'max_results' => MAX_MENSALIDADES_LOTE
        ],
        'boletos' => [
            'per_page' => 25,
            'max_results' => MAX_BOLETOS_LOTE
        ],
        'default' => [
            'per_page' => 20,
            'max_results' => 100
        ]
    ];
    
    return $configs[$tipo] ?? $configs['default'];
}

/**
 * Função para otimizar queries baseado no tipo
 */
function getOptimizedQuery($base_query, $tipo = 'select') {
    if (!USE_INDEX_HINTS) {
        return $base_query;
    }
    
    $optimizations = [
        'alunos_busca' => [
            'hints' => 'USE INDEX (idx_nome, idx_cpf)',
            'limit' => 'LIMIT ' . MAX_ALUNOS_BUSCA
        ],
        'mensalidades_count' => [
            'hints' => 'USE INDEX (idx_aluno_mes_ano)',
            'limit' => ''
        ],
        'boletos_list' => [
            'hints' => 'USE INDEX (idx_data_vencimento)',
            'limit' => 'LIMIT ' . MAX_BOLETOS_LOTE
        ]
    ];
    
    if (isset($optimizations[$tipo])) {
        $opt = $optimizations[$tipo];
        $query = str_replace('FROM alunos', 'FROM alunos ' . $opt['hints'], $base_query);
        if ($opt['limit']) {
            $query .= ' ' . $opt['limit'];
        }
        return $query;
    }
    
    return $base_query;
}

/**
 * Configurações de JavaScript para performance
 */
function getJSPerformanceConfig() {
    return [
        'busca_delay' => BUSCA_DELAY,
        'ajax_timeout' => AJAX_TIMEOUT * 1000, // Converter para ms
        'min_caracteres' => MIN_CARACTERES_BUSCA,
        'max_resultados' => MAX_ALUNOS_BUSCA,
        'cache_tempo' => CACHE_ALUNOS_TEMPO
    ];
}

/**
 * Headers para otimização de performance
 */
function setPerformanceHeaders() {
    // Cache headers
    header('Cache-Control: public, max-age=300'); // 5 minutos
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 300) . ' GMT');
    
    // Compression
    if (extension_loaded('zlib') && !ob_get_level()) {
        ob_start('ob_gzhandler');
    }
    
    // Performance headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: SAMEORIGIN');
}

/**
 * Função para log de performance
 */
function logPerformance($action, $start_time, $query_count = 0) {
    $execution_time = microtime(true) - $start_time;
    $memory_usage = memory_get_peak_usage(true);
    
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => $action,
        'execution_time' => round($execution_time, 4),
        'memory_usage' => round($memory_usage / 1024 / 1024, 2) . 'MB',
        'query_count' => $query_count
    ];
    
    // Log apenas se execução demorar mais que 1 segundo
    if ($execution_time > 1.0) {
        error_log('PERFORMANCE WARNING: ' . json_encode($log_entry));
    }
    
    return $log_entry;
}

/**
 * Configurações específicas para diferentes páginas
 */
function getPageConfig($page) {
    $configs = [
        'mensalidades_gerar' => [
            'lazy_load' => true,
            'ajax_search' => true,
            'cache_enabled' => true,
            'max_results' => MAX_ALUNOS_BUSCA
        ],
        'boletos_gerar' => [
            'lazy_load' => true,
            'ajax_search' => true,
            'cache_enabled' => true,
            'max_results' => MAX_BOLETOS_LOTE
        ],
        'relatorios' => [
            'lazy_load' => false,
            'ajax_search' => false,
            'cache_enabled' => true,
            'max_results' => MAX_RELATORIOS_REGISTROS
        ]
    ];
    
    return $configs[$page] ?? [
        'lazy_load' => false,
        'ajax_search' => false,
        'cache_enabled' => false,
        'max_results' => 100
    ];
}

/**
 * Função para verificar se sistema está sob alta carga
 */
function isHighLoad() {
    // Verificar uso de CPU (se disponível)
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        if ($load[0] > 2.0) { // Load average > 2.0
            return true;
        }
    }
    
    // Verificar uso de memória
    $memory_limit = ini_get('memory_limit');
    $memory_usage = memory_get_usage(true);
    
    if ($memory_limit !== '-1') {
        $limit_bytes = return_bytes($memory_limit);
        if ($memory_usage / $limit_bytes > 0.8) { // Mais de 80% da memória
            return true;
        }
    }
    
    return false;
}

/**
 * Converter string de memória para bytes
 */
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int) $val;
    
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    
    return $val;
}

/**
 * Configurações adaptativas baseadas na carga do sistema
 */
function getAdaptiveConfig() {
    if (isHighLoad()) {
        return [
            'alunos_por_pagina' => 10,
            'max_busca' => 20,
            'cache_tempo' => 600, // 10 minutos
            'delay_busca' => 500
        ];
    }
    
    return [
        'alunos_por_pagina' => ALUNOS_POR_PAGINA,
        'max_busca' => MAX_ALUNOS_BUSCA,
        'cache_tempo' => CACHE_ALUNOS_TEMPO,
        'delay_busca' => BUSCA_DELAY
    ];
}
?>
