<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de Matrículas - Faciencia EAD</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-purple: #6A5ACD;
            --secondary-purple: #483D8B;
            --light-purple: #9370DB;
            --very-light-purple: #E6E6FA;
            --white: #FFFFFF;
            --light-bg: #F4F4F9;
            --text-dark: #333333;
            --text-muted: #6c757d;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
            --border-radius: 10px;
            --card-shadow: 0 6px 15px rgba(106, 90, 205, 0.1);
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        /* Sidebar Moderna */
        .sidebar {
            background: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
            color: var(--white);
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            position: relative;
            z-index: 10;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar-logo i {
            font-size: 2rem;
            margin-right: 10px;
        }

        .sidebar-logo h2 {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section-header {
            font-size: 0.85rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.6);
            margin-left: 10px;
            margin-bottom: 15px;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding-left: 0;
            margin-bottom: 30px;
        }

        .sidebar-menu li {
            margin-bottom: 8px;
        }

        .sidebar-menu a {
            color: var(--white);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed) ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu a:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar-menu a.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background-color: var(--white);
            border-radius: 0 2px 2px 0;
        }

        .sidebar-menu a i {
            margin-right: 15px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            transition: all var(--transition-speed) ease;
        }

        .sidebar-menu a:hover i {
            transform: scale(1.2);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
        }

        /* Conteúdo Principal */
        .main-content {
            background-color: var(--light-bg);
            padding: 30px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--very-light-purple);
        }

        .dashboard-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--secondary-purple);
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .polo-info {
            text-align: right;
        }

        .polo-info .polo-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--secondary-purple);
        }

        .polo-info .polo-role {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .user-avatar {
            position: relative;
            cursor: pointer;
        }

        .user-avatar img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--white);
        }

        /* Cards e Elementos de UI */
        .dashboard-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 25px;
            margin-bottom: 30px;
            transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-purple), var(--light-purple));
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
            gap: 25px;
        }

        .stat-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(106, 90, 205, 0.15);
        }

        .stat-card.total-matriculas {
            background: linear-gradient(135deg, rgba(106, 90, 205, 0.08), rgba(106, 90, 205, 0.01));
        }

        .stat-card.matriculas-mes {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.08), rgba(40, 167, 69, 0.01));
        }

        .stat-card.taxa-renovacao {
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.08), rgba(23, 162, 184, 0.01));
        }

        .stat-card.pendentes {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.08), rgba(255, 193, 7, 0.01));
        }

        .stat-icon {
            width: 55px;
            height: 55px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-card.total-matriculas .stat-icon {
            background: linear-gradient(135deg, var(--primary-purple), var(--light-purple));
        }

        .stat-card.matriculas-mes .stat-icon {
            background: linear-gradient(135deg, #28a745, #5dd879);
        }

        .stat-card.taxa-renovacao .stat-icon {
            background: linear-gradient(135deg, #17a2b8, #4dc0d1);
        }

        .stat-card.pendentes .stat-icon {
            background: linear-gradient(135deg, #ffc107, #ffe066);
        }

        .stat-content {
            flex: 1;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
            line-height: 1.2;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-muted);
            margin-bottom: 0;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 0.85rem;
            margin-top: 5px;
        }

        .trend-up {
            color: var(--success-green);
        }

        .trend-down {
            color: var(--danger-red);
        }

        /* Tabelas Estilizadas */
        .custom-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom-table thead th {
            background-color: var(--very-light-purple);
            color: var(--secondary-purple);
            font-weight: 600;
            padding: 15px;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
        }

        .custom-table thead th:first-child {
            border-top-left-radius: 10px;
        }

        .custom-table thead th:last-child {
            border-top-right-radius: 10px;
        }

        .custom-table tbody tr {
            transition: all var(--transition-speed) ease;
        }

        .custom-table tbody tr:hover {
            background-color: rgba(106, 90, 205, 0.05);
            transform: scale(1.01);
        }

        .custom-table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-top: 1px solid #eee;
            font-size: 0.95rem;
        }

        .custom-table tbody tr:first-child td {
            border-top: none;
        }

        /* Status Badges */
        .badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .badge.bg-success {
            background-color: rgba(40, 167, 69, 0.15) !important;
            color: var(--success-green);
        }

        .badge.bg-warning {
            background-color: rgba(255, 193, 7, 0.15) !important;
            color: #d18700;
        }

        .badge.bg-danger {
            background-color: rgba(220, 53, 69, 0.15) !important;
            color: var(--danger-red);
        }

        .badge.bg-info {
            background-color: rgba(23, 162, 184, 0.15) !important;
            color: var(--info-blue);
        }

        .badge.bg-secondary {
            background-color: rgba(108, 117, 125, 0.15) !important;
            color: var(--text-muted);
        }

        /* Filtros e Pesquisa */
        .filters-bar {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
        }

        .search-box {
            flex-grow: 1;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border-radius: 50px;
            border: 1px solid #eee;
            background-color: var(--white);
            transition: all var(--transition-speed) ease;
        }

        .search-box input:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--light-purple);
            border-color: var(--light-purple);
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .filters-dropdown {
            min-width: 160px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-icon {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Form Elements */
        .form-floating label {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .form-select:focus, .form-control:focus {
            border-color: var(--light-purple);
            box-shadow: 0 0 0 0.2rem rgba(106, 90, 205, 0.25);
        }

        /* Botões e Ações */
        .btn-primary {
            background-color: var(--primary-purple);
            border-color: var(--primary-purple);
        }

        .btn-primary:hover {
            background-color: var(--secondary-purple);
            border-color: var(--secondary-purple);
        }

        .btn-outline-primary {
            color: var(--primary-purple);
            border-color: var(--primary-purple);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-purple);
            color: white;
        }

        .btn-outline-danger {
            color: var(--danger-red);
            border-color: var(--danger-red);
        }

        .btn-outline-danger:hover {
            background-color: var(--danger-red);
            color: white;
        }

        /* Cards Headers */
        .card-header-custom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .card-header-custom h4 {
            font-weight: 600;
            color: var(--secondary-purple);
            margin: 0;
            font-size: 1.25rem;
        }

        /* Steps/Timeline */
        .timeline {
            position: relative;
            margin-top: 20px;
            margin-bottom: 20px;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 2px;
            background-color: var(--very-light-purple);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -34px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--light-purple);
            border: 4px solid var(--white);
            box-shadow: 0 0 0 2px var(--light-purple);
        }

        .timeline-item.completed::before {
            background-color: var(--success-green);
            box-shadow: 0 0 0 2px var(--success-green);
        }

        .timeline-item.current::before {
            background-color: var(--primary-purple);
            box-shadow: 0 0 0 2px var(--primary-purple);
        }

        .timeline-item.pending::before {
            background-color: var(--white);
            box-shadow: 0 0 0 2px var(--light-purple);
        }

        .timeline-content {
            background-color: var(--white);
            border-radius: var(--border-radius);
            padding: 15px;
            box-shadow: var(--card-shadow);
        }

        .timeline-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .timeline-step {
            font-weight: 600;
            font-size: 1.05rem;
            color: var(--text-dark);
        }

        .timeline-date {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .timeline-description {
            font-size: 0.95rem;
            color: var(--text-muted);
        }

        /* Mobile Responsiveness */
        @media (max-width: 992px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -280px;
                height: 100vh;
                width: 280px;
                z-index: 1000;
                transition: left var(--transition-speed) ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 20px 15px;
            }
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .user-info {
                align-self: flex-end;
            }

            .filters-bar {
                flex-direction: column;
            }

            .action-buttons {
                width: 100%;
            }

            .action-buttons .btn {
                flex: 1;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-grid">
        <!-- Sidebar Aprimorada -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-graduation-cap"></i>
                <h2>Faciencia</h2>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Principal</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_dashboard.html">
                            <i class="fas fa-tachometer-alt"></i> Painel Geral
                        </a>
                    </li>
                    <li>
                        <a href="polo_cursos.html">
                            <i class="fas fa-book-open"></i> Cursos
                        </a>
                    </li>
                    <li>
                        <a href="polo_aluno.html">
                            <i class="fas fa-users"></i> Alunos
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Gestão</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_matriculas.html" class="active">
                            <i class="fas fa-user-plus"></i> Matrículas
                        </a>
                    </li>
                    <li>
                        <a href="polo_certificados.html">
                            <i class="fas fa-certificate"></i> Certificados
                        </a>
                    </li>
                    <li>
                        <a href="polo_relatorio.html">
                            <i class="fas fa-chart-bar"></i> Relatórios
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-header">Sistema</div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="polo_config.html">
                            <i class="fas fa-cogs"></i> Configurações
                        </a>
                    </li>
                    <li>
                        <a href="polo_suporte.html">
                            <i class="fas fa-headset"></i> Suporte
                        </a>
                    </li>
                    <li>
                        <a href="index.html" class="text-danger">
                            <i class="fas fa-sign-out-alt"></i> Sair
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <p>Faciencia EAD © 2024</p>
                <small>Versão 2.5.3</small>
            </div>
        </aside>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Cabeçalho do Painel -->
            <header class="dashboard-header">
                <h1>Gerenciamento de Matrículas</h1>
                <div class="user-info">
                    <div class="polo-info">
                        <div class="polo-name">Polo São Paulo</div>
                        <div class="polo-role">Administrador</div>
                    </div>
                    <div class="user-avatar">
                        <img src="/api/placeholder/50/50" alt="Foto de Perfil">
                        <span class="notification-badge">3</span>
                    </div>
                </div>
            </header>

            <!-- Estatísticas de Matrículas -->
            <section class="stats-grid">
                <div class="stat-card total-matriculas">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">423</div>
                        <div class="stat-label">Total de Alunos Matriculados</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up me-1"></i>
                            9.2% desde o último trimestre
                        </div>
                    </div>
                </div>
                <div class="stat-card matriculas-mes">
                    <div class="stat-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">47</div>
                        <div class="stat-label">Matrículas do Mês</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up me-1"></i>
                            12 mais que no mês anterior
                        </div>
                    </div>
                </div>
                <div class="stat-card taxa-renovacao">
                    <div class="stat-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">85.3%</div>
                        <div class="stat-label">Taxa de Renovação</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up me-1"></i>
                            3.2% em relação ao mês anterior
                        </div>
                    </div>
                </div>
                <div class="stat-card pendentes">
                    <div class="stat-icon">
                        <i class="fas fa-hourglass-half"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">12</div>
                        <div class="stat-label">Matrículas Pendentes</div>
                        <div class="stat-trend trend-down">
                            <i class="fas fa-arrow-down me-1"></i>
                            3 menos que no mês anterior
                        </div>
                    </div>
                </div>
            </section>

            <!-- Filtros e Barra de Ações -->
            <section class="filters-bar">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" class="form-control" placeholder="Buscar matrículas...">
                </div>
                
                <select class="form-select filters-dropdown">
                    <option selected>Todos os Cursos</option>
                    <option>Marketing Digital</option>
                    <option>Desenvolvimento Web</option>
                    <option>UX/UI Design</option>
                    <option>Gestão de Negócios</option>
                    <option>Inglês para Negócios</option>
                </select>
                
                <select class="form-select filters-dropdown">
                    <option selected>Todos os Status</option>
                    <option>Ativo</option>
                    <option>Pendente</option>
                    <option>Cancelado</option>
                    <option>Em Análise</option>
                </select>
                
                <div class="action-buttons">
                    <button class="btn btn-primary btn-icon" data-bs-toggle="modal" data-bs-target="#novaMatriculaModal">
                        <i class="fas fa-user-plus"></i> Nova Matrícula
                    </button>
                    <button class="btn btn-outline-primary btn-icon">
                        <i class="fas fa-file-export"></i> Exportar
                    </button>
                </div>
            </section>

            <!-- Últimas Matrículas -->
            <section class="dashboard-card">
                <div class="card-header-custom">
                    <h4>Matrículas Recentes</h4>
                    <div>
                        <span class="badge bg-primary">12 novas esta semana</span>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table custom-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Aluno</th>
                                <th>Curso</th>
                                <th>Data</th>
                                <th>Valor</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#MAT-7895</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">João Silva</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>Marketing Digital Avançado</td>
                                <td>15/05/2024</td>
                                <td>R$ 899,00</td>
                                <td><span class="badge bg-success">Confirmada</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Comprovante">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#MAT-7894</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Maria Souza</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>Desenvolvimento Web Full Stack</td>
                                <td>13/05/2024</td>
                                <td>R$ 1.199,00</td>
                                <td><span class="badge bg-success">Confirmada</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Comprovante">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#MAT-7893</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Pedro Santos</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>UX/UI Design</td>
                                <td>10/05/2024</td>
                                <td>R$ 999,00</td>
                                <td><span class="badge bg-success">Confirmada</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Comprovante">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#MAT-7892</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Ana Oliveira</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>Gestão de Negócios Digitais</td>
                                <td>08/05/2024</td>
                                <td>R$ 849,00</td>
                                <td><span class="badge bg-warning">Pendente</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Verificar Pagamento">
                                            <i class="fas fa-money-check-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#MAT-7891</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Carlos Mendes</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>Inglês para Negócios</td>
                                <td>05/05/2024</td>
                                <td>R$ 699,00</td>
                                <td><span class="badge bg-info">Em Análise</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-success" title="Aprovar">
                                            <i class="fas fa-check"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>#MAT-7890</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Juliana Ferreira</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>Marketing Digital Avançado</td>
                                <td>03/05/2024</td>
                                <td>R$ 899,00</td>
                                <td><span class="badge bg-danger">Cancelada</span></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Histórico">
                                            <i class="fas fa-history"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Restaurar">
                                            <i class="fas fa-redo-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>Mostrando 6 de 47 matrículas</div>
                    <nav>
                        <ul class="pagination">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">Próximo</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </section>

            <!-- Matrículas Pendentes e Análises -->
            <section class="dashboard-card">
                <div class="card-header-custom">
                    <h4>Matrículas que Requerem Atenção</h4>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        Ver Todas
                    </a>
                </div>
                
                <div class="table-responsive">
                    <table class="table custom-table">
                        <thead>
                            <tr>
                                <th>Aluno</th>
                                <th>Curso</th>
                                <th>Problema</th>
                                <th>Status</th>
                                <th>Data</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Ana Oliveira</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>Gestão de Negócios Digitais</td>
                                <td>Pagamento pendente</td>
                                <td><span class="badge bg-warning">Pendente</span></td>
                                <td>08/05/2024</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Enviar Lembrete">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Verificar Pagamento">
                                            <i class="fas fa-money-check-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Carlos Mendes</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>Inglês para Negócios</td>
                                <td>Documentação incompleta</td>
                                <td><span class="badge bg-info">Em Análise</span></td>
                                <td>05/05/2024</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Solicitar Documentos">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-success" title="Aprovar Mesmo Assim">
                                            <i class="fas fa-check"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="/api/placeholder/40/40" class="rounded-circle me-3" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">Roberto Alves</div>
                                            <div class="small text-muted"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>UX/UI Design</td>
                                <td>Problema no pagamento</td>
                                <td><span class="badge bg-warning">Pendente</span></td>
                                <td>30/04/2024</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Contatar Aluno">
                                            <i class="fas fa-phone-alt"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Gerar Novo Boleto">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <div class="row">
                <!-- Processo de Matrícula -->
                <div class="col-lg-7">
                    <section class="dashboard-card">
                        <div class="card-header-custom">
                            <h4>Processo de Matrícula</h4>
                        </div>
                        
                        <div class="timeline">
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-title">
                                        <div class="timeline-step">1. Cadastro do Aluno</div>
                                        <div class="timeline-date">Tempo médio: 5 minutos</div>
                                    </div>
                                    <div class="timeline-description">
                                        Registro das informações básicas do aluno: nome completo, RG, CPF, endereço, e-mail e telefone.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-title">
                                        <div class="timeline-step">2. Seleção de Curso</div>
                                        <div class="timeline-date">Tempo médio: 2 minutos</div>
                                    </div>
                                    <div class="timeline-description">
                                        Escolha do curso desejado, turma, horário e verificação de disponibilidade de vagas.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-title">
                                        <div class="timeline-step">3. Envio de Documentação</div>
                                        <div class="timeline-date">Tempo médio: 10 minutos</div>
                                    </div>
                                    <div class="timeline-description">
                                        Upload dos documentos necessários: RG, CPF, comprovante de residência e foto 3x4.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item current">
                                <div class="timeline-content">
                                    <div class="timeline-title">
                                        <div class="timeline-step">4. Processamento de Pagamento</div>
                                        <div class="timeline-date">Tempo médio: 5 minutos</div>
                                    </div>
                                    <div class="timeline-description">
                                        Escolha da forma de pagamento (cartão, boleto, transferência) e processamento da transação.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item pending">
                                <div class="timeline-content">
                                    <div class="timeline-title">
                                        <div class="timeline-step">5. Confirmação da Matrícula</div>
                                        <div class="timeline-date">Tempo médio: 24 horas</div>
                                    </div>
                                    <div class="timeline-description">
                                        Após a confirmação do pagamento, o aluno recebe um e-mail com os dados de acesso à plataforma.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item pending">
                                <div class="timeline-content">
                                    <div class="timeline-title">
                                        <div class="timeline-step">6. Início do Curso</div>
                                        <div class="timeline-date">De acordo com o calendário acadêmico</div>
                                    </div>
                                    <div class="timeline-description">
                                        O aluno recebe o material do curso e as instruções para começar as aulas conforme o cronograma.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
                
                <!-- Desempenho de Matrículas -->
                <div class="col-lg-5">
                    <section class="dashboard-card">
                        <div class="card-header-custom">
                            <h4>Desempenho de Matrículas</h4>
                        </div>
                        
                        <div style="height: 250px; background-color: var(--very-light-purple); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                            <p class="text-muted">Gráfico: Matrículas por Mês (2024)</p>
                        </div>
                        
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <h3 class="text-primary mb-1">82%</h3>
                                    <p class="mb-0 text-muted small">Taxa de Conclusão</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <h3 class="text-danger mb-1">7%</h3>
                                    <p class="mb-0 text-muted small">Taxa de Desistência</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <h3 class="text-info mb-1">8.2</h3>
                                    <p class="mb-0 text-muted small">Satisfação (0-10)</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <h3 class="text-success mb-1">91%</h3>
                                    <p class="mb-0 text-muted small">Renovações</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal de Nova Matrícula -->
    <div class="modal fade" id="novaMatriculaModal" tabindex="-1" aria-labelledby="novaMatriculaModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="novaMatriculaModalLabel">Nova Matrícula</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <!-- Seção de Informações do Aluno -->
                        <h6 class="text-primary mb-3">Informações do Aluno</h6>
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="nomeAluno" placeholder="Nome completo">
                                    <label for="nomeAluno">Nome Completo</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control" id="emailAluno" placeholder="E-mail">
                                    <label for="emailAluno">E-mail</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="cpfAluno" placeholder="CPF">
                                    <label for="cpfAluno">CPF</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="telefoneAluno" placeholder="Telefone">
                                    <label for="telefoneAluno">Telefone</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Seção de Informações do Curso -->
                        <h6 class="text-primary mb-3">Informações do Curso</h6>
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="cursoSelect">
                                        <option selected disabled>Selecione um curso</option>
                                        <option>Marketing Digital Avançado</option>
                                        <option>Desenvolvimento Web Full Stack</option>
                                        <option>UX/UI Design</option>
                                        <option>Gestão de Negócios Digitais</option>
                                        <option>Inglês para Negócios</option>
                                    </select>
                                    <label for="cursoSelect">Curso</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="turmaSelect">
                                        <option selected disabled>Selecione uma turma</option>
                                        <option>Turma A - Manhã</option>
                                        <option>Turma B - Tarde</option>
                                        <option>Turma C - Noite</option>
                                        <option>Turma D - Fins de Semana</option>
                                    </select>
                                    <label for="turmaSelect">Turma</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="dataInicio" placeholder="Data de Início">
                                    <label for="dataInicio">Data de Início</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="formaIngresso">
                                        <option selected disabled>Selecione</option>
                                        <option>Matrícula Direta</option>
                                        <option>Transferência</option>
                                        <option>Bolsa de Estudos</option>
                                        <option>Convênio Empresarial</option>
                                    </select>
                                    <label for="formaIngresso">Forma de Ingresso</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Seção de Informações de Pagamento -->
                        <h6 class="text-primary mb-3">Informações de Pagamento</h6>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="formaPagamento">
                                        <option selected disabled>Selecione</option>
                                        <option>Cartão de Crédito</option>
                                        <option>Boleto Bancário</option>
                                        <option>Transferência Bancária</option>
                                        <option>PIX</option>
                                    </select>
                                    <label for="formaPagamento">Forma de Pagamento</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="parcelas">
                                        <option selected disabled>Selecione</option>
                                        <option>À Vista</option>
                                        <option>2x sem juros</option>
                                        <option>3x sem juros</option>
                                        <option>6x com juros</option>
                                        <option>12x com juros</option>
                                    </select>
                                    <label for="parcelas">Parcelas</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control" id="valorTotal" placeholder="Valor Total">
                                    <label for="valorTotal">Valor Total (R$)</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control" id="desconto" placeholder="Desconto">
                                    <label for="desconto">Desconto (%)</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary">Cadastrar Matrícula</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle com Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Script para Mobile Sidebar Toggle -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Adicionar botão para mobile
            const mobileToggle = document.createElement('button');
            mobileToggle.classList.add('btn', 'btn-primary', 'd-md-none', 'position-fixed');
            mobileToggle.style.top = '10px';
            mobileToggle.style.left = '10px';
            mobileToggle.style.zIndex = '1001';
            mobileToggle.innerHTML = '<i class="fas fa-bars"></i>';
            document.body.appendChild(mobileToggle);
            
            // Adicionar funcionalidade de toggle
            mobileToggle.addEventListener('click', function() {
                const sidebar = document.querySelector('.sidebar');
                sidebar.classList.toggle('show');
            });
            
            // Fechar sidebar ao clicar fora dela (em mobile)
            document.addEventListener('click', function(event) {
                const sidebar = document.querySelector('.sidebar');
                const mobileToggle = document.querySelector('.btn-primary.d-md-none');
                
                if (!sidebar.contains(event.target) && event.target !== mobileToggle) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>