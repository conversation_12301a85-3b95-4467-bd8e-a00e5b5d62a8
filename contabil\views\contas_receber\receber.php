<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-dollar-sign text-green-500 mr-2"></i>
                Registrar Recebimento
            </h3>
            <a href="contas_receber.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <!-- Informações da Conta -->
    <div class="p-6 bg-gray-50 border-b border-gray-200">
        <h4 class="text-md font-semibold text-gray-800 mb-4">Informações da Conta</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-600">Descrição</label>
                <p class="text-sm text-gray-900 font-medium"><?php echo htmlspecialchars($conta['descricao']); ?></p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-600">Cliente</label>
                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($conta['cliente_nome']); ?></p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-600">Valor Original</label>
                <p class="text-sm text-gray-900 font-semibold">R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?></p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-600">Vencimento</label>
                <p class="text-sm text-gray-900">
                    <?php echo date('d/m/Y', strtotime($conta['data_vencimento'])); ?>
                    <?php if ($conta['data_vencimento'] < date('Y-m-d')): ?>
                        <span class="text-red-600 font-medium">(Vencida)</span>
                    <?php endif; ?>
                </p>
            </div>
        </div>
        
        <?php if ($conta['observacoes']): ?>
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-600">Observações</label>
                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($conta['observacoes']); ?></p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Formulário de Recebimento -->
    <form method="POST" class="p-6">
        <input type="hidden" name="acao" value="receber">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Valor Recebido -->
            <div>
                <label for="valor_recebido" class="block text-sm font-medium text-gray-700 mb-2">
                    Valor Recebido <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-2 text-gray-500">R$</span>
                    <input type="text" id="valor_recebido" name="valor_recebido" required
                           value="<?php echo number_format($conta['valor'], 2, ',', '.'); ?>"
                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                           onkeyup="formatarMoeda(this); calcularDiferenca()">
                </div>
                <div id="diferenca-info" class="mt-1 text-sm"></div>
            </div>

            <!-- Data do Recebimento -->
            <div>
                <label for="data_recebimento" class="block text-sm font-medium text-gray-700 mb-2">
                    Data do Recebimento <span class="text-red-500">*</span>
                </label>
                <input type="date" id="data_recebimento" name="data_recebimento" required
                       value="<?php echo date('Y-m-d'); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
            </div>

            <!-- Forma de Pagamento -->
            <div>
                <label for="forma_pagamento" class="block text-sm font-medium text-gray-700 mb-2">
                    Forma de Pagamento <span class="text-red-500">*</span>
                </label>
                <select id="forma_pagamento" name="forma_pagamento" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Selecione a forma</option>
                    <option value="dinheiro">Dinheiro</option>
                    <option value="pix">PIX</option>
                    <option value="transferencia">Transferência Bancária</option>
                    <option value="debito">Cartão de Débito</option>
                    <option value="credito">Cartão de Crédito</option>
                    <option value="cheque">Cheque</option>
                    <option value="boleto">Boleto Bancário</option>
                    <option value="outros">Outros</option>
                </select>
            </div>

            <!-- Conta Bancária (se aplicável) -->
            <div id="conta-bancaria-div" style="display: none;">
                <label for="conta_bancaria" class="block text-sm font-medium text-gray-700 mb-2">
                    Conta Bancária
                </label>
                <select id="conta_bancaria" name="conta_bancaria"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Selecione a conta</option>
                    <!-- Aqui seriam carregadas as contas bancárias do banco de dados -->
                    <option value="1">Banco Itaú - Conta Corrente</option>
                    <option value="2">Banco Inter - Conta Corrente</option>
                    <option value="3">C6 Bank - Conta Corrente</option>
                </select>
            </div>

            <!-- Observações do Recebimento -->
            <div class="md:col-span-2">
                <label for="observacoes_recebimento" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações do Recebimento
                </label>
                <textarea id="observacoes_recebimento" name="observacoes_recebimento" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="Informações adicionais sobre o recebimento..."></textarea>
            </div>
        </div>

        <!-- Resumo do Recebimento -->
        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 class="text-md font-semibold text-green-800 mb-2">Resumo do Recebimento</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <span class="text-green-600">Valor Original:</span>
                    <span class="font-semibold text-green-800">R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?></span>
                </div>
                <div>
                    <span class="text-green-600">Valor a Receber:</span>
                    <span id="valor-receber" class="font-semibold text-green-800">R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?></span>
                </div>
                <div>
                    <span class="text-green-600">Diferença:</span>
                    <span id="diferenca-valor" class="font-semibold">R$ 0,00</span>
                </div>
            </div>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="contas_receber.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-check mr-2"></i>
                Confirmar Recebimento
            </button>
        </div>
    </form>
</div>

<!-- Alerta de Confirmação -->
<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Atenção</h3>
            <div class="mt-2 text-sm text-yellow-700">
                <p>Ao confirmar o recebimento, a conta será marcada como recebida e uma transação financeira será registrada no sistema. Esta ação não pode ser desfeita facilmente.</p>
            </div>
        </div>
    </div>
</div>

<script>
const valorOriginal = <?php echo $conta['valor']; ?>;

function calcularDiferenca() {
    const valorRecebidoInput = document.getElementById('valor_recebido');
    const valorRecebidoStr = valorRecebidoInput.value.replace(/\./g, '').replace(',', '.');
    const valorRecebido = parseFloat(valorRecebidoStr) || 0;
    
    const diferenca = valorRecebido - valorOriginal;
    const diferencaInfo = document.getElementById('diferenca-info');
    const diferencaValor = document.getElementById('diferenca-valor');
    const valorReceber = document.getElementById('valor-receber');
    
    // Atualiza o valor a receber
    valorReceber.textContent = 'R$ ' + valorRecebido.toLocaleString('pt-BR', {minimumFractionDigits: 2});
    
    // Atualiza a diferença
    diferencaValor.textContent = 'R$ ' + Math.abs(diferenca).toLocaleString('pt-BR', {minimumFractionDigits: 2});
    
    if (diferenca > 0) {
        diferencaInfo.textContent = 'Valor maior que o original (juros/multa)';
        diferencaInfo.className = 'mt-1 text-sm text-blue-600';
        diferencaValor.className = 'font-semibold text-blue-600';
    } else if (diferenca < 0) {
        diferencaInfo.textContent = 'Valor menor que o original (desconto/parcial)';
        diferencaInfo.className = 'mt-1 text-sm text-orange-600';
        diferencaValor.className = 'font-semibold text-orange-600';
    } else {
        diferencaInfo.textContent = 'Valor igual ao original';
        diferencaInfo.className = 'mt-1 text-sm text-green-600';
        diferencaValor.className = 'font-semibold text-green-600';
    }
}

// Mostrar/ocultar campo de conta bancária baseado na forma de pagamento
document.getElementById('forma_pagamento').addEventListener('change', function() {
    const contaBancariaDiv = document.getElementById('conta-bancaria-div');
    const formasComConta = ['pix', 'transferencia', 'debito', 'credito'];
    
    if (formasComConta.includes(this.value)) {
        contaBancariaDiv.style.display = 'block';
        document.getElementById('conta_bancaria').required = true;
    } else {
        contaBancariaDiv.style.display = 'none';
        document.getElementById('conta_bancaria').required = false;
    }
});

// Confirmação antes de enviar
document.querySelector('form').addEventListener('submit', function(e) {
    const valorRecebido = document.getElementById('valor_recebido').value;
    const formaPagamento = document.getElementById('forma_pagamento').value;
    
    if (!confirm(`Confirma o recebimento de R$ ${valorRecebido} via ${formaPagamento}?`)) {
        e.preventDefault();
    }
});

// Calcular diferença inicial
calcularDiferenca();
</script>
