<?php
/**
 * ================================================================
 *                    SISTEMA FACIÊNCIA ERP
 * ================================================================
 * 
 * Script AJAX: Buscar Professores
 * Descrição: Busca professores para autocomplete em formulários
 * Versão: 1.0
 * Data: 2024-12-19
 * 
 * Funcionalidades:
 * - Busca professores por nome (parcial)
 * - Suporte a paginação
 * - Busca rápida para autocomplete
 * - Listagem geral de professores
 * 
 * ================================================================
 */

// Carregamento do sistema base
require_once __DIR__ . '/../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica se o usuário tem permissão para acessar professores
exigirPermissao('disciplinas');

// Instancia o banco de dados
$db = Database::getInstance();

// Define o tipo de retorno como JSON
header('Content-Type: application/json');

try {
    $query = $_GET['q'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $per_page = (int)($_GET['per_page'] ?? 20);
    $offset = ($page - 1) * $per_page;

    // Validação básica
    $query = trim($query);
    
    if (strlen($query) < 2 && empty($_GET['all'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Digite pelo menos 2 caracteres para buscar',
            'professores' => []
        ]);
        exit;
    }

    // Monta a consulta SQL
    $where = "status = 'ativo'";
    $params = [];

    if (!empty($query)) {
        $where .= " AND (nome LIKE ? OR email LIKE ? OR formacao LIKE ?)";
        $searchTerm = '%' . $query . '%';
        $params = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Consulta principal com paginação
    $sql = "SELECT id, nome, email, formacao, created_at
            FROM professores 
            WHERE {$where}
            ORDER BY nome ASC 
            LIMIT {$offset}, {$per_page}";

    $professores = $db->fetchAll($sql, $params);

    // Conta total para paginação
    $sql_count = "SELECT COUNT(*) as total FROM professores WHERE {$where}";
    $total_result = $db->fetchOne($sql_count, $params);
    $total = $total_result['total'] ?? 0;

    // Calcula informações de paginação
    $total_pages = $total > 0 ? ceil($total / $per_page) : 1;
    $has_next = $page < $total_pages;
    $has_prev = $page > 1;

    // Formata os dados dos professores
    $professores_formatados = [];
    foreach ($professores as $professor) {
        $professores_formatados[] = [
            'id' => $professor['id'],
            'nome' => $professor['nome'],
            'email' => $professor['email'] ?? '',
            'formacao' => $professor['formacao'] ?? '',
            'texto_completo' => $professor['nome'] . 
                              ($professor['email'] ? ' (' . $professor['email'] . ')' : '') .
                              ($professor['formacao'] ? ' - ' . $professor['formacao'] : ''),
            'created_at' => $professor['created_at']
        ];
    }

    echo json_encode([
        'success' => true,
        'professores' => $professores_formatados,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $per_page,
            'total' => $total,
            'total_pages' => $total_pages,
            'has_next' => $has_next,
            'has_prev' => $has_prev
        ],
        'search' => [
            'query' => $query,
            'found' => count($professores_formatados)
        ]
    ]);

} catch (Exception $e) {
    error_log('Erro na busca de professores: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Erro interno do servidor',
        'professores' => []
    ]);
}
?>
