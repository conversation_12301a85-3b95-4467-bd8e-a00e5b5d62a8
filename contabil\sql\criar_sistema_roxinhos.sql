-- ============================================================================
-- SISTEMA DE ROXINHOS (INDICADORES DE POLOS PARCEIROS)
-- ============================================================================
-- Sistema para gerenciar comissões de indicadores de polos parceiros
-- Os "Roxinhos" recebem percentual sobre contratos de polos que indicaram
-- ============================================================================

-- Tabela de Roxinhos (Indicadores)
CREATE TABLE IF NOT EXISTS roxinhos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    cpf VARCHAR(14) UNIQUE,
    email VARCHAR(255),
    telefone VARCHAR(20),
    endereco TEXT,
    banco VARCHAR(100),
    agencia VARCHAR(10),
    conta VARCHAR(20),
    pix VARCHAR(255),
    taxa_padrao DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Taxa padrão em %',
    status ENUM('ativo', 'inativo', 'suspenso') DEFAULT 'ativo',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_nome (nome),
    INDEX idx_cpf (cpf),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de Vínculos Roxinho-Polo
CREATE TABLE IF NOT EXISTS roxinho_polos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    roxinho_id INT NOT NULL,
    polo_id INT UNSIGNED NOT NULL,
    taxa_especifica DECIMAL(5,2) COMMENT 'Taxa específica para este polo (sobrescreve a padrão)',
    data_inicio DATE NOT NULL,
    data_fim DATE NULL COMMENT 'NULL = vínculo ativo',
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_roxinho (roxinho_id),
    INDEX idx_polo (polo_id),
    INDEX idx_status (status),
    INDEX idx_data_inicio (data_inicio)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de Comissões de Roxinhos
CREATE TABLE IF NOT EXISTS roxinho_comissoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    roxinho_id INT NOT NULL,
    polo_id INT UNSIGNED NOT NULL,
    contrato_id INT NOT NULL COMMENT 'ID do contrato que gerou a comissão',
    valor_contrato DECIMAL(10,2) NOT NULL COMMENT 'Valor total do contrato',
    taxa_aplicada DECIMAL(5,2) NOT NULL COMMENT 'Taxa aplicada em %',
    valor_comissao_total DECIMAL(10,2) NOT NULL COMMENT 'Valor total da comissão',
    valor_pago DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Valor já pago',
    valor_pendente DECIMAL(10,2) NOT NULL COMMENT 'Valor ainda pendente',
    parcelas_total INT DEFAULT 1 COMMENT 'Total de parcelas do contrato',
    parcelas_pagas INT DEFAULT 0 COMMENT 'Parcelas já pagas pelo polo',
    status ENUM('ativo', 'finalizado', 'cancelado') DEFAULT 'ativo',
    data_contrato DATE NOT NULL,
    data_finalizacao DATE NULL,
    observacoes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_roxinho (roxinho_id),
    INDEX idx_polo (polo_id),
    INDEX idx_contrato (contrato_id),
    INDEX idx_status (status),
    INDEX idx_data_contrato (data_contrato)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de Pagamentos de Comissões
CREATE TABLE IF NOT EXISTS roxinho_pagamentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    comissao_id INT NOT NULL,
    roxinho_id INT NOT NULL,
    valor_pagamento DECIMAL(10,2) NOT NULL,
    referente_parcela INT COMMENT 'Número da parcela que gerou este pagamento',
    forma_pagamento ENUM('pix', 'transferencia', 'dinheiro', 'cheque') DEFAULT 'pix',
    data_pagamento DATE NOT NULL,
    comprovante VARCHAR(255) COMMENT 'Caminho para arquivo de comprovante',
    observacoes TEXT,
    created_by INT COMMENT 'ID do usuário que registrou o pagamento',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_comissao (comissao_id),
    INDEX idx_roxinho (roxinho_id),
    INDEX idx_data_pagamento (data_pagamento),
    INDEX idx_forma_pagamento (forma_pagamento)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de Histórico de Alterações
CREATE TABLE IF NOT EXISTS roxinho_historico (
    id INT AUTO_INCREMENT PRIMARY KEY,
    roxinho_id INT NOT NULL,
    tipo_alteracao ENUM('cadastro', 'edicao', 'vinculo_polo', 'comissao_criada', 'pagamento', 'status') NOT NULL,
    descricao TEXT NOT NULL,
    dados_anteriores JSON COMMENT 'Dados antes da alteração',
    dados_novos JSON COMMENT 'Dados após a alteração',
    usuario_id INT COMMENT 'ID do usuário que fez a alteração',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_roxinho (roxinho_id),
    INDEX idx_tipo (tipo_alteracao),
    INDEX idx_data (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- View para facilitar consultas de comissões
CREATE OR REPLACE VIEW vw_roxinho_comissoes_detalhadas AS
SELECT
    rc.*,
    r.nome as roxinho_nome,
    r.cpf as roxinho_cpf,
    r.email as roxinho_email,
    r.telefone as roxinho_telefone,
    p.nome as polo_nome,
    p.cidade as polo_cidade,
    p.endereco as polo_endereco,
    p.cnpj as polo_cnpj,
    p.status as polo_status,
    (rc.valor_comissao_total - rc.valor_pago) as valor_em_aberto,
    CASE
        WHEN rc.parcelas_total > 0 THEN (rc.parcelas_pagas / rc.parcelas_total) * 100
        ELSE 0
    END as percentual_pago,
    CASE
        WHEN rc.valor_pendente = 0 THEN 'Finalizado'
        WHEN rc.parcelas_pagas > 0 THEN 'Parcial'
        ELSE 'Pendente'
    END as status_pagamento
FROM roxinho_comissoes rc
JOIN roxinhos r ON rc.roxinho_id = r.id
JOIN polos p ON rc.polo_id = p.id;

-- View para resumo de roxinhos
CREATE OR REPLACE VIEW vw_roxinho_resumo AS
SELECT 
    r.id,
    r.nome,
    r.cpf,
    r.email,
    r.status,
    COUNT(DISTINCT rp.polo_id) as total_polos_vinculados,
    COUNT(DISTINCT rc.id) as total_comissoes,
    COALESCE(SUM(rc.valor_comissao_total), 0) as valor_total_comissoes,
    COALESCE(SUM(rc.valor_pago), 0) as valor_total_pago,
    COALESCE(SUM(rc.valor_pendente), 0) as valor_total_pendente,
    COUNT(DISTINCT CASE WHEN rc.status = 'ativo' THEN rc.id END) as comissoes_ativas
FROM roxinhos r
LEFT JOIN roxinho_polos rp ON r.id = rp.roxinho_id AND rp.status = 'ativo'
LEFT JOIN roxinho_comissoes rc ON r.id = rc.roxinho_id
GROUP BY r.id, r.nome, r.cpf, r.email, r.status;

-- Inserir dados de exemplo (opcional)
INSERT INTO roxinhos (nome, cpf, email, telefone, taxa_padrao, status) VALUES
('João Silva Indicador', '123.456.789-01', '<EMAIL>', '(11) 99999-1111', 10.00, 'ativo'),
('Maria Santos Parceira', '987.654.321-02', '<EMAIL>', '(11) 99999-2222', 15.00, 'ativo'),
('Pedro Costa Consultor', '456.789.123-03', '<EMAIL>', '(11) 99999-3333', 8.00, 'ativo');

-- Triggers para manter histórico automaticamente
DELIMITER //

CREATE TRIGGER tr_roxinho_historico_insert
AFTER INSERT ON roxinhos
FOR EACH ROW
BEGIN
    INSERT INTO roxinho_historico (roxinho_id, tipo_alteracao, descricao, dados_novos)
    VALUES (NEW.id, 'cadastro', CONCAT('Roxinho cadastrado: ', NEW.nome), JSON_OBJECT(
        'nome', NEW.nome,
        'cpf', NEW.cpf,
        'email', NEW.email,
        'taxa_padrao', NEW.taxa_padrao,
        'status', NEW.status
    ));
END//

CREATE TRIGGER tr_roxinho_historico_update
AFTER UPDATE ON roxinhos
FOR EACH ROW
BEGIN
    INSERT INTO roxinho_historico (roxinho_id, tipo_alteracao, descricao, dados_anteriores, dados_novos)
    VALUES (NEW.id, 'edicao', CONCAT('Roxinho editado: ', NEW.nome), 
        JSON_OBJECT(
            'nome', OLD.nome,
            'cpf', OLD.cpf,
            'email', OLD.email,
            'taxa_padrao', OLD.taxa_padrao,
            'status', OLD.status
        ),
        JSON_OBJECT(
            'nome', NEW.nome,
            'cpf', NEW.cpf,
            'email', NEW.email,
            'taxa_padrao', NEW.taxa_padrao,
            'status', NEW.status
        )
    );
END//

CREATE TRIGGER tr_roxinho_comissao_historico
AFTER INSERT ON roxinho_comissoes
FOR EACH ROW
BEGIN
    INSERT INTO roxinho_historico (roxinho_id, tipo_alteracao, descricao, dados_novos)
    VALUES (NEW.roxinho_id, 'comissao_criada', 
        CONCAT('Nova comissão criada - Valor: R$ ', FORMAT(NEW.valor_comissao_total, 2)),
        JSON_OBJECT(
            'contrato_id', NEW.contrato_id,
            'valor_contrato', NEW.valor_contrato,
            'taxa_aplicada', NEW.taxa_aplicada,
            'valor_comissao_total', NEW.valor_comissao_total
        )
    );
END//

CREATE TRIGGER tr_roxinho_pagamento_historico
AFTER INSERT ON roxinho_pagamentos
FOR EACH ROW
BEGIN
    INSERT INTO roxinho_historico (roxinho_id, tipo_alteracao, descricao, dados_novos)
    VALUES (NEW.roxinho_id, 'pagamento', 
        CONCAT('Pagamento registrado - Valor: R$ ', FORMAT(NEW.valor_pagamento, 2)),
        JSON_OBJECT(
            'valor_pagamento', NEW.valor_pagamento,
            'forma_pagamento', NEW.forma_pagamento,
            'data_pagamento', NEW.data_pagamento,
            'referente_parcela', NEW.referente_parcela
        )
    );
END//

DELIMITER ;

-- Índices adicionais para performance
CREATE INDEX idx_roxinho_comissoes_status_data ON roxinho_comissoes(status, data_contrato);
CREATE INDEX idx_roxinho_pagamentos_data ON roxinho_pagamentos(data_pagamento);
CREATE INDEX idx_roxinho_historico_data_tipo ON roxinho_historico(created_at, tipo_alteracao);

-- Comentários nas tabelas
ALTER TABLE roxinhos COMMENT = 'Cadastro de indicadores (Roxinhos) que recebem comissões por indicação de polos';
ALTER TABLE roxinho_polos COMMENT = 'Vínculos entre roxinhos e polos com taxas específicas';
ALTER TABLE roxinho_comissoes COMMENT = 'Comissões geradas para roxinhos baseadas em contratos de polos';
ALTER TABLE roxinho_pagamentos COMMENT = 'Histórico de pagamentos de comissões para roxinhos';
ALTER TABLE roxinho_historico COMMENT = 'Log de todas as alterações relacionadas aos roxinhos';

-- Verificação final
SELECT 'Sistema de Roxinhos instalado com sucesso!' as status;
