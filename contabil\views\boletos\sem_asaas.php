<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                Boleto Não Disponível no Asaas
            </h3>
            <div class="flex items-center space-x-2">
                <a href="boletos.php" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar
                </a>
            </div>
        </div>
    </div>

    <div class="p-6">
        <div class="text-center py-12">
            <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-yellow-100 mb-6">
                <i class="fas fa-file-pdf text-yellow-600 text-3xl"></i>
            </div>
            
            <h3 class="text-lg font-medium text-gray-900 mb-2">
                PDF do Boleto Não Disponível
            </h3>
            
            <p class="text-gray-600 mb-6 max-w-md mx-auto">
                Este boleto ainda não foi enviado para o Asaas ou não possui PDF gerado. 
                Para ter acesso ao boleto oficial, é necessário enviá-lo para o Asaas primeiro.
            </p>

            <div class="space-y-4">
                <?php if (isset($boleto) && $boleto): ?>
                    <div class="bg-gray-50 rounded-lg p-4 text-left max-w-md mx-auto">
                        <h4 class="font-medium text-gray-900 mb-2">Informações do Boleto:</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>Número:</strong> <?php echo htmlspecialchars($boleto['numero_boleto'] ?? 'Não informado'); ?></p>
                            <p><strong>Valor:</strong> R$ <?php echo number_format($boleto['valor'] ?? 0, 2, ',', '.'); ?></p>
                            <p><strong>Vencimento:</strong> <?php echo $boleto['data_vencimento'] ? date('d/m/Y', strtotime($boleto['data_vencimento'])) : 'Não informado'; ?></p>
                            <p><strong>Status:</strong> <?php echo ucfirst($boleto['status'] ?? 'Não informado'); ?></p>
                        </div>
                    </div>

                    <?php if ($boleto['status'] === 'pendente'): ?>
                        <div class="flex justify-center space-x-4">
                            <button onclick="enviarParaAsaas(<?php echo $boleto['id']; ?>)" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                                <i class="fas fa-cloud-upload-alt mr-2"></i>
                                Enviar para Asaas
                            </button>
                            
                            <a href="boletos.php?acao=visualizar&id=<?php echo $boleto['id']; ?>" 
                               class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                                <i class="fas fa-eye mr-2"></i>
                                Ver Detalhes
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function enviarParaAsaas(id) {
    if (!confirm('Deseja enviar este boleto para o Asaas?\n\nApós o envio, o PDF oficial estará disponível.')) {
        return;
    }

    // Mostrar loading
    const button = event.target;
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Enviando...';
    button.disabled = true;

    fetch('boletos.php?acao=enviar_asaas', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'id=' + id
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Boleto enviado para o Asaas com sucesso!\n\nRedirecionando para o PDF...');
            window.location.href = 'boletos.php?acao=pdf_asaas&id=' + id;
        } else {
            alert('Erro ao enviar boleto: ' + (data.message || 'Erro desconhecido'));
        }
    })
    .catch(error => {
        alert('Erro na requisição: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}
</script>
