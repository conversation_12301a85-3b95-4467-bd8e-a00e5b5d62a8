<!-- Sidebar Moderno - <PERSON><PERSON><PERSON><PERSON> -->
<div id="sidebar" class="sidebar sidebar-expanded bg-red-700 text-white flex flex-col w-64 min-h-screen border-r border-red-500 transition-all duration-300">
    <!-- Logo -->
    <div class="p-4 flex items-center justify-between bg-red-800 border-b border-red-600">
        <div class="flex items-center sidebar-logo-full">
            <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center shadow-lg">
                <i class="fas fa-calculator text-white text-xl"></i>
            </div>
            <div class="ml-3">
                <h1 class="text-white font-bold text-lg tracking-wide">Contábil</h1>
                <p class="text-red-200 text-xs">Faciência ERP</p>
            </div>
        </div>
        <!-- Botão de toggle para mobile -->
        <button id="sidebar-toggle" class="lg:hidden text-white hover:bg-red-700 p-2 rounded">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <!-- <PERSON><PERSON> de Na<PERSON>ção -->
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        <a href="dashboard.php" class="flex items-center px-4 py-3 text-red-100 hover:bg-red-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-tachometer-alt text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Dashboard</span>
        </a>
        <a href="plano_contas.php" class="flex items-center px-4 py-3 text-red-100 hover:bg-red-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-sitemap text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Plano de Contas</span>
        </a>
        <a href="plano_historico.php" class="flex items-center px-4 py-3 text-red-100 hover:bg-red-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-book text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Plano Histórico</span>
        </a>
        <a href="lancamentos.php" class="flex items-center px-4 py-3 text-red-100 hover:bg-red-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-pencil-alt text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Lançamentos</span>
        </a>
        <a href="relatorio_contabil.php" class="flex items-center px-4 py-3 text-red-100 hover:bg-red-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-file-alt text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Relatório Contábil</span>
        </a>
        <a href="bancos.php" class="flex items-center px-4 py-3 text-red-100 hover:bg-red-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-university text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Bancos</span>
        </a>
        <a href="transferencias.php" class="flex items-center px-4 py-3 text-red-100 hover:bg-red-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-exchange-alt text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Transferências</span>
        </a>
    </nav>

    <!-- Rodapé do Sidebar -->
    <div class="p-4 border-t border-red-600 bg-red-700">
        <!-- Informações do Usuário -->
        <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-white"></i>
            </div>
            <div class="ml-3 sidebar-text">
                <p class="text-sm font-medium text-white tracking-wide"><?php echo htmlspecialchars($usuario_nome ?? $usuario_email ?? 'Usuário'); ?></p>
                <p class="text-xs text-red-300">Módulo Contábil</p>
            </div>
        </div>

        <!-- Botões de Ação -->
        <div class="space-y-2">
            <a href="../secretaria/index.php" class="flex items-center px-3 py-2 text-red-100 hover:bg-red-800 rounded-lg transition-colors text-sm">
                <i class="fas fa-home mr-2"></i>
                <span class="sidebar-text">Voltar ao Sistema</span>
            </a>
            <a href="../secretaria/logout.php" class="flex items-center px-3 py-2 text-red-100 hover:bg-red-900 rounded-lg transition-colors text-sm">
                <i class="fas fa-sign-out-alt mr-2"></i>
                <span class="sidebar-text">Sair</span>
            </a>
        </div>
    </div>
</div>

<!-- Overlay para mobile -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarOverlay = document.getElementById('sidebar-overlay');

    // Toggle sidebar no mobile
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('sidebar-expanded');
            sidebarOverlay.classList.toggle('hidden');
        });
    }

    // Fechar sidebar ao clicar no overlay
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('sidebar-expanded');
            sidebarOverlay.classList.add('hidden');
        });
    }

    // Marcar item ativo baseado na URL atual
    const currentPath = window.location.pathname;
    const menuItems = document.querySelectorAll('#sidebar nav a');
    
    menuItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href.split('?')[0])) {
            item.classList.add('bg-red-700', 'text-white');
            item.classList.remove('text-red-100');
        }
    });
});
</script>
